import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Divider, List, Surface, Text } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import ChipGroup from '../../components/ui/ChipGroup';
import { useTheme } from '../../context/ThemeContext';
import { commonStyles } from '../../theme/commonStyles';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

const faqData: { id: string; question: string; answer: string; category: string }[] = [
  // ... your FAQ data remains the same
  { id: '1', question: 'How do I add a new product?', answer: 'To add a new product, go to the Products screen and tap the "+" button. Fill in the product details including name, price, category, and stock quantity. You can also add a product image for better identification.', category: 'general' },
  { id: '2', question: 'How do I create an order?', answer: 'Navigate to the Orders screen and tap the "+" button. Select the customer, add products from your inventory, set quantities, and specify delivery details. The system will automatically calculate the total.', category: 'general' },
  { id: '4', question: 'How do I track customer information?', answer: 'Go to the Customers screen to view all your customers. You can add new customers, view their order history, contact information, and preferences. This helps in providing personalized service.', category: 'general' },
  { id: '5', question: 'How do I generate reports?', answer: 'Access the Reports screen from the main menu. You can generate various reports including sales reports, inventory reports, customer analytics, and financial summaries. Reports can be exported or shared.', category: 'features' },
  { id: '6', question: 'What payment methods are supported?', answer: 'The app supports multiple payment methods including cash, credit cards, mobile payments, and bank transfers. You can configure payment methods in the Settings under Payment Methods.', category: 'billing' },
  { id: '7-1', question: 'How do I backup my data?', answer: 'Your data is automatically backed up to the cloud. You can also manually backup data from Settings > Auto Backup. This ensures your business data is safe and can be restored if needed.', category: 'technical' },
  { id: '8', question: 'Can I customize the app theme?', answer: 'Yes! Go to Settings and toggle the Dark Mode option to switch between light and dark themes. The app will remember your preference and apply it across all screens.', category: 'features' },
  { id: '9', question: 'How do I manage staff accounts?', answer: 'Access Staff Management from Settings to add, edit, or remove staff accounts. You can assign different roles and permissions to control what each staff member can access.', category: 'technical' },
  { id: '10', question: 'What if I forget my password?', answer: "If you forget your password, use the 'Forgot Password' option on the login screen. You'll receive a reset link via email. For additional security, contact our support team.", category: 'technical' },
];

const FAQListItem = React.memo(
  ({ item, isExpanded, onToggle, theme, cardStyle }: { item: typeof faqData[0]; isExpanded: boolean; onToggle: () => void; theme: ReturnType<typeof useTheme>; cardStyle: object; }) => {
    return (
      <Surface style={[cardStyle, styles.faqCard, isExpanded && [styles.expandedCard, { borderColor: theme.colors.outline }]]}>
        <List.Accordion
          title={item.question}
          expanded={isExpanded}
          onPress={onToggle}
          style={styles.accordion}
          titleStyle={[styles.questionText, { color: theme.colors.onSurface }]}
        >
          <Divider style={{ marginHorizontal: SPACING.lg }} />
          <View style={styles.answerContainer}>
            <Text variant="bodyMedium" style={[styles.answerText, { color: theme.colors.onSurfaceVariant }]}>
              {item.answer}
            </Text>
          </View>
        </List.Accordion>
      </Surface>
    );
  }
);

const HelpFAQScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const cardStyle = {
    backgroundColor: theme.colors.surface,
    borderRadius: BORDER_RADIUS.lg,
  };

  const filteredFAQ = useMemo(
    () => (selectedCategory === 'all' ? faqData : faqData.filter(item => item.category === selectedCategory)),
    [selectedCategory]
  );

  const toggleExpanded = useCallback((id: string) => {
    setExpandedId(currentId => (currentId === id ? null : id));
  }, []);

  const handleGoBack = useCallback(() => navigation.goBack(), [navigation]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Help & FAQ" onBackPress={handleGoBack} showBack />
      <ScrollView style={commonStyles.flex1} showsVerticalScrollIndicator={false}>
        <Surface style={[styles.header, cardStyle]}>
          <View style={styles.headerContent}>
            <PhosphorIcon name="question" size={48} color={theme.colors.primary} />
            <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
              Help & FAQ
            </Text>
            <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
              Find answers to common questions about using TailorZa
            </Text>
          </View>
        </Surface>

        <View style={styles.categoryContainer}>
          <ChipGroup
            selectedFilter={selectedCategory}
            onFilterChange={filter => setSelectedCategory(String(filter))}
            chipSize="medium"
            borderRadius="full-rounded"
          />
        </View>

        <View style={styles.faqContainer}>
          {filteredFAQ.map(item => (
            <FAQListItem
              key={item.id}
              item={item}
              isExpanded={expandedId === item.id}
              onToggle={() => toggleExpanded(item.id)}
              theme={theme}
              cardStyle={cardStyle} // --- CHANGE: Pass the shared style to the list item
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    marginBottom: SPACING.lg,
    marginHorizontal: SPACING.lg,
    marginTop: SPACING.lg,
  },
  headerContent: { padding: SPACING.xl, alignItems: 'center' },
  title: { marginTop: SPACING.md, marginBottom: SPACING.sm, fontWeight: '700' },
  subtitle: { textAlign: 'center', lineHeight: 20 },
  categoryContainer: { marginBottom: SPACING.lg, paddingHorizontal: SPACING.lg },
  faqContainer: { paddingHorizontal: SPACING.lg },
  faqCard: {
    marginBottom: SPACING.md,
    overflow: 'hidden',
  },
  accordion: { padding: SPACING.md },
  questionText: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: '600' },
  answerContainer: { padding: SPACING.lg },
  answerText: { lineHeight: 22 },
  expandedCard: {
    borderWidth: 1.5,
  },
});

export default HelpFAQScreen;