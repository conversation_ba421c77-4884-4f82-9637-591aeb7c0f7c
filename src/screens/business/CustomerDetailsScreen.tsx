import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useCallback, useMemo, useState } from 'react';
import { Linking, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import OrderCard from '../../components/cards/OrderCard';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import EmptyState from '../../components/ui/EmptyState';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { cleanPhoneNumber, formatPhoneNumberForDisplay } from '../../utils/phoneUtils';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const CustomerDetailsScreen = ({ route }: { route: any }) => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { state: dataState, actions: dataActions } = useData();
  const { showSuccess, showError } = useToast();
  
  const { customerId } = route.params;

  const [refreshing, setRefreshing] = useState(false);
  const [actionSheetVisible, setActionSheetVisible] = useState(false);

  // Use `useMemo` to declaratively find the latest data from the global state.
  const { customer, customerOrders, customerMeasurements } = useMemo(() => {
    const foundCustomer = dataState.customers?.find(c => c.id === customerId);
    if (!foundCustomer) {
      return { customer: null, customerOrders: [], customerMeasurements: [] };
    }

    const orders = (dataState.orders || [])
      .filter(order => String(order.customerId) === String(foundCustomer.id))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // ✅ RESTORED: Logic to find and sort measurements for the customer.
    const measurements = (dataState.measurements || [])
      .filter((m: any) => String(m.customerId) === String(foundCustomer.id))
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return { customer: foundCustomer, customerOrders: orders, customerMeasurements: measurements };
  }, [customerId, dataState.customers, dataState.orders, dataState.measurements]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await dataActions.reloadData();
    setRefreshing(false);
  }, []);

  if (!customer) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Customer Profile" showBack onBackPress={() => navigation.goBack()} />
        <EmptyState type="custom" icon="question" title="Customer Not Found" description="This customer may have been deleted." />
      </View>
    );
  }

  const { name, phone, email, address, createdAt } = customer;

  const totalAmount = useMemo(
    () => customerOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0),
    [customerOrders]
  );
  
  const lastOrderDate = useMemo(() => {
    if (customerOrders.length === 0) return 'No orders yet';
    const lastOrder = customerOrders[0];
    const dateDiffDays = (new Date().getTime() - new Date(lastOrder.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    if (dateDiffDays < 1) return 'Today';
    if (dateDiffDays < 30) return `${Math.floor(dateDiffDays)} days ago`;
    if (dateDiffDays < 365) return `${Math.floor(dateDiffDays / 30)} months ago`;
    return `${Math.floor(dateDiffDays / 365)} years ago`;
  }, [customerOrders]);

  const handleCall = () => phone && Linking.openURL(`tel:${cleanPhoneNumber(phone)}`);
  const handleWhatsApp = () => phone && Linking.openURL(`whatsapp://send?phone=${cleanPhoneNumber(phone)}`);
  const handleEdit = () => navigation.navigate('AddCustomer', { isEditing: true, customerData: customer });
  
  const handleDeleteConfirm = async () => {
    try {
      await dataActions.deleteCustomer(customer.id);
      showSuccess('Customer deleted successfully.');
      navigation.goBack();
    } catch (error) {
      showError('Failed to delete customer.');
    } finally {
      setActionSheetVisible(false);
    }
  };

  const formatCurrency = (amount: number) => `${Math.round(amount || 0).toLocaleString()}`;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Customer Profile'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          { icon: 'pencil', onPress: handleEdit },
          { icon: 'trash', onPress: () => setActionSheetVisible(true) },
        ]}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]} />}
      >
        <LinearGradient colors={['#26313a', '#1a232b']} style={styles.profileCard}>
          <View style={styles.profileRow}>
            <View style={styles.profileMain}>
              <View style={styles.nameRow}>
                <Text style={styles.name}>{name}</Text>
              </View>
              <Text style={styles.phone}>{phone ? formatPhoneNumberForDisplay(phone) : 'No phone number'}</Text>
              <View style={styles.metaInfoRow}>
                <Text style={styles.metaInfo}>Last order: {lastOrderDate}</Text>
                <Text style={styles.metaInfo}>
                  Member since: {new Date(createdAt).toLocaleDateString('en-GB', { month: 'short', year: 'numeric' })}
                </Text>
              </View>
            </View>
            <View style={styles.profileActions}>
              <Button icon='chat-circle' onPress={handleWhatsApp} variant='ghost' textColor='#25D366' />
              <Button icon='phone' onPress={handleCall} variant='ghost' textColor='#60a5fa' />
            </View>
          </View>
        </LinearGradient>

        <View style={styles.statsRow}>
          <View style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]}>
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>{customerOrders.length}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Total orders</Text>
          </View>
          <View style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]}>
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>{formatCurrency(totalAmount)}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Total spent</Text>
          </View>
          {/* ✅ RESTORED: Measurements stat card */}
          <View style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]}>
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>{customerMeasurements.length}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Measurements</Text>
          </View>
        </View>
        
        {(email || address) && (
            <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
                <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>Information</Text>
                {email && (
                    <View style={styles.infoRow}>
                        <PhosphorIcon name='envelope' size={20} color='#60a5fa' style={styles.infoIcon} />
                        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>{email}</Text>
                    </View>
                )}
                {address && (
                    <View style={styles.infoRow}>
                        <PhosphorIcon name='map-pin' size={20} color='#60a5fa' style={styles.infoIcon} />
                        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>{address}</Text>
                    </View>
                )}
            </View>
        )}

        <View style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeaderRow}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Recent Orders</Text>
            <Button onPress={() => navigation.navigate('CustomerOrders', { customerId: customer.id })} variant='ghost'>
              See all
            </Button>
          </View>
          {customerOrders.length === 0 ? (
            <EmptyState
              type='custom'
              title="No Orders Found"
              icon="receipt"
              onActionPress={() => navigation.navigate('AddOrder', { customer })}
              description='No recent orders found for this customer.'
              actionLabel='Create Order'
            />
          ) : (
            customerOrders.slice(0, 3).map(order => (
              <OrderCard key={order.id} order={order} onPress={() => navigation.navigate('OrderDetails', { orderId: order.id })} />
            ))
          )}
        </View>
        
        {/* ✅ RESTORED: Entire "Recent Measurements" section */}
        <View style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeaderRow}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Recent Measurements</Text>
            <Button onPress={() => {}} variant='ghost'> 
              See all
            </Button>
          </View>
          {customerMeasurements.length === 0 ? (
            <EmptyState
              type='custom'
              title="No Measurements Found"
              icon="ruler"
              onActionPress={() => navigation.navigate('AddMeasurement', { customer })}
              description='No measurements found for this customer.'
              actionLabel='Add Measurement'
            />
          ) : (
            customerMeasurements.slice(0, 2).map((measurement: any) => (
              <View key={measurement.id} style={[styles.measurementCard, {backgroundColor: theme.colors.surfaceVariant}]}>
                <View style={styles.measurementCardHeader}>
                  <Text style={[styles.measurementTitle, {color: theme.colors.onSurfaceVariant}]}>{measurement.garmentType || 'General'}</Text>
                  <Text style={[styles.measurementDate, {color: theme.colors.onSurfaceVariant}]}>
                      {new Date(measurement.createdAt).toLocaleDateString('en-GB', { day: 'numeric', month: 'short' })}
                  </Text>
                </View>
                <View style={styles.measurementGrid}>
                  {(() => {
                    let measurements = measurement.measurements;
                    if (typeof measurements === 'string') {
                      try { measurements = JSON.parse(measurements); } catch (e) { measurements = {}; }
                    }
                    if (measurements && typeof measurements === 'object' && !Array.isArray(measurements)) {
                      return Object.entries(measurements as Record<string, number>)
                        .filter(([, value]) => value != null)
                        .map(([key, value]) => (
                          <View key={key} style={styles.measurementField}>
                              <Text style={[styles.measurementLabel, {color: theme.colors.onSurfaceVariant}]}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
                              <Text style={[styles.measurementValue, {color: theme.colors.onSurface}]}>{value}"</Text>
                          </View>
                        ));
                    }
                    return null;
                  })()}
                </View>
              </View>
            ))
          )}
        </View>

      </ScrollView>

      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete ${name}?`}
        description="Are you sure you want to delete this customer? This action cannot be undone."
        options={[
          { text: 'Delete', onPress: handleDeleteConfirm, style: 'destructive' },
          { text: 'Cancel', onPress: () => setActionSheetVisible(false), style: 'cancel' },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    scrollContent: { paddingBottom: 32, paddingHorizontal: 16 },
    profileCard: { borderRadius: 18, marginTop: 16, marginBottom: 16, padding: 18, elevation: 4 },
    profileRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
    profileMain: { flex: 1 },
    nameRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 2 },
    name: { fontSize: 24, fontWeight: 'bold', color: 'white' },
    phone: { fontSize: 16, color: '#60a5fa', marginBottom: 8, marginTop: 4 },
    profileActions: { flexDirection: 'row' },
    metaInfoRow: { flexDirection: 'row', justifyContent: 'flex-start', marginTop: 8, gap: 16 },
    metaInfo: { fontSize: 13, color: 'white', opacity: 0.8 },
    statsRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 16, gap: 8 },
    statCard: { flex: 1, borderRadius: 12, alignItems: 'center', paddingVertical: 16 },
    statValue: { fontSize: 20, fontWeight: 'bold' },
    statLabel: { fontSize: 14, marginTop: 4 },
    infoCard: { borderRadius: 16, padding: 18, marginBottom: 16 },
    infoTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 12 },
    infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10 },
    infoIcon: { marginRight: 12 },
    infoText: { fontSize: 15 },
    sectionCard: { borderRadius: 16, paddingVertical: 16, marginBottom: 16, elevation: 2 },
    sectionHeaderRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8, paddingHorizontal: 16 },
    sectionTitle: { fontSize: 18, fontWeight: 'bold' },
    measurementCard: { borderRadius: 12, padding: 14, marginBottom: 12 },
    measurementCardHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
    measurementTitle: { fontSize: 16, fontWeight: 'bold' },
    measurementDate: { fontSize: 13 },
    measurementGrid: { flexDirection: 'row', flexWrap: 'wrap' },
    measurementField: { width: '50%', paddingRight: 8, marginBottom: 10 },
    measurementLabel: { fontSize: 13, opacity: 0.8, marginBottom: 2 },
    measurementValue: { fontSize: 15, fontWeight: '600' },
});

export default CustomerDetailsScreen;