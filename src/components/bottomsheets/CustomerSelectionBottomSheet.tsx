import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlatList, // 👈 Correct import for FlatList
} from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ListRenderItem, // FlatList is no longer imported from here
  StyleSheet,
  TextInput as RNTextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Text } from 'react-native-paper';

import { DataCard } from '@/components/cards';
import Button from '@/components/ui/Button';
import ChipGroup from '@/components/ui/ChipGroup';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

// --- Types & Hooks ---
interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  totalOrders?: number;
  totalSpent?: number;
  lastOrderDate?: string;
  isActive?: boolean;
  createdAt?: string;
}

interface CustomerSelectionBottomSheetProps {
  onSelect: (customer: Customer) => void;
  onClose: () => void;
}

export interface CustomerSelectionBottomSheetRef {
  open: () => void;
  close: () => void;
}

const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};

// --- Component ---
const CustomerSelectionBottomSheet = forwardRef<
  CustomerSelectionBottomSheetRef,
  CustomerSelectionBottomSheetProps
>(({ onSelect, onClose }, ref) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { state } = useData();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('All');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const snapPoints = useMemo(() => ['80%'], []);
  const allCustomers = useMemo(() => state.customers || [], [state.customers]);

  const filteredCustomers = useMemo(() => {
    let filtered = allCustomers;
    const searchLower = debouncedSearchQuery.trim().toLowerCase();

    if (searchLower) {
      filtered = filtered.filter(
        c =>
          c.name.toLowerCase().includes(searchLower) ||
          c.email?.toLowerCase().includes(searchLower) ||
          c.phone?.includes(searchLower)
      );
    }

    if (selectedFilter !== 'All') {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      filtered = filtered.filter(customer => {
        switch (selectedFilter) {
          case 'Active':
            return (
              customer.isActive === true ||
              (customer.lastOrderDate && new Date(customer.lastOrderDate) > thirtyDaysAgo)
            );
          case 'VIP':
            return (customer.totalSpent || 0) > 15000;
          case 'New':
            return (
              (customer.totalOrders || 0) <= 3 ||
              (customer.createdAt && new Date(customer.createdAt) > thirtyDaysAgo)
            );
          default:
            return true;
        }
      });
    }
    return filtered;
  }, [allCustomers, debouncedSearchQuery, selectedFilter]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) onClose();
    },
    [onClose]
  );

  useImperativeHandle(
    ref,
    () => ({
      open: () => bottomSheetRef.current?.snapToIndex(0),
      close: () => bottomSheetRef.current?.close(),
    }),
    []
  );

  const handleFilterChange = useCallback((filter: string | number) => {
    setSelectedFilter(String(filter));
  }, []);

  const handleCustomerSelect = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
  }, []);

  const handleConfirmSelection = useCallback(() => {
    if (selectedCustomer) {
      onSelect(selectedCustomer);
      bottomSheetRef.current?.close();
    }
  }, [selectedCustomer, onSelect]);

  const renderCustomerItem: ListRenderItem<Customer> = useCallback(
    ({ item }) => (
      <DataCard
        {...item}
        lastOrderDate={item.lastOrderDate ? new Date(item.lastOrderDate) : null}
        cardType="customer"
        onPress={() => handleCustomerSelect(item)}
        onCheckboxPress={() => handleCustomerSelect(item)}
        selected={selectedCustomer?.id === item.id}
        showSelectionIndicator
        isInBottomSheet
      />
    ),
    [handleCustomerSelect, selectedCustomer]
  );

  const keyExtractor = useCallback((item: Customer) => item.id, []);

  const ListEmptyComponent = useMemo(
    () => (
      <View style={styles.noResultsContainer}>
        <Text style={styles.noResultsText}>No Customers Found</Text>
      </View>
    ),
    [styles.noResultsContainer, styles.noResultsText]
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => <BottomSheetBackdrop {...props} pressBehavior="close" />,
    []
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={renderBackdrop}
      enablePanDownToClose={false}
      handleStyle={styles.handleStyle}
      handleIndicatorStyle={styles.handleIndicator}
      backgroundStyle={styles.background}
      keyboardBehavior="extend"
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Select Customer</Text>
        <TouchableOpacity style={styles.closeButton} onPress={() => bottomSheetRef.current?.close()}>
          <PhosphorIcon name="x" size={20} color={theme.colors.onSurface} />
        </TouchableOpacity>
      </View>

      {/* Fixed Search and Filter Section */}
      <View style={styles.fixedSection}>
        <View style={styles.searchInputWrapper}>
          <PhosphorIcon
            name="magnifying-glass"
            size={20}
            color={theme.colors.onSurfaceVariant}
            style={styles.searchIcon}
          />
          <RNTextInput
            style={styles.searchInput}
            placeholder="Search customers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />
          {searchQuery ? (
            <TouchableOpacity style={styles.clearButton} onPress={() => setSearchQuery('')}>
              <PhosphorIcon name="x" size={20} color={theme.colors.onSurfaceVariant} />
            </TouchableOpacity>
          ) : null}
        </View>
        <ChipGroup
          filters={['All', 'Active', 'VIP', 'New']}
          selectedFilter={selectedFilter}
          onFilterChange={handleFilterChange}
          style={styles.filterChipsContainer}
          borderRadius="full-rounded"
        />
      </View>

      {/* 👈 Correct component for scrolling */}
      <BottomSheetFlatList
        data={filteredCustomers}
        renderItem={renderCustomerItem}
        keyExtractor={keyExtractor}
        ListEmptyComponent={ListEmptyComponent}
        contentContainerStyle={styles.scrollableContentContainer}
        keyboardShouldPersistTaps="handled"
        // Performance Props
        removeClippedSubviews
        maxToRenderPerBatch={10}
        windowSize={11}
        initialNumToRender={10}
        getItemLayout={(_, index) => ({
          length: 88, // Height of DataCard
          offset: 88 * index,
          index,
        })}
      />

      {/* Fixed Footer */}
      <View style={styles.actionButtonsContainer}>
        <Button
          variant="ghost"
          onPress={() => bottomSheetRef.current?.close()}
          style={styles.button}
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onPress={handleConfirmSelection}
          style={styles.button}
          disabled={!selectedCustomer}
        >
          Select
        </Button>
      </View>
    </BottomSheet>
  );
});

// --- Styles ---
const createStyles = (theme: any) =>
  StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
      backgroundColor: theme.colors.background,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.onSurface,
    },
    closeButton: { padding: theme.spacing.xs },
    fixedSection: {
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.sm,
      paddingBottom: theme.spacing.xs,
      backgroundColor: theme.colors.background,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    scrollableContentContainer: {
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
    },
    searchInputWrapper: {
      position: 'relative',
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    searchInput: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: theme.borderRadius.lg,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      paddingLeft: 44,
      paddingRight: 44,
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.onSurface,
      flex: 1,
    },
    searchIcon: { position: 'absolute', left: theme.spacing.md, zIndex: 1 },
    clearButton: { position: 'absolute', right: theme.spacing.md, zIndex: 1 },
    filterChipsContainer: { paddingBottom: 0, gap: theme.spacing.xs },
    noResultsContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.xl,
      minHeight: 200,
    },
    noResultsText: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    actionButtonsContainer: {
      flexDirection: 'row',
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.background,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outlineVariant,
      gap: theme.spacing.md,
    },
    button: { flex: 1 },
    handleStyle: { paddingTop: theme.spacing.xs, paddingBottom: theme.spacing.xs },
    handleIndicator: { backgroundColor: theme.colors.outline },
    background: { backgroundColor: theme.colors.background },
  });

export default CustomerSelectionBottomSheet;