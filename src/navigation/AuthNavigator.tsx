import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { useTheme } from '../context/ThemeContext';
import LoginScreen from '../screens/auth/LoginScreen';
import LoggingService from '../services/LoggingService';

export type AuthStackParamList = {
  Login: undefined;
};

const Stack = createStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  const theme = useTheme();

  // Debug logging
  LoggingService.debug('AuthNavigator rendered, showing unified login screen', 'NAV');

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        cardStyleInterpolator: () => ({
          cardStyle: {
            transform: [],
            opacity: 1,
          },
          overlayStyle: {
            opacity: 0,
          },
        }),
      }}
      initialRouteName='Login'
    >
      <Stack.Screen
        name='Login'
        component={LoginScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
