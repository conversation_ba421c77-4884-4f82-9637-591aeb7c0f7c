import { useRoute } from '@react-navigation/native';
import React, { useMemo, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { Avatar } from 'react-native-paper';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const TAB_CONFIG = [
  { name: 'Home', icon: 'house', label: 'Home', route: 'Home', isAddButton: false },
  { name: 'Orders', icon: 'receipt', label: 'Orders', route: 'Orders', isAddButton: false },
  { name: 'Add', icon: 'plus', label: '', route: 'Add', isAddButton: true },
  { name: 'Inventory', icon: 'package', label: 'Inventory', route: 'Inventory', isAddButton: false },
  { name: 'Profile', icon: 'user', label: 'Profile', route: 'Profile', isAddButton: false },
] as const;

type TabItem = (typeof TAB_CONFIG)[number];

interface NavBarProps {
  navigation?: any;
}

const NavBar: React.FC<NavBarProps> = ({ navigation }) => {
  const route = useRoute();
  const theme = useTheme();
  const { state: dataState } = useData();
  const currentRoute = route.name;

  // Memoize profile data and initials together
  const profileInfo = useMemo(() => {
    const profileImage = (dataState.settings as any)?.profileImage;
    const storeName = dataState.settings?.storeName;
    const initials = storeName
      ? storeName.split(' ').map((word: string) => word[0]).join('').substring(0, 2)
      : 'SD';
    
    return { profileImage, storeName, initials };
  }, [dataState.settings]);

  // Pre-calculate all theme colors once
  const colors = useMemo(() => ({
    primary: theme.colors.primary,
    primaryContainer: theme.colors.primaryContainer,
    onPrimary: theme.colors.onPrimary,
    onSecondaryContainer: theme.colors.onSecondaryContainer,
    onSurface: theme.colors.onSurface,
    outline: theme.colors.outline,
    surface: theme.colors.surface,
  }), [theme.colors]);

  // Memoize styles with colors
  const styles = useMemo(() => createStyles(theme, colors), [theme, colors]);

  const handleTabPress = useCallback((tab: TabItem) => {
    if (!navigation) return;
    
    if (tab.isAddButton) {
      navigation.navigate('AddOrder');
    } else if (currentRoute !== tab.route) {
      navigation.navigate(tab.route);
    }
  }, [navigation, currentRoute]);

  // Memoize the profile avatar component
  const ProfileAvatar = useMemo(() => {
    if (profileInfo.profileImage) {
      return (
        <Avatar.Image
          size={28}
          source={{ uri: profileInfo.profileImage }}
          style={styles.profileAvatar}
        />
      );
    }
    return (
      <Avatar.Text
        size={28}
        label={profileInfo.initials}
        style={[styles.profileAvatar, { backgroundColor: colors.outline }]}
      />
    );
  }, [profileInfo.profileImage, profileInfo.initials, styles.profileAvatar, colors.outline]);

  const renderTab = useCallback((tab: TabItem) => {
    const isActive = currentRoute === tab.name;
    const iconSize = isActive ? 28 : 24;
    
    // Pre-calculate styles and colors
    const iconColor = tab.isAddButton
      ? colors.onSecondaryContainer
      : isActive ? colors.onPrimary : colors.onSurface;
    
    const textColor = isActive ? colors.primary : colors.onSurface;
    
    const iconContainerStyle = [
      styles.iconContainer,
      tab.isAddButton && [styles.addButtonContainer, { backgroundColor: colors.primary }],
      isActive && !tab.isAddButton && [styles.activeIconContainer, { backgroundColor: colors.primaryContainer }],
    ];

    return (
      <TouchableOpacity
        key={tab.name}
        style={[styles.tab, tab.isAddButton && styles.addTab]}
        onPress={() => handleTabPress(tab)}
        activeOpacity={0.7}
      >
        <View style={iconContainerStyle}>
          {tab.name === 'Profile' ? (
            ProfileAvatar
          ) : (
            <PhosphorIcon name={tab.icon} size={iconSize} color={iconColor} />
          )}
        </View>
        {tab.label && (
          <Text style={[styles.label, { color: textColor }]}>
            {tab.label}
          </Text>
        )}
      </TouchableOpacity>
    );
  }, [currentRoute, colors, styles, handleTabPress, ProfileAvatar]);

  return (
    <View style={[styles.container, { backgroundColor: colors.surface }]}>
      {TAB_CONFIG.map(renderTab)}
    </View>
  );
};

const createStyles = (theme: any, colors: any) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      height: 60,
      paddingVertical: 8,
      paddingHorizontal: 12,
      elevation: 8,
      shadowColor: colors.onSurface,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      zIndex: 100,
    },
    tab: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addTab: {
      justifyContent: 'flex-start',
      paddingBottom: 0,
    },
    iconContainer: {
      width: 28,
      height: 28,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 0,
    },
    addButtonContainer: {
      width: 40,
      height: 40,
      borderRadius: 28,
      elevation: 2,
      shadowColor: colors.onSurface,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    activeIconContainer: {
      width: 36,
      height: 36,
      borderRadius: 18,
      elevation: 4,
      shadowColor: colors.onSurface,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    label: {
      fontSize: TYPOGRAPHY.fontSize.xs,
      fontWeight: TYPOGRAPHY.fontWeight.normal,
      textAlign: 'center',
      marginTop: 2,
    },
    profileAvatar: {
      borderRadius: 14,
    },
  });

export default React.memo(NavBar);