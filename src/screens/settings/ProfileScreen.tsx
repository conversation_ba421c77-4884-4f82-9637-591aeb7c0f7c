import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Avatar, Text } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import Switch from '../../components/ui/Switch';
import { useCurrentUser, useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { usePermissions } from '../../hooks';
import LoggingService from '../../services/LoggingService';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

interface ProfileItem {
  icon: PhosphorIconName;
  label: string;
  onPress: () => void;
  rightComponent?: React.ReactNode;
}

const MyProfileScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { state, actions } = useData();

  const currentUser = useCurrentUser();
  const theme = useTheme();
  const { isDarkMode, toggleTheme } = theme;

  // State for settings
  const [notifications, setNotifications] = useState(true);

  const { logout } = useAuth();
  const { hasRole } = usePermissions();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleSettingChange = (key: string, value: boolean): void => {
    const newSettings = { [key]: value };
    actions.updateSettings(newSettings);
    if (key === 'notifications') setNotifications(value);
  };

  const handleLogout = async (): Promise<void> => {
    setIsLoggingOut(true);
    try {
      await logout();
      LoggingService.info('User logged out successfully', 'AUTH');
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH', error as Error);
      // Optionally, show an alert to the user
      Alert.alert('Logout Failed', 'An error occurred during logout. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const ProfileHeader: React.FC = () => (
    <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={styles.avatarContainer}>
              {(state.settings as any)?.profileImage ? (
                <Avatar.Image size={56} source={{ uri: (state.settings as any)?.profileImage }} />
              ) : (
                <Avatar.Text
                  size={56}
                  label={state.settings.storeName
                    .split(' ')
                    .map(word => word[0])
                    .join('')
                    .substring(0, 2)}
                />
              )}
            </View>
            <View style={styles.profileInfo}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Text
                  style={{
                    fontWeight: '700',
                    color: theme.colors.onSurface,
                    fontSize: TYPOGRAPHY.fontSize.xl,
                    lineHeight: TYPOGRAPHY.lineHeight.tight * TYPOGRAPHY.fontSize.xl,
                  }}
                >
                  {state.settings.storeName}
                </Text>
                <View
                  style={{
                    backgroundColor: `${theme.colors.primary}22`,
                    paddingHorizontal: 8,
                    paddingVertical: 2,
                    borderRadius: BORDER_RADIUS.sm,
                    marginLeft: 4,
                  }}
                >
                  <Text style={{ color: theme.colors.primary, fontWeight: '600', fontSize: 12 }}>
                    {currentUser?.role || 'Owner'}
                  </Text>
                </View>
              </View>
              <Text
                style={{
                  color: theme.colors.onVariant,
                  marginTop: 2,
                  fontSize: TYPOGRAPHY.fontSize.sm,
                  fontWeight: TYPOGRAPHY.fontWeight.normal,
                }}
              >
                {state.settings.phone || 'No phone provided'}
              </Text>
              <Text
                style={{
                  color: theme.colors.onVariant,
                  marginTop: 2,
                  fontSize: TYPOGRAPHY.fontSize.sm,
                  fontWeight: TYPOGRAPHY.fontWeight.normal,
                }}
              >
                {state.settings.email || 'No email provided'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={() =>
              (navigation as any).navigate('EditProfile', { profileData: state.settings })
            }
            style={[styles.editButton, { backgroundColor: `${theme.colors.primary}10` }]}
          >
            <Text
              style={{
                color: theme.colors.primary,
                fontWeight: '600',
                fontSize: TYPOGRAPHY.fontSize.md,
              }}
            >
              Edit
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Define all section items for ProfileSectionList
  const appSettingsItems: ProfileItem[] = [
    {
      icon: 'moon',
      label: 'Dark Mode',
      rightComponent: <Switch value={isDarkMode} onValueChange={toggleTheme} style={{}} />,
      onPress: toggleTheme,
    },
    {
      icon: 'bell',
      label: 'Notifications',
      onPress: () => handleSettingChange('notifications', !notifications),
      rightComponent: (
        <Switch
          value={notifications}
          onValueChange={(value: boolean) => handleSettingChange('notifications', value)}
          style={{}}
        />
      ),
    },
  ];

  const preferencesItems: ProfileItem[] = [
    {
      icon: 'credit-card' as PhosphorIconName,
      label: 'Payment Methods',
      onPress: () => {
        try {
          navigation.navigate('PaymentMethods');
        } catch (error) {
          LoggingService.error(
            'Failed to navigate to PaymentMethods',
            'NAVIGATION',
            error as Error
          );
        }
      },
    },
    {
      icon: 't-shirt' as PhosphorIconName,
      label: 'Garment Types',
      onPress: () => {
        try {
          navigation.navigate('GarmentTypes');
        } catch (error) {
          LoggingService.error('Failed to navigate to GarmentTypes', 'NAVIGATION', error as Error);
        }
      },
    },
    
    ...(hasRole('manager')
      ? [
          {
            icon: 'users' as PhosphorIconName,
            label: 'Staff Management',
            onPress: () => {
              try {
                navigation.navigate('StaffManagement');
              } catch (error) {
                LoggingService.error(
                  'Failed to navigate to StaffManagement',
                  'NAVIGATION',
                  error as Error
                );
              }
            },
          },
        ]
      : []),
    ...(hasRole('manager')
      ? [
          {
            icon: 'database' as PhosphorIconName,
            label: 'Data Management',
            onPress: () => {
              try {
                navigation.navigate('DataManagement');
              } catch (error) {
                LoggingService.error(
                  'Failed to navigate to DataManagement',
                  'NAVIGATION',
                  error as Error
                );
              }
            },
          },
        ]
      : []),

    {
      icon: 'clipboard-text' as PhosphorIconName,
      label: 'Activity Log',
      onPress: () => {
        try {
          navigation.navigate('ActivityLog');
        } catch (error) {
          LoggingService.error('Failed to navigate to ActivityLog', 'NAVIGATION', error as Error);
        }
      },
    },
  ];

  // Support and Help items
  const supportItems: ProfileItem[] = [
    {
      icon: 'question' as PhosphorIconName,
      label: 'Help & FAQ',
      onPress: () => {
        try {
          navigation.navigate('HelpFAQ');
        } catch (error) {
          LoggingService.error('Failed to navigate to HelpFAQ', 'NAVIGATION', error as Error);
        }
      },
    },
    {
      icon: 'phone' as PhosphorIconName,
      label: 'Contact Support',
      onPress: () => {
        try {
          navigation.navigate('ContactSupport');
        } catch (error) {
          LoggingService.error(
            'Failed to navigate to ContactSupport',
            'NAVIGATION',
            error as Error
          );
        }
      },
    },
    {
      icon: 'info' as PhosphorIconName,
      label: 'About',
      onPress: () => {
        try {
          navigation.navigate('About');
        } catch (error) {
          LoggingService.error('Failed to navigate to About', 'NAVIGATION', error as Error);
        }
      },
    },
    {
      icon: 'info' as PhosphorIconName, // Using 'info' icon, as it's already imported
      label: 'App Status',
      onPress: () => {
        try {
          navigation.navigate('AppStatus');
        } catch (error) {
          LoggingService.error('Failed to navigate to AppStatus', 'NAVIGATION', error as Error);
        }
      },
    },
  ];

  // Logout item
  const logoutItem: ProfileItem[] = [
    {
      icon: 'sign-out' as PhosphorIconName,
      label: 'Logout',
      onPress: handleLogout,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
            <Header title='My Profile' onBackPress={() => navigation.goBack()} showBack={true} />

      <ScrollView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
        contentContainerStyle={{
          padding: 0,
          margin: 0,
          paddingTop: 4,
          paddingBottom: 4,
          flexGrow: 1,
          justifyContent: 'flex-start',
        }}
        showsVerticalScrollIndicator={false}
      >
        <ProfileHeader />

        {/* App Preferences Section */}
        <View
          style={[
            styles.section,
            { backgroundColor: theme.colors.surface, shadowColor: '#000', marginTop: 4 },
          ]}
        >
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>
            App Preferences
          </Text>
          {appSettingsItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                {
                  borderBottomColor:
                    idx === appSettingsItems.length - 1
                      ? 'transparent'
                      : `${theme.colors.outline}22`,
                },
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon
                name={item.icon}
                size={24}
                color={theme.colors.onVariant}
                weight='regular'
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>{item.label}</Text>
              {item.rightComponent ? (
                item.rightComponent
              ) : (
                <PhosphorIcon
                  name='chevron-right'
                  size={24}
                  color={theme.colors.onVariant}
                  weight='regular'
                  style={styles.sectionChevron}
                />
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Profile Preferences Section */}
        <View
          style={[
            styles.section,
            { backgroundColor: theme.colors.surface, shadowColor: '#000', marginTop: 4 },
          ]}
        >
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>
            Profile Preferences
          </Text>
          {preferencesItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                {
                  borderBottomColor:
                    idx === preferencesItems.length - 1
                      ? 'transparent'
                      : `${theme.colors.outline}22`,
                },
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon
                name={item.icon}
                size={24}
                color={theme.colors.onVariant}
                weight='regular'
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>{item.label}</Text>
              {item.rightComponent ? (
                item.rightComponent
              ) : (
                <PhosphorIcon
                  name='chevron-right'
                  size={24}
                  color={theme.colors.onVariant}
                  weight='regular'
                  style={styles.sectionChevron}
                />
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Support & Help Section */}
        <View
          style={[
            styles.section,
            {
              backgroundColor: theme.colors.surface,
              shadowColor: '#000',
              marginTop: 4,
              marginBottom: 4,
            },
          ]}
        >
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>
            Support & Help
          </Text>
          {supportItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                {
                  borderBottomColor:
                    idx === supportItems.length - 1 ? 'transparent' : `${theme.colors.outline}22`,
                },
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon
                name={item.icon}
                size={24}
                color={theme.colors.onVariant}
                weight='regular'
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>{item.label}</Text>
              <PhosphorIcon
                name='chevron-right'
                size={24}
                color={theme.colors.onVariant}
                weight='regular'
                style={styles.sectionChevron}
              />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Section */}
        <View
          style={[
            styles.section,
            {
              backgroundColor: theme.colors.surface,
              shadowColor: '#000',
              marginTop: 4,
              marginBottom: 4,
            },
          ]}
        >
          {logoutItem.map((item) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                {
                  borderBottomColor: 'transparent',
                },
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon
                name={item.icon}
                size={24}
                color={theme.colors.error}
                weight='regular'
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionLabel, { color: theme.colors.error }]}>{item.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  profileContent: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xs,
    marginBottom: 0,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    padding: SPACING.xs,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md,
    position: 'relative',
  },

  profileInfo: {
    flex: 1,
  },

  editButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },

  // Section styles (replacing ProfileSectionList)
  section: {
    borderRadius: 0,
    padding: 8,
    marginVertical: 4,
    marginHorizontal: 0,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    marginLeft: 16,
    opacity: 0.7,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 0,
  },
  sectionIcon: {
    marginRight: 16,
  },
  sectionLabel: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  sectionChevron: {
    marginLeft: 8,
  },
});

export default MyProfileScreen;
