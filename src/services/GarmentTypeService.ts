import LoggingService from './LoggingService';
import { StorageService } from './storageService';

export interface GarmentTemplate {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Service for managing garment types using AsyncStorage via StorageService
 * This avoids adding a new DB table and keeps the UI responsive.
 */
export class GarmentTypeService {
  private static readonly STORAGE_KEY = 'garmentTypes';

  /** Default seed used only when storage is empty */
  private static readonly DEFAULT_TEMPLATES: GarmentTemplate[] = [
    {
      id: 'gt_1',
      name: 'Shirt',
      price: 1200,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'],
    },
    {
      id: 'gt_2',
      name: 'Pants',
      price: 1000,
      measurementFields: ['waist', 'hip', 'length', 'inseam'],
    },
    {
      id: 'gt_3',
      name: 'Suit',
      price: 3500,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length', 'inseam'],
    },
    {
      id: 'gt_4',
      name: 'Dress',
      price: 1800,
      measurementFields: ['bust', 'waist', 'hip', 'length'],
    },
    {
      id: 'gt_5',
      name: 'Kurta',
      price: 1500,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'],
    },
  ];

  /** Generate a reasonably unique id */
  private static generateId(prefix: string = 'gt'): string {
    const random = Math.random().toString(36).slice(2, 8);
    return `${prefix}_${Date.now()}_${random}`;
  }

  /** Ensure storage has data; seed defaults if empty */
  static async seedIfEmpty(): Promise<void> {
    const existing = await StorageService.get<GarmentTemplate[]>(this.STORAGE_KEY);
    if (!existing || existing.length === 0) {
      const now = new Date().toISOString();
      const seeded = this.DEFAULT_TEMPLATES.map(t => ({
        ...t,
        createdAt: now,
        updatedAt: now,
      }));
      await StorageService.set(this.STORAGE_KEY, seeded);
      LoggingService.info('Seeded default garment types', 'GARMENT_TYPES');
    }
  }

  /** Get all garment types */
  static async list(): Promise<GarmentTemplate[]> {
    const templates = (await StorageService.get<GarmentTemplate[]>(this.STORAGE_KEY)) || [];
    return Array.isArray(templates) ? templates : [];
  }

  /** Get by id */
  static async getById(id: string): Promise<GarmentTemplate | null> {
    const templates = await this.list();
    return templates.find(t => t.id === id) || null;
  }

  /** Create a new garment type */
  static async create(
    input: Omit<GarmentTemplate, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<GarmentTemplate> {
    const templates = await this.list();
    const now = new Date().toISOString();
    const newItem: GarmentTemplate = {
      ...input,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
    };

    const next = [...templates, newItem];
    await StorageService.set(this.STORAGE_KEY, next);
    LoggingService.info(`Created garment type: ${newItem.name}`, 'GARMENT_TYPES');
    return newItem;
  }

  /** Update a garment type */
  static async update(
    id: string,
    updates: Partial<Omit<GarmentTemplate, 'id' | 'createdAt'>>
  ): Promise<GarmentTemplate> {
    const templates = await this.list();
    const idx = templates.findIndex(t => t.id === id);
    if (idx === -1) {
      throw new Error('Garment type not found');
    }

    const now = new Date().toISOString();
    const updated: GarmentTemplate = {
      ...templates[idx],
      ...updates,
      updatedAt: now,
    };

    const next = [...templates];
    next[idx] = updated;
    await StorageService.set(this.STORAGE_KEY, next);
    LoggingService.info(`Updated garment type: ${updated.name}`, 'GARMENT_TYPES');
    return updated;
  }

  /** Delete a garment type */
  static async remove(id: string): Promise<void> {
    const templates = await this.list();
    const filtered = templates.filter(t => t.id !== id);
    await StorageService.set(this.STORAGE_KEY, filtered);
    LoggingService.info(`Deleted garment type: ${id}`, 'GARMENT_TYPES');
  }

  /** Replace all (use with caution) */
  static async replaceAll(items: GarmentTemplate[]): Promise<void> {
    await StorageService.set(this.STORAGE_KEY, items);
  }
}

export default GarmentTypeService;
