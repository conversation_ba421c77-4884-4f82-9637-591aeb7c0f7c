/**
 * ChipGroup - Container component for displaying multiple chips in a horizontal scrollable row
 * Optimized for performance by memoizing children and streamlining data transformation.
 */

import React, { useMemo, useCallback } from 'react';
import { View, StyleSheet, ScrollView, ViewStyle } from 'react-native';

import Chip, { ChipBorderRadius, ChipSize } from './Chip';
import { PhosphorIconName } from '../../utils/phosphorIconRegistry';

// Improved type safety for filter items
interface FilterItem {
  id: string | number;
  label: string;
  icon?: PhosphorIconName;
  value?: string | number;
}

// Optimization 1: Create a memoized child component for the list.
interface MemoizedChipProps {
  id: string | number;
  label: string;
  icon?: PhosphorIconName;
  isSelected: boolean;
  onFilterChange?: (id: string | number) => void;
  chipSize: ChipSize;
  borderRadius: ChipBorderRadius;
  style?: ViewStyle;
}

const MemoizedChip: React.FC<MemoizedChipProps> = React.memo(
  ({ id, label, icon, isSelected, onFilterChange, chipSize, borderRadius, style }) => {
    const handlePress = useCallback(() => {
      onFilterChange?.(id);
    }, [onFilterChange, id]);

    return (
      <View style={[styles.chipWrapper, style]}>
        <Chip
          label={label}
          leftIcon={icon}
          selected={isSelected}
          onPress={handlePress}
          variant={isSelected ? 'solid' : 'outlined'}
          size={chipSize}
          borderRadius={borderRadius}
        />
      </View>
    );
  }
);
MemoizedChip.displayName = 'MemoizedChip';


// Main ChipGroup Component
interface ChipGroupProps {
  filters?: (FilterItem | string)[];
  selectedFilter?: string | number;
  onFilterChange?: (filter: string | number) => void;
  showCounts?: boolean;
  data?: any[];
  countField?: string;
  style?: ViewStyle;
  chipStyle?: ViewStyle;
  showIcons?: boolean;
  borderRadius?: ChipBorderRadius;
  chipSize?: ChipSize;
}

const ChipGroup: React.FC<ChipGroupProps> = React.memo(
  ({
    filters = [],
    selectedFilter,
    onFilterChange,
    showIcons = false,
    showCounts = false,
    data = [],
    countField = 'status',
    style,
    chipStyle,
    borderRadius = 'semi-rounded',
    chipSize = 'medium',
  }) => {
    
    const chipData = useMemo(() => {
      return filters
        .map((filter) => {
          const isObject = typeof filter === 'object' && filter !== null;
          const id = isObject ? (filter as FilterItem).id : filter as string;
          const label = isObject ? (filter as FilterItem).label : filter as string;

          if (!label || label.trim() === '') {
            return null;
          }

          const icon = showIcons && isObject ? (filter as FilterItem).icon : undefined;
          
          let finalLabel = label;
          if (showCounts && data.length > 0) {
            const count = label === 'All'
              ? data.length
              : data.filter(item => item[countField] === label).length;
            finalLabel = `${label}・${count}`;
          }

          return {
            id,
            label: finalLabel,
            icon,
            isSelected: selectedFilter === id,
          };
        })
        // ✅ CORRECTED LINE: Explicitly tell TypeScript we are removing nulls.
        .filter((chip): chip is NonNullable<typeof chip> => Boolean(chip));
    }, [filters, showIcons, showCounts, data, countField, selectedFilter]);

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[styles.chipContainer, style]}
      >
        {chipData.map((chip) => (
          <MemoizedChip
            key={chip.id}
            id={chip.id}
            label={chip.label}
            icon={chip.icon}
            isSelected={chip.isSelected}
            onFilterChange={onFilterChange}
            chipSize={chipSize}
            borderRadius={borderRadius}
            style={chipStyle}
          />
        ))}
      </ScrollView>
    );
  }
);

ChipGroup.displayName = 'ChipGroup';

const styles = StyleSheet.create({
  chipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  chipWrapper: {
    marginHorizontal: 4,
  },
});

export default ChipGroup;