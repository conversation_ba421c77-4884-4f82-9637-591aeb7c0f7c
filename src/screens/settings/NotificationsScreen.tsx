import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ListRenderItem,
  TouchableOpacity,
} from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';
import { ActionSheetOption } from '../../types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import ChipGroup from '../../components/ui/ChipGroup';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import LoggingService from '../../services/LoggingService';
import { NotificationLog as NotificationType, NotificationCategory as NotificationServiceCategory, useNotifications } from '../../services/notificationService';
import { StorageService } from '../../services/storageService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

type Notification = NotificationType & {
  type: string;
  data?: { action?: () => void; actionType?: string; actionText?: string };
};

interface NotificationCategoryConfig {
  id: string;
  label: string;
  types?: string[];
}

interface NotificationsScreenProps {
  navigation: any;
}

const NOTIFICATION_CONFIG = {
  view_order: {
    icon: 'clipboard-text' as PhosphorIconName,
    title: 'Orders',
    color: '#4CAF50',
    route: 'Orders',
  },
  inventory_low: {
    icon: 'warning-circle' as PhosphorIconName,
    title: 'Low Stock',
    color: '#F44336',
    route: 'Products',
  },
  inventory_added: {
    icon: 'plus-circle' as PhosphorIconName,
    title: 'Stock Added',
    color: '#4CAF50',
    route: 'Products',
  },
  inventory_removed: {
    icon: 'minus-circle' as PhosphorIconName,
    title: 'Stock Removed',
    color: '#FF9800',
    route: 'Products',
  },
  inventory_transferred: {
    icon: 'arrows-left-right' as PhosphorIconName,
    title: 'Stock Transfer',
    color: '#2196F3',
    route: 'Products',
  },
  make_payment: {
    icon: 'money' as PhosphorIconName,
    title: 'Payments',
    color: '#FF9800',
    route: 'Financial',
  },
  view_payment: {
    icon: 'money' as PhosphorIconName,
    title: 'Payments',
    color: '#FF9800',
    route: 'Financial',
  },
  view_appointment: {
    icon: 'calendar' as PhosphorIconName,
    title: 'Appointments',
    color: '#2196F3',
    route: 'Measurement',
  },
  restock: {
    icon: 'package' as PhosphorIconName,
    title: 'Inventory',
    color: '#F44336',
    route: 'Products',
  },
  system_update: {
    icon: 'gear' as PhosphorIconName,
    title: 'System',
    color: '#9C27B0',
    route: 'About',
  },
  view_review: {
    icon: 'star' as PhosphorIconName,
    title: 'Reviews',
    color: '#FFC107',
    route: 'Customers',
  },
  order_supplies: {
    icon: 'shopping-cart' as PhosphorIconName,
    title: 'Supplies',
    color: '#795548',
    route: 'AddFabric',
  },
  test: { icon: 'bell' as PhosphorIconName, title: 'Test', color: '#607D8B', route: null },
} as const;

const NotificationItem = React.memo(
  ({
    item,
    onPress,
    config,
    formatTimestamp,
    theme,
    onDelete,
  }: {
    item: Notification;
    onPress: (notification: Notification) => void;
    onDelete?: (notification: Notification) => void;
    config: (typeof NOTIFICATION_CONFIG)[keyof typeof NOTIFICATION_CONFIG];
    formatTimestamp: (timestamp: string) => string;
    theme: any;
  }) => (
    <TouchableOpacity
      onPress={() => onPress(item)}
      activeOpacity={0.7}
      style={[
        styles.notificationItem,
        {
          backgroundColor: theme.colors.surface,
          borderRadius: BORDER_RADIUS.lg,
          marginBottom: SPACING.sm,
          ...SHADOWS.sm,
        },
      ]}
    >
      <View style={styles.notificationContent}>
        <View style={styles.avatarSection}>
          <View style={[styles.avatar, { backgroundColor: config.color }]}>
            <PhosphorIcon name={config.icon} size={20} color='white' />
          </View>
        </View>

        <View style={styles.contentSection}>
          <View style={styles.notificationHeader}>
            <Text
              style={[
                styles.notificationTitle,
                {
                  color: item.read ? theme.colors.onSurfaceVariant : theme.colors.onSurface,
                  fontWeight: item.read ? '400' : '700',
                },
              ]}
            >
              {item.title}
            </Text>
            <View style={styles.timestampContainer}>
              <View style={styles.timestampActions}>
              <Text style={[styles.timestamp, { color: theme.colors.onSurfaceVariant }]}>
                {formatTimestamp(item.timestamp)}
              </Text>
              {onDelete && (
                <TouchableOpacity
                  onPress={() => onDelete(item)}
                  style={styles.deleteButton}
                >
                  <PhosphorIcon name="trash" size={18} color={theme.colors.error} />
                </TouchableOpacity>
              )}
              {!item.read && (
                <View style={[styles.unreadBadge, { backgroundColor: config.color }]} />
              )}
          </View>
            </View>
          </View>

          <Text
            style={[
              styles.notificationMessage,
              { color: item.read ? theme.colors.onSurfaceVariant : theme.colors.onSurface },
            ]}
          >
            {item.message}
          </Text>

          {item.data && (item.data.action || item.data.actionType) && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: `${theme.colors.primary}15` }]}
              onPress={() => onPress(item)}
            >
              <Text style={[styles.actionButtonText, { color: theme.colors.primary }]}>
                {item.data.actionText || 'View'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  )
);

const EmptyState = React.memo(
  ({
    theme,
    typeFilter,
    onAddTest,
    onGenerateSample,
  }: {
    theme: any;
    typeFilter: string;
    onAddTest: () => void;
    onGenerateSample: () => void;
  }) => (
    <View style={styles.emptyStateContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: `${theme.colors.primary}10` }]}>
        <PhosphorIcon name='bell' size={48} color={theme.colors.primary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
        No notifications yet
      </Text>
      <Text style={[styles.emptyMessage, { color: theme.colors.onSurfaceVariant }]}>
        You'll see notifications about orders, payments, appointments, and more here.
      </Text>
      {typeFilter === 'all' && (
        <View style={styles.emptyActions}>
          <Button variant='primary' onPress={onAddTest} style={styles.testButton}>
            Add Test Notification
          </Button>
          <Button variant='outline' onPress={onGenerateSample}>
            Generate Sample Data
          </Button>
        </View>
      )}
    </View>
  )
);

const NotificationsScreen: React.FC<NotificationsScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { showSuccess, showError } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showRead, setShowRead] = useState(true);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Mock notifications data - replace with real data source
  useEffect(() => {
    const loadInitialNotifications = async () => {
      try {
        setLoading(true);
        const storedNotifications = await StorageService.get('notifications');
        if (storedNotifications) {
          setNotifications(storedNotifications);
        }
      } catch (error) {
        LoggingService.error('Failed to load notifications', 'NotificationsScreen', error as Error);
        showError('Failed to load notifications');
      } finally {
        setLoading(false);
      }
    };

    loadInitialNotifications();
  }, []);

  const unreadCount = useMemo(
    () => notifications?.filter((n: Notification) => !n.read).length || 0,
    [notifications]
  );

  const NOTIFICATION_CATEGORIES: Record<string, NotificationCategoryConfig> = {
    all: { id: 'all', label: 'All' },
    inventory: {
      id: 'inventory',
      label: 'Inventory',
      types: ['inventory_low', 'inventory_added', 'inventory_removed', 'inventory_transferred', 'restock', 'order_supplies']
    },
    orders: { id: 'orders', label: 'Orders', types: ['view_order'] },
    payments: { id: 'payments', label: 'Payments', types: ['make_payment', 'view_payment'] },
    appointments: { id: 'appointments', label: 'Appointments', types: ['view_appointment'] },
    system: { id: 'system', label: 'System', types: ['system_update'] }
  };

  const notificationTypes = useMemo(
    () =>
      Array.from(new Set(notifications?.map((n: Notification) => n.data?.actionType || 'other'))),
    [notifications]
  );

  const filteredNotifications = useMemo(() => {
    let filtered = [...notifications];

    // Filter by read status
    if (!showRead) {
      filtered = filtered.filter(n => !n.read);
    }

    // Filter by category
    if (typeFilter !== 'all') {
      const category = NOTIFICATION_CATEGORIES[typeFilter as keyof typeof NOTIFICATION_CATEGORIES];
      const types = category?.types;
      if (types) {
        filtered = filtered.filter(n => 
          types.includes(n.data?.actionType || '')
        );
      }
    }

    return filtered.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [notifications, typeFilter, showRead]);

  const formatTimestamp = useCallback((timestamp: string) => {
    const diffInHours = (Date.now() - new Date(timestamp).getTime()) / (1000 * 60 * 60);
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return new Date(timestamp).toLocaleDateString();
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const updatedNotifications = notifications.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      );
      setNotifications(updatedNotifications);
      await StorageService.set('notifications', updatedNotifications);
    } catch (error) {
      LoggingService.error('Failed to mark notification as read', 'NotificationsScreen', error as Error);
    }
  }, [notifications]);

  const handleDelete = useCallback(async (notification: Notification) => {
    try {
      const updatedNotifications = notifications.filter(n => n.id !== notification.id);
      setNotifications(updatedNotifications);
      await StorageService.set('notifications', updatedNotifications);
      showSuccess('Notification deleted successfully');
    } catch (error) {
      LoggingService.error('Failed to delete notification', 'NotificationsScreen', error as Error);
      showError('Failed to delete notification');
    }
  }, [notifications, showSuccess, showError]);

  const handleNotificationPress = useCallback(
    (notification: Notification): void => {
      markAsRead(notification.id);
      const actionType = notification.data?.actionType;
      if (actionType && actionType in NOTIFICATION_CONFIG) {
        const config = NOTIFICATION_CONFIG[actionType as keyof typeof NOTIFICATION_CONFIG];
        if (actionType === 'test') {
          // setShowTestNotificationActionSheet(true); // This line was removed from the new_code, so it's removed here.
        } else if (config.route) {
          navigation.navigate(config.route);
        }
      } else if (notification.data?.action && typeof notification.data.action === 'function') {
        try {
          notification.data.action();
        } catch (error) {
          LoggingService.error(
            'Error executing notification action',
            'NOTIFICATION',
            error as Error
          );
        }
      }
    },
    [navigation]
  );

  const getNotificationConfig = useCallback(
    (actionType: string) =>
      NOTIFICATION_CONFIG[actionType as keyof typeof NOTIFICATION_CONFIG] ||
      NOTIFICATION_CONFIG.test,
    []
  );

  // Remove this section as we're already using the categories constant



  // Create filter options for ChipGroup
  const filterOptions = useMemo(() => {
    const options = [{ id: 'all', label: 'All' }];

    // Add category filters
    Object.entries(NOTIFICATION_CATEGORIES).forEach(([key, category]) => {
      if (key !== 'all') {
        // Always show all categories regardless of whether they have notifications
        options.push({
          id: category.id,
          label: category.label
        });
      }
    });

    return options;
  }, [notifications]);

  const [showClearAllActionSheet, setShowClearAllActionSheet] = useState(false);

  const handleClearAll = useCallback(async () => {
    setShowClearAllActionSheet(true);
  }, []);

  const handleConfirmClearAll = useCallback(async () => {
    try {
      await StorageService.set('notifications', []);
      setNotifications([]);
      showSuccess('All notifications cleared');
    } catch (error) {
      LoggingService.error('Failed to clear notifications', 'NotificationsScreen', error as Error);
      showError('Failed to clear notifications');
    }
    setShowClearAllActionSheet(false);
  }, [showSuccess, showError]);

  const handleAddTest = useCallback(
    () =>
      // addNotification({ // This line was removed from the new_code, so it's removed here.
      //   title: 'Test Notification',
      //   message: 'This is a test notification to verify the system is working.',
      //   data: { actionText: 'Test Action', actionType: 'test' },
      // }),
      setNotifications(prev => [
        ...prev,
        {
          id: String(prev.length + 1),
          title: 'Test Notification',
          message: 'This is a test notification to verify the system is working.',
          type: 'test',
          category: 'general',
          timestamp: new Date().toISOString(),
          read: false,
          data: { actionType: 'test' },
        },
      ]),
    []
  );

  const handleGenerateSample = useCallback(() => navigation.navigate('ImportData'), [navigation]);

  const renderItem: ListRenderItem<Notification> = useCallback(
    ({ item }) => (
      <NotificationItem
        item={item}
        onPress={handleNotificationPress}
        onDelete={handleDelete}
        config={getNotificationConfig(item.data?.actionType || 'test')}
        formatTimestamp={formatTimestamp}
        theme={theme}
      />
    ),
    [handleNotificationPress, handleDelete, getNotificationConfig, formatTimestamp, theme]
  );

  const renderEmptyState = useCallback(
    () => (
      <EmptyState
        theme={theme}
        typeFilter={typeFilter}
        onAddTest={handleAddTest}
        onGenerateSample={handleGenerateSample}
      />
    ),
    [theme, typeFilter, handleAddTest, handleGenerateSample]
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title='Notifications' onBackPress={() => navigation.goBack()} showBack={true} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurfaceVariant }]}>
            Loading notifications...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={`Notifications (${unreadCount})`}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={notifications.length > 0 ? [{ icon: 'trash', onPress: handleClearAll }] : []}
        
      />

      <View style={styles.filterChipsContainer}>
        <ChipGroup
          filters={filterOptions}
          selectedFilter={typeFilter}
          onFilterChange={filter => setTypeFilter(String(filter))}
          style={styles.chipGroupStyle}
        />
      </View>

      <FlatList
        data={filteredNotifications}
        keyExtractor={(item: Notification) => item.id}
        renderItem={renderItem}
        contentContainerStyle={[styles.listContent, { padding: SPACING.md }]}
        ListEmptyComponent={renderEmptyState}
      />

      <ActionSheet
        visible={showClearAllActionSheet}
        onDismiss={() => setShowClearAllActionSheet(false)}
        title="Clear Notifications"
        options={[
          {
            text: 'Clear All Notifications',
            onPress: handleConfirmClearAll,
            style: 'destructive',
            isAction: true,
          },
          {
            text: 'Cancel',
            onPress: () => setShowClearAllActionSheet(false),
            style: 'cancel',
            isAction: false,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  listContent: {},
  filterChipsContainer: { paddingVertical: 12 },
  chipGroupStyle: { paddingHorizontal: 16 },
  notificationItem: { paddingHorizontal: 0, paddingVertical: 16, marginHorizontal: 0 },
  notificationContent: { flexDirection: 'row', alignItems: 'flex-start', flex: 1 },
  avatarSection: { position: 'relative', marginRight: 4, marginLeft: 16 },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadBadge: { width: 8, height: 8, borderRadius: 4 },
  contentSection: { flex: 1, marginRight: 4, paddingHorizontal: 12 },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '700',
    flex: 1,
    marginRight: 12,
    letterSpacing: -0.2,
  },
  timestampContainer: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  timestampActions: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  deleteButton: { padding: 4 },
  timestamp: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'right',
    minWidth: 80,
    fontWeight: '400',
  },
  notificationMessage: { fontSize: 14, lineHeight: 18, marginBottom: 10, letterSpacing: -0.1 },
  actionButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 6,
  },
  actionButtonText: { fontSize: 14, fontWeight: '500' },
  emptyStateContainer: {
    alignItems: 'center',
    marginTop: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyTitle: {
    textAlign: 'center',
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyMessage: {
    textAlign: 'center',
    fontSize: TYPOGRAPHY.fontSize.md,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.md,
    marginBottom: SPACING.xl,
  },
  emptyActions: { gap: SPACING.sm, width: '100%' },
  testButton: { marginBottom: SPACING.sm },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  loadingText: { marginTop: SPACING.md },
});

export default NotificationsScreen;