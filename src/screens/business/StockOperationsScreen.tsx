/**
 * Stock Operations Screen
 * Handles stock in and stock out operations with real-time conversion
 */

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Card, Chip, HelperText, SegmentedButtons, Text, TextInput } from 'react-native-paper';

import Search from '../../components/forms/Search';
import {
  ConversionPreview,
  InventoryCard,
  UnitQuantityInput,
  WarehouseSelector,
} from '../../components/inventory';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { ActionSheetOption } from '../../types';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { InventoryItem, Warehouse } from '../../types/inventory';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

interface StockOperationsScreenProps {
  navigation: any;
  route?: {
    params?: {
      operation?: 'in' | 'out';
      itemId?: string;
      warehouseId?: string;
    };
  };
}

type OperationType = 'in' | 'out';

interface FormData {
  selectedItem: InventoryItem | null;
  selectedWarehouse: Warehouse | null;
  quantity: number;
  unit: string;
  reference: string;
  note: string;
}

interface FormErrors {
  item?: string;
  warehouse?: string;
  quantity?: string;
  general?: string;
}

const StockOperationsScreen: React.FC<StockOperationsScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const stockIn = actions.stockIn;
  const stockOut = actions.stockOut;
  const getStockByItem = actions.getStockByItem;

  const initialOperation = route?.params?.operation || 'in';
  const initialItemId = route?.params?.itemId;
  const initialWarehouseId = route?.params?.warehouseId;

  // Form state
  const [operation, setOperation] = useState<OperationType>(initialOperation);
  const [formData, setFormData] = useState<FormData>({
    selectedItem: null,
    selectedWarehouse: null,
    quantity: 0,
    unit: 'meter',
    reference: '',
    note: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [currentStock, setCurrentStock] = useState<number>(0);
  const [showItemSearch, setShowItemSearch] = useState<boolean>(false);

  // ActionSheet state
  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Initialize form with route params
  useEffect(() => {
    if (initialItemId) {
      const item = dataState.products.find(i => i.id === initialItemId);
      if (item) {
        setFormData(prev => ({
          ...prev,
          selectedItem: item as any,
          unit: item.baseUnit || '',
        }));
      }
    }

    if (initialWarehouseId) {
      const warehouse = dataState.warehouses.find(w => w.id === initialWarehouseId);
      if (warehouse) {
        setFormData(prev => ({
          ...prev,
          selectedWarehouse: warehouse,
        }));
      }
    }
  }, [initialItemId, initialWarehouseId, dataState.products, dataState.warehouses]);

  // Load current stock when item and warehouse are selected
  useEffect(() => {
    const loadCurrentStock = async () => {
      if (formData.selectedItem && formData.selectedWarehouse) {
        try {
          const stockData = await getStockByItem(formData.selectedItem.id);
          const warehouseStock = stockData.find(
            s => s.warehouseId === formData.selectedWarehouse!.id
          );
          setCurrentStock(warehouseStock?.quantity || 0);
        } catch (error) {
          LoggingService.error('Failed to load current stock', 'STOCK_OPERATIONS', error as Error);
          setCurrentStock(0);
        }
      }
    };

    loadCurrentStock();
  }, [formData.selectedItem, formData.selectedWarehouse, getStockByItem]);

  // Operation options
  const operationOptions = [
    {
      value: 'in',
      label: 'Stock In',
      icon: 'arrow-down',
    },
    {
      value: 'out',
      label: 'Stock Out',
      icon: 'arrow-up',
    },
  ];

  // Form validation
  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.selectedItem) {
      newErrors.item = 'Please select an item';
    }

    if (!formData.selectedWarehouse) {
      newErrors.warehouse = 'Please select a warehouse';
    }

    if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    // Additional validation for stock out
    if (operation === 'out' && formData.quantity > currentStock) {
      newErrors.quantity = `Insufficient stock. Available: ${currentStock} ${formData.unit}`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, operation, currentStock]);

  // Handle item selection
  const handleItemSelect = useCallback((item: any) => {
    // Convert SearchItem to InventoryItem
    const inventoryItem = item as InventoryItem;
    setFormData(prev => ({
      ...prev,
      selectedItem: inventoryItem,
      unit: inventoryItem.baseUnit,
    }));
    setShowItemSearch(false);
    setErrors(prev => ({ ...prev, item: undefined }));
  }, []);

  // Handle warehouse selection
  const handleWarehouseSelect = useCallback((warehouse: Warehouse) => {
    setFormData(prev => ({
      ...prev,
      selectedWarehouse: warehouse,
    }));
    setErrors(prev => ({ ...prev, warehouse: undefined }));
  }, []);

  // Handle quantity change
  const handleQuantityChange = useCallback((quantity: number) => {
    setFormData(prev => ({
      ...prev,
      quantity,
    }));
    setErrors(prev => ({ ...prev, quantity: undefined }));
  }, []);

  // Handle unit change
  const handleUnitChange = useCallback((unit: string) => {
    setFormData(prev => ({
      ...prev,
      unit,
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      let result;

      if (operation === 'in') {
        result = await stockIn(
          formData.selectedItem!.id,
          formData.selectedWarehouse!.id,
          formData.quantity,
          formData.unit,
          'Current User', // This should come from auth context
          formData.reference || undefined,
          formData.note || undefined
        );
      } else {
        result = await stockOut(
          formData.selectedItem!.id,
          formData.selectedWarehouse!.id,
          formData.quantity,
          formData.unit,
          'Current User', // This should come from auth context
          formData.reference || undefined,
          formData.note || undefined
        );
      }

      if ((result as any).success) {
        setSuccessMessage((result as any).message);
        setShowSuccessActionSheet(true);
        LoggingService.info(`Stock ${operation} operation completed`, 'STOCK_OPERATIONS', result);
      } else {
        setErrors({ general: (result as any).error || 'Operation failed' });
      }
    } catch (error) {
      LoggingService.error(
        `Stock ${operation} operation failed`,
        'STOCK_OPERATIONS',
        error as Error
      );
      setErrors({ general: (error as Error).message });
    } finally {
      setLoading(false);
    }
  }, [formData, operation, validateForm, stockIn, stockOut, navigation]);

  // Get operation color
  const getOperationColor = (op: OperationType): string => {
    return op === 'in' ? '#10B981' : '#EF4444';
  };

  // Filter active items for search
  const activeItems = useMemo(() => dataState.products.filter(item => item.isActive), [dataState.products]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Stock Operations'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            text: 'Submit',
            onPress: handleSubmit,
            disabled: loading,
          },
        ]}
        
      />

      <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Operation Type Selection */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Operation Type
              </Text>

              <SegmentedButtons
                value={operation}
                onValueChange={value => setOperation(value as OperationType)}
                buttons={operationOptions.map(option => ({
                  value: option.value,
                  label: option.label,
                  icon: option.icon,
                  style: {
                    backgroundColor:
                      operation === option.value
                        ? `${getOperationColor(option.value as OperationType)}20`
                        : 'transparent',
                  },
                }))}
                style={styles.segmentedButtons}
              />

              <View
                style={[
                  styles.operationIndicator,
                  { backgroundColor: `${getOperationColor(operation)}15` },
                ]}
              >
                <PhosphorIcon
                  name={operation === 'in' ? 'arrow-down' : 'arrow-up'}
                  size={20}
                  color={getOperationColor(operation)}
                />
                <Text style={[styles.operationText, { color: getOperationColor(operation) }]}>
                  {operation === 'in'
                    ? 'Adding stock to inventory'
                    : 'Removing stock from inventory'}
                </Text>
              </View>
            </Card.Content>
          </Card>

          {/* Item Selection */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Select Item
              </Text>

              {formData.selectedItem ? (
                <View>
                  <InventoryCard
                    item={formData.selectedItem}
                    stockLevel={currentStock}
                    onPress={() => setShowItemSearch(true)}
                    showStockLevel={true}
                    showActions={false}
                    showPrices={false}
                    compact={true}
                  />

                  <Button
                    variant='outline'
                    onPress={() => setShowItemSearch(true)}
                    style={styles.changeButton}
                    icon='swap-horizontal'
                  >
                    Change Item
                  </Button>
                </View>
              ) : (
                <Button
                  variant='outline'
                  onPress={() => setShowItemSearch(true)}
                  style={styles.selectButton}
                  icon={<PhosphorIcon name='magnifying-glass' />}
                >
                  Select Item
                </Button>
              )}

              {errors.item && <HelperText type='error'>{errors.item}</HelperText>}

              {/* Item Search Modal */}
              {showItemSearch && (
                <View style={styles.searchContainer}>
                  <Search
                    data={dataState.products}
                    searchFields={['name', 'category']}
                    placeholder='Search inventory items...'
                    onResultSelect={handleItemSelect}
                    style={styles.itemSearch}
                  />
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Warehouse Selection */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Select Warehouse
              </Text>

              <WarehouseSelector
                warehouses={dataState.warehouses}
                selectedWarehouseId={formData.selectedWarehouse?.id || null}
                onWarehouseSelect={handleWarehouseSelect}
                placeholder='Select warehouse'
                error={errors.warehouse}
              />
            </Card.Content>
          </Card>

          {/* Current Stock Display */}
          {formData.selectedItem && formData.selectedWarehouse && (
            <Card style={styles.sectionCard} mode='outlined'>
              <Card.Content>
                <View style={styles.stockInfoHeader}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                    Current Stock
                  </Text>
                  <Chip
                    mode='outlined'
                    style={[
                      styles.stockChip,
                      {
                        borderColor: currentStock > 0 ? theme.colors.primary : theme.colors.error,
                        backgroundColor:
                          currentStock > 0
                            ? `${theme.colors.primary}15`
                            : `${theme.colors.error}15`,
                      },
                    ]}
                    textStyle={{
                      color: currentStock > 0 ? theme.colors.primary : theme.colors.error,
                      fontWeight: '600',
                    }}
                  >
                    {currentStock} {formData.unit}
                  </Chip>
                </View>

                {operation === 'out' && currentStock === 0 && (
                  <HelperText type='error'>
                    No stock available for this item in the selected warehouse
                  </HelperText>
                )}
              </Card.Content>
            </Card>
          )}

          {/* Quantity Input */}
          {formData.selectedItem && (
            <Card style={styles.sectionCard} mode='outlined'>
              <Card.Content>
                <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                  Quantity
                </Text>

                <UnitQuantityInput
                  value={formData.quantity}
                  unit={formData.unit}
                  availableUnits={formData.selectedItem.availableUnits}
                  onQuantityChange={handleQuantityChange}
                  onUnitChange={handleUnitChange}
                  label={`${operation === 'in' ? 'Stock In' : 'Stock Out'} Quantity`}
                  placeholder='Enter quantity'
                  error={errors.quantity}
                  showConversion={true}
                  baseUnit={formData.selectedItem.baseUnit}
                />

                {/* Conversion Preview */}
                {formData.quantity > 0 && formData.unit !== formData.selectedItem.baseUnit && (
                  <ConversionPreview
                    quantity={formData.quantity}
                    fromUnit={formData.unit}
                    toUnit={formData.selectedItem.baseUnit}
                    showCalculation={true}
                  />
                )}

                {/* Stock Level Preview */}
                {formData.quantity > 0 && formData.selectedWarehouse && (
                  <View style={styles.stockPreview}>
                    <Text
                      style={[styles.stockPreviewLabel, { color: theme.colors.onSurfaceVariant }]}
                    >
                      After operation:
                    </Text>
                    <Text
                      style={[
                        styles.stockPreviewValue,
                        {
                          color:
                            operation === 'in'
                              ? theme.colors.primary
                              : currentStock - formData.quantity >= 0
                                ? theme.colors.onSurface
                                : theme.colors.error,
                        },
                      ]}
                    >
                      {operation === 'in'
                        ? currentStock + formData.quantity
                        : currentStock - formData.quantity}{' '}
                      {formData.unit}
                    </Text>
                  </View>
                )}
              </Card.Content>
            </Card>
          )}

          {/* Additional Information */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Additional Information
              </Text>

              {/* Reference */}
              <TextInput
                label='Reference (Optional)'
                value={formData.reference}
                onChangeText={text => setFormData(prev => ({ ...prev, reference: text }))}
                mode='outlined'
                style={styles.input}
                placeholder='e.g., Purchase Order #123, Sale #456'
              />

              {/* Note */}
              <TextInput
                label='Note (Optional)'
                value={formData.note}
                onChangeText={text => setFormData(prev => ({ ...prev, note: text }))}
                mode='outlined'
                multiline
                numberOfLines={3}
                style={styles.input}
                placeholder='Add any additional notes...'
              />
            </Card.Content>
          </Card>

          {/* General Error */}
          {errors.general && (
            <HelperText type='error' style={styles.generalError}>
              {errors.general}
            </HelperText>
          )}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              variant='outline'
              onPress={() => navigation.goBack()}
              style={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </Button>

            <Button
              variant='primary'
              onPress={handleSubmit}
              loading={loading}
              disabled={
                loading ||
                !formData.selectedItem ||
                !formData.selectedWarehouse ||
                formData.quantity <= 0
              }
              style={
                [styles.submitButton, { backgroundColor: getOperationColor(operation) }] as any
              }
              icon={operation === 'in' ? 'arrow-down' : 'arrow-up'}
            >
              {operation === 'in' ? 'Stock In' : 'Stock Out'}
            </Button>
          </View>
        </View>
      </ScrollView>

      <ActionSheet
        visible={showSuccessActionSheet}
        onDismiss={() => setShowSuccessActionSheet(false)}
        title='Success'
        description={successMessage}
        options={[
          {
            text: 'OK',
            onPress: () => {
              setShowSuccessActionSheet(false);
              navigation.goBack();
            },
            isAction: true,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  sectionCard: {
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  operationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  operationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectButton: {
    marginVertical: 8,
  },
  changeButton: {
    marginTop: 12,
  },
  searchContainer: {
    marginTop: 16,
  },
  itemSearch: {
    marginBottom: 8,
  },
  stockInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stockChip: {
    alignSelf: 'flex-start',
  },
  stockPreview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  stockPreviewLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  stockPreviewValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    marginVertical: 4,
  },
  generalError: {
    textAlign: 'center',
    marginVertical: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 1,
  },
});

export default StockOperationsScreen;
