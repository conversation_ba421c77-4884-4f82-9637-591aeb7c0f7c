import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { Checkbox, Text as PaperText } from 'react-native-paper';
import { ActionSheetOption } from '@/types';
import CustomerSelectionBottomSheet, {
  CustomerSelectionBottomSheetRef,
} from '@/components/bottomsheets/CustomerSelectionBottomSheet';
import GarmentTypeSelectionBottomSheet, {
  GarmentTypeSelectionBottomSheetRef,
} from '@/components/bottomsheets/GarmentTypeSelectionBottomSheet';
import TimePeriodBottomSheet, {
  TimePeriodBottomSheetRef,
} from '@/components/bottomsheets/TimePeriodBottomSheet';
import { DataCard } from '@/components/cards';
import ImagePicker from '@/components/forms/ImagePicker';
import Header from '@/components/navigation/Header';
import Button from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { GarmentDetail } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

// TypeScript interfaces
interface CreateOrderScreenProps {
  navigation: any;
  route: any;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

type SelectedTemplate = {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
  category?: string;
};

interface OrderFormErrors {
  customer?: string;
  garmentType?: string;
  price?: string;
  dueDate?: string;
  discount?: string;
}

interface OrderItem {
  id: string;
  garmentType: string;
  price: number;
  quantity: number;
  templateId?: string;
  measurements?: Record<string, number>;
  notes?: string;
}

const CreateOrderScreen = ({ navigation, route }: CreateOrderScreenProps) => {
  const theme = useTheme();
  const { actions } = useData();
  const { showSuccess, showError } = useToast();
  const isEditing = route.params?.isEditing || false;
  const editingOrder = route.params?.order || null;

  // Customer selection state
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    editingOrder?.customer || null
  );

  // Order details state
  const [garmentType, setGarmentType] = useState(editingOrder?.garmentType || '');
  const [price, setPrice] = useState<string>(editingOrder?.price?.toString() || '');
  const [advancePayment, setAdvancePayment] = useState(editingOrder?.paidAmount?.toString() || '');
  const [dueDate, setDueDate] = useState<string>(editingOrder?.dueDate || '');

  // Bottom sheet refs
  const timePeriodBottomSheetRef = useRef<TimePeriodBottomSheetRef>(null);
  const garmentTypeBottomSheetRef = useRef<GarmentTypeSelectionBottomSheetRef>(null);
  const customerSelectionBottomSheetRef = useRef<CustomerSelectionBottomSheetRef>(null);

  // Lazy-mount flags for bottom sheets
  const [showTimePeriodSheet, setShowTimePeriodSheet] = useState(false);
  const [showGarmentSheet, setShowGarmentSheet] = useState(false);
  const [showCustomerSheet, setShowCustomerSheet] = useState(false);

  // Auto-open sheets after they mount to avoid blocking navigation transitions
  useEffect(() => {
    if (showTimePeriodSheet) {
      const id = setTimeout(() => {
        timePeriodBottomSheetRef.current?.open();
      }, 0);
      return () => clearTimeout(id);
    }
  }, [showTimePeriodSheet]);
  useEffect(() => {
    if (showGarmentSheet) {
      const id = setTimeout(() => {
        garmentTypeBottomSheetRef.current?.open();
      }, 0);
      return () => clearTimeout(id);
    }
  }, [showGarmentSheet]);
  useEffect(() => {
    if (showCustomerSheet) {
      const id = setTimeout(() => {
        customerSelectionBottomSheetRef.current?.open();
      }, 0);
      return () => clearTimeout(id);
    }
  }, [showCustomerSheet]);

  // Garment template state
  const [selectedTemplates, setSelectedTemplates] = useState<SelectedTemplate[]>([]);

  // Enhanced garment details state
  const [garmentDetails, setGarmentDetails] = useState<GarmentDetail[]>([]);

  // Discount state
  const [discount, setDiscount] = useState({ amount: 0, type: 'amount' as const });

  // Form validation state
  const [errors, setErrors] = useState<OrderFormErrors>({});
  const [garmentValidationErrors, setGarmentValidationErrors] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Expanded sections state
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Calculate total price
  const totalPrice = useMemo(() => {
    const garmentTotal = garmentDetails.reduce((sum, detail) => sum + detail.totalPrice, 0);
    const fabricTotal = garmentDetails.reduce((sum, detail) =>
      detail.fabricSelection === 'inhouse' && detail.fabricAmount && detail.fabricPrice
        ? sum + detail.fabricAmount * detail.fabricPrice
        : sum, 0);
    const extraChargeTotal = garmentDetails.reduce((sum, detail) => sum + (detail.extraCharge || 0), 0);
    const subtotal = garmentTotal + fabricTotal + extraChargeTotal;

    return Math.max(0, subtotal - discount.amount);
  }, [garmentDetails, discount]);

  // Calculate remaining amount
  const remainingAmount = useMemo(() => {
    const advance = parseFloat(advancePayment) || 0;
    return Math.max(0, totalPrice - advance);
  }, [totalPrice, advancePayment]);

  // Handle customer selection
  const handleCustomerSelect = useCallback(
    (customer: Customer) => {
      setSelectedCustomer(customer);
      if (errors.customer) {
        setErrors(prev => ({ ...prev, customer: undefined }));
      }
    },
    [errors.customer]
  );

  const handleClearCustomer = useCallback(() => {
    setSelectedCustomer(null);
  }, []);

  // Handle customer selection button press
  const handleCustomerPress = useCallback(() => {
    setShowCustomerSheet(true);
  }, []);

  // Handle garment type selection
  const handleGarmentTypeSelect = useCallback(
    (templates: { id: string; name: string; price: number; measurementFields: string[] }[]) => {
      if (templates.length > 0) {
        setSelectedTemplates(templates);

        // Create garment details for each selected template
        const newGarmentDetails: GarmentDetail[] = templates.map(template => ({
          id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
          garmentType: template.name,
          templateId: template.id || '',
          measurements: {},
          fabricSelection: 'customer' as const,
          fabricAmount: 0,
          fabricPrice: 0,
          notes: '',
          images: [],
          quantity: 1,
          isUrgent: false,
          sampleGiven: false,
          extraCharge: 0,
          unitPrice: template.price,
          totalPrice: template.price,
        }));

        setGarmentDetails(newGarmentDetails);
        setGarmentType(templates.map(t => t.name).join(', '));

        if (errors.garmentType) {
          setErrors(prev => ({ ...prev, garmentType: undefined }));
        }
      }
    },
    [errors.garmentType]
  );

  // Handle garment type button press
  const handleGarmentTypePress = useCallback(() => {
    setShowGarmentSheet(true);
  }, []);



  // Handle garment detail updates
  const handleGarmentDetailUpdate = useCallback(
    (detailId: string, updates: Partial<GarmentDetail>) => {
      setGarmentDetails(prev =>
        prev.map(detail =>
          detail.id === detailId
            ? {
              ...detail,
              ...updates,
              totalPrice:
                (updates.quantity || detail.quantity) * (updates.unitPrice || detail.unitPrice),
            }
            : detail
        )
      );
    },
    []
  );

  const handleMeasurementChange = useCallback((detailId: string, field: string, value: number) => {
    setGarmentDetails(prev =>
      prev.map(detail =>
        detail.id === detailId
          ? {
            ...detail,
            measurements: { ...detail.measurements, [field]: value },
          }
          : detail
      )
    );
  }, []);

  const handleRemoveGarmentDetail = useCallback(
    (detailId: string) => {
      setGarmentDetails(prev => prev.filter(detail => detail.id !== detailId));
      setSelectedTemplates(prev =>
        prev.filter(
          template =>
            garmentDetails.find(detail => detail.id === detailId)?.templateId !== template.id
        )
      );
    },
    [garmentDetails]
  );

  // Handle due date selection
  const handleDueDatePress = useCallback(() => {
    LoggingService.debug('Due date press handled', 'CREATE_ORDER_SCREEN');
    setShowTimePeriodSheet(true);
  }, []);

  // Handle date selection
  const handleDateSelect = useCallback((date: Date) => {
    LoggingService.debug('Date selected', 'CREATE_ORDER_SCREEN', { date: date.toISOString() });
    setDueDate(date.toLocaleDateString());
  }, []);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors: OrderFormErrors = {};
    const newGarmentErrors: Record<string, any> = {};

    // Basic validations
    if (!selectedCustomer) newErrors.customer = 'Select a customer';
    if (selectedTemplates.length === 0 && !garmentType.trim()) {
      newErrors.garmentType = 'Please select at least one garment type or enter garment details';
    }
    if (!price || isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      newErrors.price = 'Please enter a valid price';
    }
    if (!dueDate) newErrors.dueDate = 'Please select a due date';

    // Payment validations
    const advance = parseFloat(advancePayment) || 0;
    if (advance > totalPrice) newErrors.price = 'Advance payment cannot exceed total price';
    if (discount.amount > 0 && discount.amount > totalPrice) {
      newErrors.discount = 'Discount amount cannot exceed total price';
    }

    // Validate garment details
    garmentDetails.forEach(detail => {
      const detailErrors: any = { measurements: {} };
      const template = selectedTemplates.find(t => t.id === detail.templateId);

      // Validate measurements
      if (template) {
        template.measurementFields.forEach(field => {
          const value = detail.measurements[field];
          if (!value || value <= 0) {
            detailErrors.measurements[field] = `${field} is required`;
          }
        });
      }

      // Validate fabric details for in-house selection
      if (detail.fabricSelection === 'inhouse') {
        if (!detail.fabricAmount || detail.fabricAmount <= 0) {
          detailErrors.fabricAmount = 'Fabric amount is required';
        }
        if (!detail.fabricPrice || detail.fabricPrice <= 0) {
          detailErrors.fabricPrice = 'Fabric price is required';
        }
      }

      // Validate quantity
      if (!detail.quantity || detail.quantity < 1) {
        detailErrors.quantity = 'Quantity must be at least 1';
      }

      // Add errors if any exist
      const hasErrors = Object.keys(detailErrors.measurements).length > 0 ||
        detailErrors.fabricAmount ||
        detailErrors.fabricPrice ||
        detailErrors.quantity;

      if (hasErrors) {
        newGarmentErrors[detail.id] = detailErrors;
      }
    });

    setErrors(newErrors);
    setGarmentValidationErrors(newGarmentErrors);

    return Object.keys(newErrors).length === 0 && Object.keys(newGarmentErrors).length === 0;
  }, [selectedCustomer, selectedTemplates, garmentType, price, dueDate, advancePayment, totalPrice, discount, garmentDetails]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Create order items from garment details
      const orderItems: OrderItem[] = garmentDetails.length > 0
        ? garmentDetails.map((detail: GarmentDetail) => ({
          id: detail.id,
          garmentType: detail.garmentType,
          price: detail.totalPrice,
          quantity: detail.quantity,
          templateId: detail.templateId,
          measurements: detail.measurements,
          notes: detail.notes,
        }))
        : [{
          id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
          garmentType,
          price: parseFloat(price) || 0,
          quantity: 1,
          measurements: {},
          notes: '',
        }];

      // Calculate costs
      const fabricCosts = garmentDetails.reduce((sum, detail) =>
        detail.fabricSelection === 'inhouse' && detail.fabricAmount && detail.fabricPrice
          ? sum + detail.fabricAmount * detail.fabricPrice
          : sum, 0);

      const extraChargeTotal = garmentDetails.reduce((sum, detail) => sum + (detail.extraCharge || 0), 0);
      const subtotal = garmentDetails.reduce((sum, detail) => sum + detail.totalPrice, 0) + fabricCosts + extraChargeTotal;

      const orderData = {
        customerName: selectedCustomer?.name || '',
        customer: selectedCustomer?.name || '',
        email: selectedCustomer?.email || '',
        phone: selectedCustomer?.phone || '',
        date: new Date().toISOString(),
        dueDate,
        status: 'pending' as const,
        orderType: 'custom' as const,
        total: totalPrice,
        paidAmount: parseFloat(advancePayment) || 0,
        subtotal,
        discountAmount: discount.amount,
        fabricCosts,
        items: garmentDetails.map(detail => ({
          id: detail.id,
          orderId: '',
          productId: detail.templateId,
          productName: detail.garmentType,
          quantity: detail.quantity,
          price: detail.unitPrice,
          total: detail.totalPrice,
        })),
        garmentDetails,
        discount,
        notes: garmentDetails.map(d => d.notes).filter(Boolean).join('; '),
      };

      // Save order using context
      if (isEditing) {
        // Update existing order
        const updatedOrder = {
          ...editingOrder,
          ...orderData,
          updatedAt: new Date().toISOString(),
        };
        await actions.updateOrder(updatedOrder);
        LoggingService.info('Order updated successfully', 'CREATE_ORDER', {
          orderId: updatedOrder.id,
          itemCount: orderItems.length,
          totalAmount: orderData.total,
        });
        showSuccess('Order updated successfully!');
      } else {
        const savedOrder = await actions.addOrder(orderData as any);
        LoggingService.info('Order created successfully', 'CREATE_ORDER', {
          orderId: savedOrder.id,
          itemCount: orderItems.length,
          totalAmount: orderData.total,
        });
        showSuccess('Order created successfully!');
      }

      // Navigate back after a short delay to show success message
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      LoggingService.error('Error saving order', 'CREATE_ORDER_SCREEN', error as Error);
      showError('Failed to save order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, garmentDetails, garmentType, price, selectedCustomer, advancePayment, dueDate, discount, isEditing, editingOrder, actions, navigation, totalPrice, showSuccess, showError]);

  // Handle save action from AppBar
  const handleSaveAction = useCallback(() => {
    handleSubmit();
  }, [handleSubmit]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={isEditing ? 'Edit Order' : 'Create Order'}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            text: 'Save',
            onPress: handleSaveAction,
            color: theme.colors.primary,
          },
        ]}
        
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {!selectedCustomer ? (
          <TouchableOpacity onPress={handleCustomerPress} activeOpacity={0.7}>
            <TextInput
              label='Select Customer'
              value=''
              onChangeText={() => { }} // Read-only, handled by bottom sheet
              style={styles.input}
              placeholder='Choose customer'
              editable={false}
              rightIcon='chevron-down'
              error={!!errors.customer}
            />
          </TouchableOpacity>
        ) : (
          <View style={styles.customerContainer}>
            <View style={styles.customerCardContainer}>
              <DataCard
                id={selectedCustomer.id}
                name={selectedCustomer.name}
                phone={selectedCustomer.phone}
                cardType='customer'
                onPress={() => { }}
                onCheckboxPress={() => { }}
                selected={false}
                showSelectionIndicator={false}
              />
              <View style={styles.clearCustomerButton}>
                <TouchableOpacity
                  onPress={handleClearCustomer}
                  accessibilityLabel='Clear selected customer'
                  style={styles.clearCustomerTouchable}
                  hitSlop={{ top: 8, right: 8, bottom: 8, left: 8 }}
                >
                  <PhosphorIcon name='x' size={20} color={theme.colors.onSurfaceVariant} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        <TouchableOpacity onPress={handleGarmentTypePress} activeOpacity={0.7}>
          <TextInput
            label='Select Garment Type'
            value={garmentType}
            onChangeText={() => { }} // Read-only, handled by bottom sheet
            style={styles.input}
            placeholder='Choose garment type'
            editable={false}
            rightIcon='chevron-down'
            error={!!errors.garmentType}
          />
        </TouchableOpacity>

        {errors.garmentType && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {errors.garmentType}
          </Text>
        )}

        {/* Garment Measurement Sections - Optimized Implementation */}
        {useMemo(() => garmentDetails.map(detail => {
          const template = selectedTemplates.find(t => t.id === detail.templateId);
          const measurementFields = template?.measurementFields || [];
          const isExpanded = expandedSections[detail.id] !== false; // Default to true
          const validationErrors = garmentValidationErrors[detail.id] || {};

          return (
            <View key={detail.id} style={[styles.garmentSection, { backgroundColor: theme.colors.surface }]}>
              {/* Header */}
              <View style={styles.garmentHeader}>
                <TouchableOpacity
                  style={styles.garmentHeaderLeft}
                  onPress={() => setExpandedSections(prev => ({ ...prev, [detail.id]: !isExpanded }))}
                  activeOpacity={0.7}
                >
                  <PhosphorIcon
                    name={isExpanded ? 'caret-down' : 'caret-right'}
                    size={20}
                    color={theme.colors.onSurface}
                  />
                  <Text style={[styles.garmentTitle, { color: theme.colors.onSurface }]}>
                    {detail.garmentType}
                  </Text>
                  {detail.isUrgent && (
                    <View style={[styles.urgentBadge, { backgroundColor: theme.colors.error }]}>
                      <Text style={[styles.urgentText, { color: theme.colors.onError }]}>URGENT</Text>
                    </View>
                  )}
                </TouchableOpacity>

                <View style={styles.garmentHeaderRight}>
                  {/* Inline Quantity Control */}
                  <View style={styles.quantityControl}>
                    <TouchableOpacity
                      style={[styles.quantityButton, { backgroundColor: theme.colors.surface }]}
                      onPress={() => handleGarmentDetailUpdate(detail.id, { quantity: Math.max(1, detail.quantity - 1) })}
                      activeOpacity={0.7}
                    >
                      <PhosphorIcon name='minus' size={16} color={theme.colors.onSurface} />
                    </TouchableOpacity>

                    <Text style={[styles.quantityText, { color: theme.colors.onSurface }]}>
                      {detail.quantity}
                    </Text>

                    <TouchableOpacity
                      style={[styles.quantityButton, { backgroundColor: theme.colors.surface }]}
                      onPress={() => handleGarmentDetailUpdate(detail.id, { quantity: detail.quantity + 1 })}
                      activeOpacity={0.7}
                    >
                      <PhosphorIcon name='plus' size={16} color={theme.colors.onSurface} />
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.deleteButton, { backgroundColor: theme.colors.errorContainer }]}
                      onPress={() => handleRemoveGarmentDetail(detail.id)}
                      activeOpacity={0.7}
                    >
                      <PhosphorIcon name='trash' size={16} color={theme.colors.error} />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* Expandable Content */}
              {isExpanded && (
                <View style={styles.garmentContent}>
                  {/* Status Checkboxes */}
                  <View style={styles.statusSection}>
                    <View style={styles.checkboxRow}>
                      <Checkbox
                        status={detail.isUrgent ? 'checked' : 'unchecked'}
                        onPress={() => handleGarmentDetailUpdate(detail.id, {
                          isUrgent: !detail.isUrgent,
                          sampleGiven: detail.sampleGiven
                        })}
                        color={theme.colors.primary}
                      />
                      <Text style={[styles.checkboxLabel, { color: theme.colors.onSurface }]}>
                        Urgent Order
                      </Text>
                    </View>
                    <View style={styles.checkboxRow}>
                      <Checkbox
                        status={detail.sampleGiven ? 'checked' : 'unchecked'}
                        onPress={() => handleGarmentDetailUpdate(detail.id, {
                          isUrgent: detail.isUrgent,
                          sampleGiven: !detail.sampleGiven
                        })}
                        color={theme.colors.primary}
                      />
                      <Text style={[styles.checkboxLabel, { color: theme.colors.onSurface }]}>
                        Sample Given
                      </Text>
                    </View>
                  </View>

                  {/* Extra Charge for Urgent Orders */}
                  {detail.isUrgent && (
                    <View style={styles.extraChargeSection}>
                      <TextInput
                        label='Extra Charge for Urgent Order'
                        value={!detail.extraCharge || detail.extraCharge === 0 ? '' : detail.extraCharge.toString()}
                        onChangeText={text => handleGarmentDetailUpdate(detail.id, { extraCharge: parseFloat(text) || 0 })}
                        placeholder='Extra Charge'
                        type='number'
                      />
                    </View>
                  )}

                  {/* Measurements */}
                  <View style={styles.measurementsSection}>
                    <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                      Measurements
                    </Text>
                    <View style={styles.measurementGrid}>
                      {measurementFields.map(field => {
                        const fieldLabel = field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1');
                        const fieldValue = detail.measurements[field] || 0;
                        const displayValue = fieldValue === 0 ? '' : fieldValue.toString();

                        return (
                          <View key={field} style={styles.measurementField}>
                            <TextInput
                              label={`${fieldLabel} (cm)`}
                              value={displayValue}
                              onChangeText={text => {
                                const numericValue = text === '' ? 0 : parseFloat(text) || 0;
                                handleMeasurementChange(detail.id, field, numericValue);
                              }}
                              placeholder={`Enter ${fieldLabel.toLowerCase()}`}
                              error={validationErrors.measurements?.[field]}
                              type='number'
                            />
                          </View>
                        );
                      })}
                    </View>
                  </View>

                  {/* Fabric Selection */}
                  <View style={styles.fabricSection}>
                    <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Fabric Source</Text>

                    <View style={styles.radioGroup}>
                      <TouchableOpacity
                        style={styles.radioOption}
                        onPress={() => handleGarmentDetailUpdate(detail.id, { fabricSelection: 'inhouse' })}
                        activeOpacity={0.7}
                      >
                        <View style={styles.radioButton}>
                          {detail.fabricSelection === 'inhouse' && (
                            <View style={[styles.radioButtonInner, { backgroundColor: theme.colors.primary }]} />
                          )}
                        </View>
                        <Text style={[styles.radioLabel, { color: theme.colors.onSurface }]}>In-house Fabric</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.radioOption}
                        onPress={() => handleGarmentDetailUpdate(detail.id, { fabricSelection: 'customer' })}
                        activeOpacity={0.7}
                      >
                        <View style={styles.radioButton}>
                          {detail.fabricSelection === 'customer' && (
                            <View style={[styles.radioButtonInner, { backgroundColor: theme.colors.primary }]} />
                          )}
                        </View>
                        <Text style={[styles.radioLabel, { color: theme.colors.onSurface }]}>Customer Provided</Text>
                      </TouchableOpacity>
                    </View>

                    {/* Fabric Details for In-house */}
                    {detail.fabricSelection === 'inhouse' && (
                      <View style={styles.fabricDetails}>
                        <View style={styles.row}>
                          <View style={styles.halfWidth}>
                            <TextInput
                              label='Amount (meters)'
                              value={!detail.fabricAmount || detail.fabricAmount === 0 ? '' : detail.fabricAmount.toString()}
                              onChangeText={text => {
                                const amount = parseFloat(text) || 0;
                                handleGarmentDetailUpdate(detail.id, { fabricAmount: amount });
                              }}
                              placeholder='0.0'
                              error={validationErrors.fabricAmount}
                              type='number'
                            />
                          </View>
                          <View style={styles.halfWidth}>
                            <TextInput
                              label='Price per meter'
                              value={!detail.fabricPrice || detail.fabricPrice === 0 ? '' : detail.fabricPrice.toString()}
                              onChangeText={text => {
                                const price = parseFloat(text) || 0;
                                handleGarmentDetailUpdate(detail.id, { fabricPrice: price });
                              }}
                              placeholder='0.00'
                              error={validationErrors.fabricPrice}
                              type='number'
                            />
                          </View>
                        </View>
                      </View>
                    )}
                  </View>

                  {/* Notes */}
                  <View style={styles.notesSection}>
                    <TextInput
                      label='Notes'
                      value={detail.notes}
                      onChangeText={notes => handleGarmentDetailUpdate(detail.id, { notes })}
                      type='textarea'
                      placeholder='Add instructions or notes...'
                      multiline
                      numberOfLines={3}
                      style={styles.notesInput}
                    />
                  </View>

                  {/* Sample Images */}
                  <View style={styles.imageSection}>
                    <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                      Sample Images
                    </Text>
                    <View style={styles.imageGrid}>
                      {detail.images.map((imageUri, index) => (
                        <View key={index} style={styles.imageContainer}>
                          <ImagePicker
                            currentImage={imageUri}
                            onImageSelected={() => { }}
                            size='medium'
                            placeholder='Sample'
                          />
                          <TouchableOpacity
                            style={styles.imageRemoveButton}
                            onPress={() => {
                              const newImages = detail.images.filter((_, i) => i !== index);
                              handleGarmentDetailUpdate(detail.id, { images: newImages });
                            }}
                            activeOpacity={0.7}
                          >
                            <PhosphorIcon name='x' size={16} color={theme.colors.error} />
                          </TouchableOpacity>
                        </View>
                      ))}
                      {detail.images.length < 5 && (
                        <ImagePicker
                          currentImage={null}
                          onImageSelected={(imageUri) => {
                            try {
                              if (imageUri) {
                                handleGarmentDetailUpdate(detail.id, { images: [...detail.images, imageUri] });
                              }
                            } catch (error) {
                              LoggingService.error('Error adding image', 'CREATE_ORDER_SCREEN', error as Error);
                              showError('Failed to add image. Please try again.');
                            }
                          }}
                          size='medium'
                          placeholder='Add Image'
                        />
                      )}
                    </View>
                  </View>
                </View>
              )}
            </View>
          );
        }), [garmentDetails, selectedTemplates, expandedSections, garmentValidationErrors, theme.colors, handleGarmentDetailUpdate, handleMeasurementChange, handleRemoveGarmentDetail])}

        {/* Due Date */}
        <TouchableOpacity onPress={handleDueDatePress} activeOpacity={0.7}>
          <TextInput
            label='Due Date'
            value={dueDate}
            onChangeText={() => { }} // Read-only, handled by calendar
            
            style={styles.input}
            placeholder='Select Due Date'
            editable={false}
            rightIcon='calendar'
          />
        </TouchableOpacity>
        {errors.dueDate && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.dueDate}</Text>
        )}

        {/* Price and Payment - Moved to bottom */}
        <PaperText
          variant='titleMedium'
          style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
        >
          Price & Payment
        </PaperText>

        <View style={styles.row}>
          <View style={styles.halfWidth}>
            <TextInput
              label='Total'
              value={price}
              onChangeText={setPrice}
              style={styles.input}
              type='number'
              error={!!errors.price}
            />
          </View>
          <View style={styles.halfWidth}>
            <TextInput
              label='Advance'
              value={advancePayment}
              onChangeText={setAdvancePayment}
              style={styles.input}
              type='number'
            />
          </View>
        </View>

        {/* Discount Section */}
        <TextInput
          label='Discount Amount'
          value={discount.amount === 0 ? '' : discount.amount.toString()}
          onChangeText={text => setDiscount(prev => ({ ...prev, amount: parseFloat(text) || 0 }))}
          style={styles.input}
          type='number'
          placeholder='Discount'
          error={!!errors.discount}
        />

        {errors.price && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.price}</Text>
        )}

        {errors.discount && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.discount}</Text>
        )}

        {/* Enhanced Payment Summary - Inline Implementation */}
        <View style={[styles.priceSummaryContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.priceSummaryTitle, { color: theme.colors.onSurface }]}>Order Summary</Text>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>Garment Total:</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
              {formatCurrency(garmentDetails.reduce((sum, detail) => sum + detail.totalPrice, 0))}
            </Text>
          </View>

          {useMemo(() => {
            const fabricCosts = garmentDetails.reduce((sum, detail) =>
              detail.fabricSelection === 'inhouse' && detail.fabricAmount && detail.fabricPrice
                ? sum + detail.fabricAmount * detail.fabricPrice
                : sum, 0);

            return fabricCosts > 0 ? (
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Fabric Costs:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  {formatCurrency(fabricCosts)}
                </Text>
              </View>
            ) : null;
          }, [garmentDetails, theme.colors])}

          {useMemo(() => {
            const extraCharges = garmentDetails.reduce((sum, detail) => sum + (detail.extraCharge || 0), 0);

            return extraCharges > 0 ? (
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Extra Charges:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  {formatCurrency(extraCharges)}
                </Text>
              </View>
            ) : null;
          }, [garmentDetails, theme.colors])}

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>Subtotal:</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
              {formatCurrency(totalPrice + discount.amount)}
            </Text>
          </View>

          {discount.amount > 0 && (
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.tertiary }]}>
                Discount (Amount):
              </Text>
              <Text style={[styles.summaryValue, styles.discountValue, { color: theme.colors.tertiary }]}>
                -{formatCurrency(discount.amount)}
              </Text>
            </View>
          )}

          <View style={[styles.summaryDivider, { backgroundColor: theme.colors.outline }]} />

          <View style={styles.summaryRow}>
            <Text style={[styles.totalLabel, { color: theme.colors.onSurface }]}>Total Amount:</Text>
            <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
              {formatCurrency(totalPrice)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>
              Advance Payment:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
              {formatCurrency(parseFloat(advancePayment) || 0)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.remainingLabel, { color: theme.colors.onSurface }]}>
              Remaining Amount:
            </Text>
            <Text style={[styles.remainingValue, { color: theme.colors.tertiary }]}>
              {formatCurrency(remainingAmount)}
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            variant='outline'
            onPress={() => navigation.goBack()}
            style={{ ...styles.button, ...styles.cancelButton }}
          >
            Cancel
          </Button>

          <Button
            variant='primary'
            onPress={handleSubmit}
            style={{ ...styles.button, ...styles.saveButton }}
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {isEditing ? 'Update Order' : 'Create Order'}
          </Button>
        </View>
      </ScrollView>

      {/* Bottom Sheets */}
      {showTimePeriodSheet && (
        <TimePeriodBottomSheet
          ref={timePeriodBottomSheetRef}
          mode='single'
          onDateSelect={handleDateSelect}
          selectedDate={
            dueDate
              ? (() => {
                try {
                  return new Date(dueDate);
                } catch (error) {
                  return undefined;
                }
              })()
              : undefined
          }
          onClose={() => setShowTimePeriodSheet(false)}
          onApply={() => { }}
        />
      )}

      {showGarmentSheet && (
        <GarmentTypeSelectionBottomSheet
          ref={garmentTypeBottomSheetRef}
          onSelect={handleGarmentTypeSelect}
          onClose={() => setShowGarmentSheet(false)}
        />
      )}

      {showCustomerSheet && (
        <CustomerSelectionBottomSheet
          ref={customerSelectionBottomSheetRef}
          onSelect={handleCustomerSelect}
          onClose={() => setShowCustomerSheet(false)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1877F2',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 0,
  },

  // Garment Section Styles
  garmentSection: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  garmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  garmentHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  garmentHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  garmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  urgentBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  urgentText: {
    fontSize: 10,
    fontWeight: '700',
  },
  garmentContent: {
    padding: 16,
  },
  statusSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  measurementsSection: {
    marginBottom: 16,
  },
  measurementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  measurementField: {
    width: '48%',
    minWidth: 150,
  },
  extraChargeSection: {
    marginBottom: 16,
  },
  fabricSection: {
    marginBottom: 16,
  },
  radioGroup: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
    paddingVertical: 4,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  radioLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  fabricDetails: {
    padding: 12,
    borderRadius: 8,
  },
  notesSection: {
    marginBottom: 16,
  },
  notesInput: {
    minHeight: 80,
  },
  imageSection: {
    marginBottom: 12,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  imageContainer: {
    position: 'relative',
  },
  imageRemoveButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  // Price Summary Styles
  priceSummaryContainer: {
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  priceSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  discountValue: {
    fontWeight: '700',
  },
  summaryDivider: {
    height: 1,
    marginVertical: 12,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '700',
  },
  remainingLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  remainingValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 24,
    textAlign: 'center',
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  discountTypeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  discountTypeButtonActive: {
    backgroundColor: '#E3F2FD',
  },
  discountTypeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Quantity Control Styles
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  // Customer selection styles
  customerContainer: {
    marginBottom: 12,
  },
  customerCardContainer: {
    position: 'relative',
  },
  clearCustomerButton: {
    position: 'absolute',
    right: 8,
    top: 8,
    justifyContent: 'center',
  },
  clearCustomerTouchable: {
    padding: 8,
  },
});

export default CreateOrderScreen;
