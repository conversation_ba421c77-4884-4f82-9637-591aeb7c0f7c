/**
 * Warehouse Management Screen
 * Manage warehouses with CRUD operations and stock summaries
 */

import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import {
  <PERSON>ton,
  Card,
  FAB,
  HelperText,
  IconButton,
  Modal,
  Portal,
  Switch,
  Text,
  TextInput,
} from 'react-native-paper';

// Removed unused StockLevelIndicator import
import Header from '@/components/navigation/Header';

import ActionSheet from '@/components/ui/ActionSheet';
import { ActionSheetOption } from '@/types';
import { useData } from '../../context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import LoggingService from '@/services/LoggingService';
import { SPACING } from '@/theme/theme';
import { Warehouse } from '@/types/inventory';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

interface WarehouseManagementScreenProps {
  navigation: any;
}

interface WarehouseFormData {
  name: string;
  location: string;
  isDefault: boolean;
  isActive: boolean;
}

interface WarehouseFormErrors {
  name?: string;
  location?: string;
  general?: string;
}

interface WarehouseWithStock extends Warehouse {
  stockSummary?: {
    totalItems: number;
    lowStockItems: number;
    totalValue: number;
  };
}

const WarehouseManagementScreen: React.FC<WarehouseManagementScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  
  const state = {
    warehouses: dataState.warehouses || [],
    isLoading: dataState.loading,
    error: dataState.error,
  };
  const loadWarehouses = dataActions.loadWarehouses;
  const addWarehouse = dataActions.addWarehouse;
  const updateWarehouse = dataActions.updateWarehouse;
  const getStockByWarehouse = dataActions.getStockByWarehouse;
  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null);
  const [formData, setFormData] = useState<WarehouseFormData>({
    name: '',
    location: '',
    isDefault: false,
    isActive: true,
  });
  const [formErrors, setFormErrors] = useState<WarehouseFormErrors>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [warehousesWithStock, setWarehousesWithStock] = useState<WarehouseWithStock[]>([]);

  // ActionSheet state
  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>('');

  // Load warehouses on mount
  useEffect(() => {
    loadWarehouses();
  }, [loadWarehouses]);

  // Load stock summaries for warehouses
  useEffect(() => {
    const loadStockSummaries = async () => {
      const warehousesWithStockData: WarehouseWithStock[] = [];

      for (const warehouse of state.warehouses) {
        try {
          const stockData = await getStockByWarehouse(warehouse.id);
          let totalValue = 0;
          let lowStockItems = 0;

          for (const stock of stockData) {
            const item = await dataActions.getItemById(stock.itemId);
            if (item) {
              totalValue += stock.quantity * item.sellingPrice;
              if (stock.quantity <= item.minimumStockLevel) {
                lowStockItems++;
              }
            }
          }

          const stockSummary = {
            totalItems: stockData.length,
            lowStockItems: lowStockItems,
            totalValue: totalValue,
          };

          warehousesWithStockData.push({
            ...warehouse,
            stockSummary,
          });
        } catch (error) {
          LoggingService.error(
            'Failed to load stock summary',
            'WAREHOUSE_MANAGEMENT',
            error as Error
          );
          warehousesWithStockData.push(warehouse);
        }
      }

      setWarehousesWithStock(warehousesWithStockData);
    };

    if (state.warehouses.length > 0) {
      loadStockSummaries();
    }
  }, [state.warehouses, getStockByWarehouse]);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      location: '',
      isDefault: false,
      isActive: true,
    });
    setFormErrors({});
    setEditingWarehouse(null);
  }, []);

  // Open add modal
  const handleAddWarehouse = useCallback(() => {
    resetForm();
    setShowAddModal(true);
  }, [resetForm]);

  // Open edit modal
  const handleEditWarehouse = useCallback((warehouse: Warehouse) => {
    setFormData({
      name: warehouse.name,
      location: warehouse.location,
      isDefault: warehouse.isDefault,
      isActive: warehouse.isActive,
    });
    setEditingWarehouse(warehouse);
    setShowAddModal(true);
  }, []);

  // Close modal
  const handleCloseModal = useCallback(() => {
    setShowAddModal(false);
    resetForm();
  }, [resetForm]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const errors: WarehouseFormErrors = {};

    if (!formData.name.trim()) {
      errors.name = 'Warehouse name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Warehouse name must be at least 2 characters';
    }

    if (!formData.location.trim()) {
      errors.location = 'Location is required';
    } else if (formData.location.trim().length < 3) {
      errors.location = 'Location must be at least 3 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setFormErrors({});

    try {
      const warehouseData = {
        name: formData.name.trim(),
        location: formData.location.trim(),
        isDefault: formData.isDefault,
        isActive: formData.isActive,
      };

      if (editingWarehouse) {
        await updateWarehouse(editingWarehouse.id, warehouseData);
        LoggingService.info(`Warehouse updated: ${warehouseData.name}`, 'WAREHOUSE_MANAGEMENT');
        setSuccessMessage('Warehouse updated successfully');
        setShowSuccessActionSheet(true);
      } else {
        await addWarehouse(warehouseData);
        LoggingService.info(`Warehouse created: ${warehouseData.name}`, 'WAREHOUSE_MANAGEMENT');
        setSuccessMessage('Warehouse created successfully');
        setShowSuccessActionSheet(true);
      }

      handleCloseModal();
    } catch (error) {
      LoggingService.error(
        `Failed to ${editingWarehouse ? 'update' : 'create'} warehouse`,
        'WAREHOUSE_MANAGEMENT',
        error as Error
      );
      setFormErrors({ general: (error as Error).message });
    } finally {
      setLoading(false);
    }
  }, [formData, validateForm, editingWarehouse, addWarehouse, updateWarehouse, handleCloseModal]);

  // Handle warehouse deletion
  const handleDeleteWarehouse = useCallback(
    (warehouse: Warehouse) => {
      showConfirmation(
        'Delete Warehouse',
        `Are you sure you want to delete "${warehouse.name}"? This action cannot be undone.`,
        async () => {
          try {
            // Check if warehouse has stock
            const stockData = await getStockByWarehouse(warehouse.id);
            if (stockData.length > 0) {
              showConfirmation(
                'Cannot Delete',
                'This warehouse contains inventory items. Please move or remove all items before deleting.',
                () => {},
                { confirmText: 'OK', type: 'warning' }
              );
              return;
            }

            // For now, we'll just mark as inactive since we don't have a delete method
            await updateWarehouse(warehouse.id, { isActive: false });
            LoggingService.info(`Warehouse deactivated: ${warehouse.name}`, 'WAREHOUSE_MANAGEMENT');
            showConfirmation('Success', 'Warehouse has been deactivated', () => {}, {
              confirmText: 'OK',
              type: 'info',
            });
          } catch (error) {
            LoggingService.error(
              'Failed to delete warehouse',
              'WAREHOUSE_MANAGEMENT',
              error as Error
            );
            showConfirmation('Error', 'Failed to delete warehouse', () => {}, {
              confirmText: 'OK',
              type: 'warning',
            });
          }
        },
        {
          confirmText: 'Delete',
          cancelText: 'Cancel',
          type: 'danger',
        }
      );
    },
    [getStockByWarehouse, updateWarehouse, showConfirmation]
  );

  // Navigate to warehouse stock details
  const handleViewStock = useCallback((warehouse: Warehouse) => {
    navigation.navigate('WarehouseStockDetail' as any, { warehouseId: warehouse.id });
  }, []);

  // Render warehouse card
  const renderWarehouseCard = useCallback(
    (warehouse: WarehouseWithStock) => (
      <Card key={warehouse.id} style={styles.warehouseCard} mode='outlined'>
        <Card.Content>
          {/* Header */}
          <View style={styles.warehouseHeader}>
            <View style={styles.warehouseInfo}>
              <View style={styles.warehouseTitleRow}>
                <Text style={[styles.warehouseName, { color: theme.colors.onSurface }]}>
                  {warehouse.name}
                </Text>

                {warehouse.isDefault && (
                  <View
                    style={[
                      styles.defaultBadge,
                      { backgroundColor: theme.colors.primaryContainer },
                    ]}
                  >
                    <Text
                      style={[styles.defaultBadgeText, { color: theme.colors.onPrimaryContainer }]}
                    >
                      DEFAULT
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.locationRow}>
                <PhosphorIcon name='map-pin' size={16} color={theme.colors.onSurfaceVariant} />
                <Text style={[styles.warehouseLocation, { color: theme.colors.onSurfaceVariant }]}>
                  {warehouse.location}
                </Text>
              </View>
            </View>

            <View style={styles.warehouseActions}>
              <IconButton
                icon='pencil'
                size={20}
                onPress={() => handleEditWarehouse(warehouse)}
                iconColor={theme.colors.onSurfaceVariant}
              />
              <IconButton
                icon='delete'
                size={20}
                onPress={() => handleDeleteWarehouse(warehouse)}
                iconColor={theme.colors.error}
              />
            </View>
          </View>

          {/* Stock Summary */}
          {warehouse.stockSummary && (
            <View style={styles.stockSummary}>
              <View style={styles.stockSummaryRow}>
                <View style={styles.stockStat}>
                  <Text style={[styles.stockStatValue, { color: theme.colors.primary }]}>
                    {warehouse.stockSummary.totalItems}
                  </Text>
                  <Text style={[styles.stockStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Items
                  </Text>
                </View>

                <View style={styles.stockStat}>
                  <Text
                    style={[
                      styles.stockStatValue,
                      { color: warehouse.stockSummary.lowStockItems > 0 ? '#EF4444' : '#10B981' },
                    ]}
                  >
                    {warehouse.stockSummary.lowStockItems}
                  </Text>
                  <Text style={[styles.stockStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Low Stock
                  </Text>
                </View>

                <View style={styles.stockStat}>
                  <Text style={[styles.stockStatValue, { color: theme.colors.tertiary }]}>
                    {formatCurrency(warehouse.stockSummary.totalValue, { decimals: 0 })}
                  </Text>
                  <Text style={[styles.stockStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Value
                  </Text>
                </View>
              </View>

              <Button
                mode='outlined'
                onPress={() => handleViewStock(warehouse)}
                style={styles.viewStockButton}
                icon='package-variant'
                compact
              >
                View Stock Details
              </Button>
            </View>
          )}

          {/* Status Indicator */}
          <View style={styles.statusRow}>
            <View style={styles.statusIndicator}>
              <View
                style={[
                  styles.statusDot,
                  { backgroundColor: warehouse.isActive ? '#10B981' : '#EF4444' },
                ]}
              />
              <Text style={[styles.statusText, { color: theme.colors.onSurfaceVariant }]}>
                {warehouse.isActive ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    ),
    [theme.colors, handleEditWarehouse, handleDeleteWarehouse, handleViewStock]
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Warehouse Management'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'plus',
            onPress: handleAddWarehouse,
          },
        ]}
      />

      <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Summary Stats */}
          <Card style={styles.summaryCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.summaryTitle, { color: theme.colors.onSurface }]}>
                Warehouse Overview
              </Text>

              <View style={styles.summaryStats}>
                <View style={styles.summaryStat}>
                  <Text style={[styles.summaryStatValue, { color: theme.colors.primary }]}>
                    {state.warehouses.length}
                  </Text>
                  <Text style={[styles.summaryStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Total Warehouses
                  </Text>
                </View>

                <View style={styles.summaryStat}>
                  <Text style={[styles.summaryStatValue, { color: theme.colors.primary }]}>
                    {state.warehouses.filter((w: Warehouse) => w.isActive).length}
                  </Text>
                  <Text style={[styles.summaryStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Active
                  </Text>
                </View>

                <View style={styles.summaryStat}>
                  <Text style={[styles.summaryStatValue, { color: theme.colors.secondary }]}>
                    {state.warehouses.filter(w => w.isDefault).length}
                  </Text>
                  <Text style={[styles.summaryStatLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Default
                  </Text>
                </View>
              </View>
            </Card.Content>
          </Card>

          {/* Warehouses List */}
          {warehousesWithStock.length > 0 ? (
            <View style={styles.warehousesList}>
              {warehousesWithStock.map(renderWarehouseCard)}
            </View>
          ) : (
            <Card style={styles.emptyStateCard} mode='outlined'>
              <Card.Content style={styles.emptyStateContent}>
                <PhosphorIcon name='warehouse' size={64} color={theme.colors.onSurfaceVariant} />
                <Text style={[styles.emptyStateTitle, { color: theme.colors.onSurface }]}>
                  No Warehouses
                </Text>
                <Text style={[styles.emptyStateMessage, { color: theme.colors.onSurfaceVariant }]}>
                  Create your first warehouse to start managing inventory locations.
                </Text>
                <Button
                  mode='contained'
                  onPress={handleAddWarehouse}
                  style={styles.emptyStateButton}
                  icon='plus'
                >
                  Add First Warehouse
                </Button>
              </Card.Content>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Add/Edit Warehouse Modal */}
      <Portal>
        <Modal
          visible={showAddModal}
          onDismiss={handleCloseModal}
          contentContainerStyle={[styles.modalContainer, { backgroundColor: theme.colors.surface }]}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
              {editingWarehouse ? 'Edit Warehouse' : 'Add New Warehouse'}
            </Text>
            <IconButton
              icon='close'
              size={24}
              onPress={handleCloseModal}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Warehouse Name */}
            <TextInput
              label='Warehouse Name *'
              value={formData.name}
              onChangeText={text => setFormData(prev => ({ ...prev, name: text }))}
              mode='outlined'
              error={!!formErrors.name}
              style={styles.modalInput}
              placeholder='Enter warehouse name'
            />
            {formErrors.name && <HelperText type='error'>{formErrors.name}</HelperText>}

            {/* Location */}
            <TextInput
              label='Location *'
              value={formData.location}
              onChangeText={text => setFormData(prev => ({ ...prev, location: text }))}
              mode='outlined'
              error={!!formErrors.location}
              style={styles.modalInput}
              placeholder='Enter warehouse location'
              multiline
              numberOfLines={2}
            />
            {formErrors.location && <HelperText type='error'>{formErrors.location}</HelperText>}

            {/* Default Warehouse Switch */}
            <View style={styles.switchRow}>
              <View style={styles.switchInfo}>
                <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>
                  Default Warehouse
                </Text>
                <Text style={[styles.switchDescription, { color: theme.colors.onSurfaceVariant }]}>
                  New items will be assigned to this warehouse by default
                </Text>
              </View>
              <Switch
                value={formData.isDefault}
                onValueChange={value => setFormData(prev => ({ ...prev, isDefault: value }))}
              />
            </View>

            {/* Active Switch */}
            <View style={styles.switchRow}>
              <View style={styles.switchInfo}>
                <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>Active</Text>
                <Text style={[styles.switchDescription, { color: theme.colors.onSurfaceVariant }]}>
                  Inactive warehouses won&apos;t appear in selections
                </Text>
              </View>
              <Switch
                value={formData.isActive}
                onValueChange={value => setFormData(prev => ({ ...prev, isActive: value }))}
              />
            </View>

            {/* General Error */}
            {formErrors.general && (
              <HelperText type='error' style={styles.generalError}>
                {formErrors.general}
              </HelperText>
            )}
          </ScrollView>

          {/* Modal Actions */}
          <View style={styles.modalActions}>
            <Button
              mode='outlined'
              onPress={handleCloseModal}
              style={styles.modalCancelButton}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              mode='contained'
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              style={styles.modalSaveButton}
            >
              {editingWarehouse ? 'Update' : 'Create'}
            </Button>
          </View>
        </Modal>

        {/* ActionSheet for success messages */}
        <ActionSheet
          visible={showSuccessActionSheet}
          onDismiss={() => setShowSuccessActionSheet(false)}
          title='Success'
          description={successMessage}
          options={[{ text: 'OK', onPress: () => setShowSuccessActionSheet(false), isAction: true }]}
        />
      </Portal>

      {/* Floating Action Button */}
      <FAB
        icon='plus'
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddWarehouse}
        label='Add Warehouse'
      />

      {/* Confirmation Dialog */}
      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
    paddingBottom: 100, // Space for FAB
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryStat: {
    alignItems: 'center',
  },
  summaryStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  summaryStatLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  warehousesList: {
    gap: 12,
  },
  warehouseCard: {
    marginVertical: 4,
  },
  warehouseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  warehouseInfo: {
    flex: 1,
  },
  warehouseTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  warehouseName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  warehouseLocation: {
    fontSize: 14,
  },
  warehouseActions: {
    flexDirection: 'row',
  },
  stockSummary: {
    marginBottom: 16,
  },
  stockSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  stockStat: {
    alignItems: 'center',
  },
  stockStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  stockStatLabel: {
    fontSize: 11,
    marginTop: 2,
  },
  viewStockButton: {
    alignSelf: 'center',
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
  },
  emptyStateCard: {
    marginVertical: 32,
  },
  emptyStateContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  emptyStateButton: {
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  // Modal styles
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalContent: {
    paddingHorizontal: 20,
    maxHeight: 400,
  },
  modalInput: {
    marginVertical: 4,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  switchDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  generalError: {
    textAlign: 'center',
    marginVertical: 8,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingTop: 16,
  },
  modalCancelButton: {
    flex: 1,
  },
  modalSaveButton: {
    flex: 1,
  },
});

export default WarehouseManagementScreen;
