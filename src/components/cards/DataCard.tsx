import React, { useMemo, useCallback, memo } from 'react';
import { View, StyleSheet, TouchableOpacity, Pressable, Platform, StyleProp, TextStyle } from 'react-native';
import { Text, Surface } from 'react-native-paper';
import { useTheme } from '@/context/ThemeContext';
import { PhosphorIcon, PhosphorIconName } from '@/utils/phosphorIconRegistry';

// --- Type Definitions ---
type Theme = ReturnType<typeof useTheme>;

interface DataCardProps {
  id: string | number;
  name: string;
  phone?: string;
  email?: string;
  price?: number;
  measurementFields?: string[];
  category?: string;
  totalSpent?: number;
  totalOrders?: number;
  activeOrders?: number;
  isVIP?: boolean;
  date?: string;
  createdAt?: string;
  lastOrderDate?: Date | null;
  isPopular?: boolean;
  isCustom?: boolean;
  cardType: 'customer' | 'garment';
  onPress: (data: Omit<DataCardProps, 'onPress' | 'onLongPress' | 'onCheckboxPress'>) => void;
  onLongPress?: (data: Omit<DataCardProps, 'onPress' | 'onLongPress' | 'onCheckboxPress'>) => void;
  onCheckboxPress: () => void;
  selected?: boolean;
  showSelectionIndicator?: boolean;
  searchQuery?: string;
  isInBottomSheet?: boolean;
}

type CardData = {
  leftText: string;
  rightParts: { text: string; isSpecial: boolean }[];
};

// --- Custom Hooks for Logic Separation ---

function useCardData(props: DataCardProps): CardData {
  return useMemo(() => {
    if (props.cardType === 'customer') {
      const { totalOrders = 0, activeOrders = 0, phone } = props;
      const rightParts: CardData['rightParts'] = [];
      if (totalOrders > 0) {
        rightParts.push({ text: `${totalOrders} order${totalOrders > 1 ? 's' : ''}`, isSpecial: false });
      }
      if (activeOrders > 0) {
        rightParts.push({ text: `${activeOrders} active`, isSpecial: true });
      }
      return { leftText: phone || '', rightParts };
    } else {
      const { price, measurementFields } = props;
      const priceText = price ? `৳${Number(price).toLocaleString()}` : 'Price not set';
      const measurementsText = measurementFields?.length ? `${measurementFields.length} measurement${measurementFields.length > 1 ? 's' : ''}` : 'No measurements';
      return { leftText: measurementsText, rightParts: [{ text: priceText, isSpecial: false }] };
    }
  }, [props.cardType, props.phone, props.totalOrders, props.activeOrders, props.price, props.measurementFields]);
}

function useStatusBadge(props: DataCardProps, theme: Theme) {
  return useMemo(() => {
    if (props.cardType === 'customer') {
      if (props.isVIP || (props.totalSpent || 0) > 50000) return { text: 'VIP', bg: '#4d443a', color: '#ffb74d' };
      if ((props.totalOrders || 0) === 0) return { text: 'New', bg: theme.colors.primaryContainer, color: theme.colors.onPrimaryContainer };
    } else {
      if (props.isPopular) return { text: 'Popular', bg: '#4d443a', color: '#ffb74d' };
      if (props.isCustom) return { text: 'Custom', bg: theme.colors.primaryContainer, color: theme.colors.onPrimaryContainer };
      if (props.category === 'premium') return { text: 'Premium', bg: '#2d1b69', color: '#d4af37' };
    }
    return null;
  }, [props.cardType, props.isVIP, props.totalSpent, props.totalOrders, props.isPopular, props.isCustom, props.category, theme.colors]);
}

// --- Helper Components ---

const HighlightText = ({ text, searchQuery, highlightStyle, normalStyle }: { text: string; searchQuery?: string; highlightStyle: TextStyle; normalStyle: StyleProp<TextStyle>; }) => {
  if (!searchQuery?.trim()) {
    return <Text style={normalStyle}>{text}</Text>;
  }
  const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'));
  return (
    <Text style={normalStyle}>
      {parts.map((part, i) =>
        part.toLowerCase() === searchQuery.toLowerCase() ? (
          <Text key={i} style={highlightStyle}>{part}</Text>
        ) : (
          part
        )
      )}
    </Text>
  );
};

// --- Main Component ---

const DataCard = memo<DataCardProps>(
  (props) => {
    const { id, name, onCheckboxPress, selected = false, showSelectionIndicator = false, searchQuery = '', isInBottomSheet = false } = props;
    const theme = useTheme();

    const cardData = useCardData(props);
    const statusBadge = useStatusBadge(props, theme);

    const styles = useMemo(() => createCardStyles(theme), [theme]);

    const cardContainerStyle = useMemo(() => [
      styles.card,
      {
        backgroundColor: selected ? theme.colors.primaryContainer : theme.colors.surface,
        borderWidth: 1,
        borderColor: selected ? theme.colors.primary : theme.colors.outlineVariant,
      },
    ], [selected, theme.colors, styles.card]);

    const textHighlightStyle = useMemo(() => ({
      backgroundColor: theme.colors.secondaryContainer,
      color: theme.colors.onSecondaryContainer,
    }), [theme.colors]);

    const handlePress = useCallback(() => {
      const { onPress: _onPress, onLongPress: _onLongPress, onCheckboxPress: _onCheckboxPress, ...rest } = props;
      props.onPress(rest);
    }, [props]);

    const handleLongPress = useCallback(() => {
      if (props.onLongPress) {
        const { onPress: _onPress, onLongPress: _onLongPress, onCheckboxPress: _onCheckboxPress, ...rest } = props;
        props.onLongPress(rest);
      }
    }, [props]);
    
    const CardWrapper = Platform.OS === 'android' ? Pressable : TouchableOpacity;

    return (
      <CardWrapper onPress={handlePress} onLongPress={handleLongPress}>
        <Surface style={cardContainerStyle} elevation={0}>
          <View style={styles.cardContent}>
            {showSelectionIndicator && (
              <TouchableOpacity onPress={onCheckboxPress} style={styles.selectionCheckbox}>
                <PhosphorIcon
                  name={(selected ? 'check-circle' : 'circle') as PhosphorIconName}
                  size={24}
                  color={selected ? theme.colors.primary : theme.colors.outline}
                  weight={selected ? 'fill' : 'regular'}
                />
              </TouchableOpacity>
            )}

            <View style={styles.detailsContainer}>
              <View style={styles.topRow}>
                <HighlightText text={name} searchQuery={searchQuery} highlightStyle={textHighlightStyle} normalStyle={styles.name} />
                {statusBadge && (
                  <View style={[styles.badge, { backgroundColor: statusBadge.bg }]}>
                    <Text style={[styles.badgeText, { color: statusBadge.color }]}>{statusBadge.text}</Text>

                  </View>
                )}
              </View>

              <View style={styles.bottomRow}>
                <HighlightText text={cardData.leftText} searchQuery={searchQuery} highlightStyle={textHighlightStyle} normalStyle={styles.metaText} />
                <Text style={styles.metaText}>
                  {cardData.rightParts.map((part, index) => (
                    <React.Fragment key={index}>
                      <Text style={part.isSpecial && styles.activeOrdersText}>{part.text}</Text>
                      {index < cardData.rightParts.length - 1 && ', '}
                    </React.Fragment>
                  ))}
                </Text>
              </View>
            </View>
          </View>
        </Surface>
      </CardWrapper>
    );
  },
  (prevProps, nextProps) => {
    if ( prevProps.selected !== nextProps.selected || prevProps.searchQuery !== nextProps.searchQuery || prevProps.id !== nextProps.id ) {
      return false; // Re-render if selection or search changes
    }
    // For other props, assume they are stable unless ID changes
    return prevProps.id === nextProps.id;
  }
);

const createCardStyles = (theme: Theme) => StyleSheet.create({
  card: { borderRadius: 12, padding: 12, marginBottom: 8 },
  cardContent: { flexDirection: 'row', alignItems: 'center' },
  detailsContainer: { flex: 1 },
  selectionCheckbox: { marginRight: 12, padding: 4 },
  topRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 6 },
  bottomRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  name: { fontSize: 16, fontWeight: '600', color: theme.colors.onSurface, flexShrink: 1 },
  metaText: { fontSize: 13, color: theme.colors.onSurfaceVariant },
  activeOrdersText: { color: theme.colors.tertiary, fontWeight: '600' },
  badge: { paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12, marginLeft: 8 },
  badgeText: { fontSize: 10, fontWeight: '600' },
});

export default DataCard;