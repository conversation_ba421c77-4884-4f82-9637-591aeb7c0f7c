import React, { useMemo } from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle, Pressable } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// Types remain the same
export type ChipVariant = 'solid' | 'outlined';
export type ChipSize = 'small' | 'medium';
export type ChipBorderRadius = 'semi-rounded' | 'full-rounded';
export interface ChipProps {
  label: string;
  leftIcon?: PhosphorIconName;
  rightIcon?: PhosphorIconName;
  variant?: ChipVariant;
  size?: ChipSize;
  borderRadius?: ChipBorderRadius;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  iconColor?: string;
  selected?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  iconSize?: number;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

// Optimization 1: Moved static configuration outside the component
// This object is now created only once, not on every render.
const sizeConfig = {
  small: {
    height: 28,
    paddingHorizontal: SPACING.sm,
    fontSize: TYPOGRAPHY.fontSize.sm,
    iconSize: 14,
  },
  medium: {
    height: 36,
    paddingHorizontal: SPACING.md,
    fontSize: TYPOGRAPHY.fontSize.md,
    iconSize: 16,
  },
};

const Chip: React.FC<ChipProps> = ({
  label,
  leftIcon,
  rightIcon,
  variant = 'solid',
  size = 'medium',
  borderRadius = 'semi-rounded',
  backgroundColor,
  textColor,
  borderColor,
  iconColor,
  selected = false,
  disabled = false,
  loading = false,
  onPress,
  onLongPress,
  style,
  textStyle,
  iconSize: customIconSize,
  accessibilityLabel,
  accessibilityHint,
}) => {
  const theme = useTheme();
  const currentSize = sizeConfig[size];
  const finalIconSize = customIconSize || currentSize.iconSize;

  // Optimization 2: Memoize color and style calculations
  // These complex objects are now only recalculated when their dependencies change,
  // preventing unnecessary work on unrelated re-renders.
  const { chipColors, chipStyle, textStyle: memoizedTextStyle } = useMemo(() => {
    // Determine Colors
    let resolvedColors;
    if (disabled) {
      resolvedColors = {
        background: theme.colors.surfaceVariant,
        text: theme.colors.onSurfaceVariant,
        border: theme.colors.outlineVariant,
        icon: theme.colors.onSurfaceVariant,
      };
    } else if (selected) {
      resolvedColors = {
        background: backgroundColor || theme.colors.primaryContainer,
        text: textColor || theme.colors.onPrimaryContainer,
        border: borderColor || theme.colors.primary,
        icon: iconColor || theme.colors.onPrimaryContainer,
      };
    } else if (variant === 'solid') {
      resolvedColors = {
        background: backgroundColor || theme.colors.surfaceVariant,
        text: textColor || theme.colors.onSurface,
        border: borderColor || 'transparent',
        icon: iconColor || theme.colors.onSurface,
      };
    } else { // outlined
      resolvedColors = {
        background: backgroundColor || 'transparent',
        text: textColor || theme.colors.onSurface,
        border: borderColor || theme.colors.outline,
        icon: iconColor || theme.colors.onSurface,
      };
    }

    // Determine Border Radius
    const brConfig = {
      'semi-rounded': { small: BORDER_RADIUS.sm, medium: BORDER_RADIUS.md },
      'full-rounded': { small: BORDER_RADIUS.round, medium: BORDER_RADIUS.round },
    };
    const finalBorderRadius = brConfig[borderRadius][size];
    
    // Construct Dynamic Styles
    const finalChipStyle: ViewStyle = {
      height: currentSize.height,
      minWidth: currentSize.height,
      paddingHorizontal: currentSize.paddingHorizontal,
      borderRadius: finalBorderRadius,
      backgroundColor: resolvedColors.background,
      borderWidth: variant === 'outlined' ? 1 : 0,
      borderColor: resolvedColors.border,
      opacity: disabled || loading ? 0.6 : 1,
    };

    const finalTextStyle: TextStyle = {
      fontSize: currentSize.fontSize,
      color: resolvedColors.text,
      fontWeight: selected ? TYPOGRAPHY.fontWeight.bold : TYPOGRAPHY.fontWeight.normal,
      lineHeight: currentSize.fontSize * 1.4, // Improved line height for better centering
    };

    return { chipColors: resolvedColors, chipStyle: finalChipStyle, textStyle: finalTextStyle };
  }, [
    disabled, selected, variant, backgroundColor, textColor, borderColor, iconColor,
    theme, size, borderRadius
  ]);

  // Optimization 3: DRY Principle - Don't Repeat Yourself
  // The chip's inner content is defined once and reused.
  const chipContent = (
    <View style={styles.contentContainer}>
      {leftIcon && (
        <PhosphorIcon
          name={leftIcon}
          size={finalIconSize}
          color={chipColors.icon}
          style={styles.leftIcon}
        />
      )}
      <Text style={[memoizedTextStyle, textStyle]} numberOfLines={1}>
        {label}
      </Text>
      {rightIcon && (
        <PhosphorIcon
          name={rightIcon}
          size={finalIconSize}
          color={chipColors.icon}
          style={styles.rightIcon}
        />
      )}
    </View>
  );

  return (
    <Pressable
      onPress={onPress}
      onLongPress={onLongPress}
      disabled={disabled || loading || !onPress}
      style={({ pressed }) => [
        styles.container,
        chipStyle,
        style,
        pressed && !disabled && { opacity: 0.8 },
      ]}
      accessibilityLabel={accessibilityLabel || label}
      accessibilityHint={accessibilityHint}
      accessibilityRole='button'
      accessibilityState={{ disabled, selected }}
    >
      {chipContent}
    </Pressable>
  );
};

// Optimization 4: Use StyleSheet.create for all static styles.
// This sends the styles over the React Native bridge only once.
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start', // Prevent chip from taking full width
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftIcon: {
    marginRight: SPACING.xs,
  },
  rightIcon: {
    marginLeft: SPACING.xs,
  },
});

export default Chip;