import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import {
  AddCustomerScreen,
  AddEditInventoryItemScreen,
  CreateOrderScreen,
  CustomerDetailsScreen,
  CustomersScreen,
  FinancialScreen,
  GarmentTypeDetailScreen,
  GarmentTypesScreen,
  InventoryDashboardScreen,
  InventoryItemDetailScreen,
  InventoryItemsScreen,
  OrdersScreen,
  ProfitLossScreen,
  ReportsScreen,
  StockOperationsScreen,
  StockTransferScreen,
  TaxSummaryScreen,
  TransactionHistoryScreen,
} from '../screens/business';
import AddFabricScreen from '../screens/business/AddFabricScreen';
import AddItemScreen from '../screens/business/AddItemScreen';
import {
  PaymentMethodsScreen,
  StaffManagementScreen,
  WarehouseManagementScreen,
} from '../screens/management';
import AddStaffScreen from '../screens/management/AddStaffScreen';
import StaffDetailsScreen from '../screens/management/StaffDetailsScreen';
import {
  ActivityLogScreen,
  EditProfileScreen,
  ProfileScreen as MyProfileScreen,
  NotificationsScreen,
} from '../screens/settings';
import {
  AboutScreen,
  AppStatusScreen,
  ContactSupportScreen,
  HelpFAQScreen,
  SearchScreen,
} from '../screens/support';
// Import the consolidated DataScreen from management
import DataScreen from '../screens/management/DataScreen';
import { RootStackParamList } from '../types/navigation';

import TabNavigator from './TabNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const theme = useTheme();
  const { state } = useAuth();

  // Only render if user is authenticated
  if (!state.isAuthenticated || !state.user) {
    return null;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // Completely disable all animations
        cardStyleInterpolator: () => ({
          cardStyle: {
            transform: [],
            opacity: 1,
          },
          overlayStyle: {
            opacity: 0,
          },
        }),
      }}
      initialRouteName='Main'
    >
      {/* Main Tab Navigator */}
      <Stack.Screen
        name='Main'
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />

      {/* Customers Screen */}
      <Stack.Screen
        name='Customers'
        component={CustomersScreen}
        options={{
          headerShown: false,
        }}
      />

      

      {/* Customer Details Screen */}
      <Stack.Screen
        name='CustomerDetails'
        component={CustomerDetailsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Customer Screen */}
      <Stack.Screen
        name='AddCustomer'
        component={AddCustomerScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Reports Screen */}
      <Stack.Screen
        name='Reports'
        component={ReportsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Search Screen */}
      <Stack.Screen
        name='Search'
        component={SearchScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Profile Screen */}
      <Stack.Screen
        name='Profile'
        component={MyProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Edit Profile Screen */}
      <Stack.Screen
        name='EditProfile'
        component={EditProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Notifications Screen */}
      <Stack.Screen
        name='Notifications'
        component={NotificationsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Activity Log Screen */}
      <Stack.Screen
        name='ActivityLog'
        component={ActivityLogScreen}
        options={{
          headerShown: false,
        }}
      />



      {/* Data Management Screen - Consolidated backup, import, export, and sample data */}
      <Stack.Screen
        name='DataManagement'
        component={DataScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Unified Add Item Screen */}
      <Stack.Screen
        name='AddItem'
        component={AddItemScreen}
        options={{
          headerShown: false,
        }}
      />

      

      {/* Add Fabric Screen - still available for specific fabric workflows */}
      <Stack.Screen
        name='AddFabricScreen'
        component={AddFabricScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Orders */}
      <Stack.Screen name='Orders' component={OrdersScreen} options={{ headerShown: false }} />
      {/* Map legacy AddOrder navigations to CreateOrderScreen */}
      <Stack.Screen
        name='AddOrder'
        component={CreateOrderScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='CreateOrder'
        component={CreateOrderScreen}
        options={{ headerShown: false }}
      />

      {/* Garment Types Management */}
      <Stack.Screen
        name='GarmentTypes'
        component={GarmentTypesScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='GarmentTypeDetail'
        component={GarmentTypeDetailScreen}
        options={{ headerShown: false }}
      />

      {/* Financial */}
      <Stack.Screen name='Financial' component={FinancialScreen} options={{ headerShown: false }} />
      <Stack.Screen
        name='ProfitLoss'
        component={ProfitLossScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='TaxSummary'
        component={TaxSummaryScreen}
        options={{ headerShown: false }}
      />

      {/* Inventory Management */}
      <Stack.Screen
        name='InventoryDashboard'
        component={InventoryDashboardScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='AddEditInventoryItem'
        component={AddEditInventoryItemScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='InventoryItemDetail'
        component={InventoryItemDetailScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='StockOperations'
        component={StockOperationsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='StockTransfer'
        component={StockTransferScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='InventoryItems'
        component={InventoryItemsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='TransactionHistory'
        component={TransactionHistoryScreen}
        options={{ headerShown: false }}
      />

      {/* Management */}
      <Stack.Screen
        name='PaymentMethods'
        component={PaymentMethodsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='StaffManagement'
        component={StaffManagementScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen name='AddStaff' component={AddStaffScreen} options={{ headerShown: false }} />
      <Stack.Screen
        name='StaffDetails'
        component={StaffDetailsScreen}
        options={{ headerShown: false }}
      />

      {/* Inventory */}
      <Stack.Screen
        name='WarehouseManagement'
        component={WarehouseManagementScreen}
        options={{ headerShown: false }}
      />

      {/* Utilities - ImportData now redirects to DataManagement */}
      <Stack.Screen
        name='ImportData'
        component={DataScreen}
        options={{ headerShown: false }}
      />

      {/* Support Screens */}
      <Stack.Screen name='HelpFAQ' component={HelpFAQScreen} options={{ headerShown: false }} />
      <Stack.Screen
        name='ContactSupport'
        component={ContactSupportScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen name='About' component={AboutScreen} options={{ headerShown: false }} />
      <Stack.Screen name='AppStatus' component={AppStatusScreen} options={{ headerShown: false }} />

      {/* Database management is now handled in the Settings -> Data Management screen */}

      {/* Form Screens */}
    </Stack.Navigator>
  );
};

export default AppNavigator;
