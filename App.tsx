import React, { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer, createNavigationContainerRef } from '@react-navigation/native';
import { PaperProvider, Portal } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PortalProvider } from '@gorhom/portal';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';

import { createPaperTheme, getFallbackTheme } from './src/theme/paperThemeAdapter';

// Import navigation and contexts
import RootNavigator from './src/navigation/RootNavigator';
import { DataProvider } from './src/context/DataContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { FinancialProvider } from './src/context/FinancialContext';
import { AuthProvider } from './src/context/AuthContext';
import { ToastProvider } from './src/context/ToastContext';
import { NotificationService } from './src/services/notificationService';
import LoggingService from './src/services/LoggingService';

// Import Master Database Service
import masterDb from './src/services/MasterDatabaseService';
import { getConfig } from './src/config/masterDatabase.config';

import ErrorBoundary from './src/components/utils/ErrorBoundary';

// Create navigation ref for global navigation access
const navigationRef = createNavigationContainerRef();

// Root App Component with providers and error boundary
const App: React.FC = () => {

  useEffect(() => {
    if (__DEV__) {
      (async () => {
        try {
          await NotificationService.seedDummyData();
        } catch (error: any) {
          LoggingService.error('Notification seeding failed', 'APP', error);
          if (error.message?.includes('too large') || error.message?.includes('Row too big')) {
            LoggingService.warn('Storage overflow detected, clearing notification data', 'APP');
            try {
              await NotificationService.clearAllNotificationData();
            } catch (clearError: any) {
              LoggingService.error('Failed to clear notification data', 'APP', clearError);
            }
          }
        }
      })();

      const initializeMasterDb = async () => {
        try {
          LoggingService.info('Initializing Master Database Service...', 'MASTER_DB');
          const config = getConfig(__DEV__ ? 'development' : 'production');
          let initSuccess = false;
          let attempts = 0;
          const maxAttempts = 3;

          while (!initSuccess && attempts < maxAttempts) {
            try {
              attempts++;
              LoggingService.info(`Master Database initialization attempt ${attempts}/${maxAttempts}`, 'MASTER_DB');
              await masterDb.initialize({
                databaseName: config.database.name,
                enableCache: config.cache.enabled,
                enableOfflineSync: config.sync.enabled,
                enableMonitoring: config.monitoring.enabled,
                enableAutoBackup: config.backup.enabled,
                cacheSize: config.cache.maxSize,
                cacheTTL: config.cache.defaultTTL,
                syncInterval: config.sync.syncInterval,
                backupInterval: config.backup.autoBackupInterval,
                maxRetries: config.sync.maxRetries,
              });
              initSuccess = true;
              LoggingService.info('Master Database Service initialized successfully', 'MASTER_DB');
              const health = masterDb.getHealth();
              LoggingService.info(`Database Health: Connected=${health.isConnected}, Performance=${health.performanceMetrics.errorRate < 0.1 ? 'Good' : 'Degraded'}`, 'MASTER_DB');
            } catch (initError) {
              LoggingService.warn(`Master Database initialization attempt ${attempts} failed`, 'MASTER_DB', initError as Error);
              if (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
              } else {
                throw initError;
              }
            }
          }
        } catch (error) {
          LoggingService.error('Failed to initialize Master Database Service after all attempts', 'MASTER_DB', error as Error);
          LoggingService.warn('App will continue with AsyncStorage fallback', 'MASTER_DB');
        }
      };
      initializeMasterDb();
    }
  }, []);

  const handleAppError = (): void => {
    LoggingService.info('App error handled', 'APP');
  };

  const handleAppReload = (): void => {
    LoggingService.info('App reload requested', 'APP');
  };

  if (__DEV__) {
    // Icon validation removed - iconValidator was deleted

    // Handle notification seeding more safely
    setTimeout(() => {
      import('./src/services/notificationService')
        .then((mod) => {
          const svc = mod.NotificationService;
          if (!svc) {
            throw new Error('NotificationService module not found');
          }
          return           (svc.seedDummyData() as Promise<void>).catch((error: Error) => {
            LoggingService.error('Notification seeding failed', 'APP', error);
            if (error.message?.includes('too large') || error.message?.includes('Row too big')) {
              LoggingService.warn('Storage overflow detected, clearing notification data', 'APP');
              return svc.clearAllNotificationData().catch((clearError: Error) => {
                LoggingService.error('Failed to clear notification data', 'APP', clearError as Error);
              });
            }
          });
        })
        .catch((error) => {
          LoggingService.error('Failed to import NotificationService', 'APP', error as Error);
        });
    }, 2000); // Delay notification seeding by 2 seconds

    // Initialize Master Database Service with improved error handling
    
  }

  return (
    <ErrorBoundary onRetry={handleAppError} onReload={handleAppReload}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ThemeProvider>
            <AuthProvider>
              <DataProvider>
                    <FinancialProvider orders={[]}>
                      <ToastProvider>
                      <BottomSheetModalProvider>
                        <AppContent />
                      </BottomSheetModalProvider>
                    </ToastProvider>
                  </FinancialProvider>
              </DataProvider>
            </AuthProvider>
          </ThemeProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
};

// Main App Component that uses theme - moved inside provider structure
const AppContent: React.FC = () => {
  const themeContext = useTheme();
  const { isDarkMode, isLoading } = themeContext;

  // Ensure theme is available before rendering - with timeout fallback
  if (isLoading || !themeContext) {
    // Add a timeout to prevent infinite loading
    React.useEffect(() => {
      const timeout = setTimeout(() => {
        // Force continue if theme loading takes too long
        LoggingService.warn('Theme loading timeout, continuing with fallback', 'APP');
      }, 3000); // Increased to 3 seconds

      return () => clearTimeout(timeout);
    }, []);

    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#F8F9FA' }}>
        <ActivityIndicator size="large" color="#1877F2" />
      </View>
    );
  }

  // Create a proper React Native Paper theme using the adapter
  const paperTheme = themeContext ? createPaperTheme(isDarkMode) : getFallbackTheme();

  return (
    <PaperProvider theme={paperTheme}>
      <PortalProvider>
        <Portal>
          <ErrorBoundary>
            <NavigationContainer ref={navigationRef}>
              <RootNavigator />
              <StatusBar style={isDarkMode ? "light" : "dark"} />
            </NavigationContainer>
          </ErrorBoundary>
        </Portal>
      </PortalProvider>
    </PaperProvider>
  );
};

export default App;