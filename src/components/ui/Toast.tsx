import React, { useEffect, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';

const { width } = Dimensions.get('window');

// --- Type Definitions ---

type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default';

interface ToastProps {
  visible: boolean;
  message: string;
  type?: ToastType;
  position?: 'top' | 'bottom';
  duration?: number;
  onHide: () => void;
}

interface PresetConfig {
  icon: PhosphorIconName;
  backgroundColor: string;
  iconColor: string;
  textColor: string;
}

const Toast: React.FC<ToastProps> = ({
  visible,
  message,
  type = 'default',
  position = 'top',
  duration = 3000,
  onHide,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  // ✅ FIX: Use the correct theme structure (theme.colors.toast.*)
  const PRESET_CONFIGS = useMemo((): Record<ToastType, PresetConfig> => ({
    success: { icon: 'check-circle', backgroundColor: theme.colors.toast.surface, iconColor: theme.colors.toast.success, textColor: theme.colors.toast.successText },
    error: { icon: 'x-circle', backgroundColor: theme.colors.toast.surface, iconColor: theme.colors.toast.error, textColor: theme.colors.toast.errorText },
    warning: { icon: 'warning-circle', backgroundColor: theme.colors.toast.surface, iconColor: theme.colors.toast.warning, textColor: theme.colors.toast.warningText },
    info: { icon: 'info', backgroundColor: theme.colors.toast.surface, iconColor: theme.colors.toast.info, textColor: theme.colors.toast.infoText },
    default: { icon: 'bell', backgroundColor: theme.colors.toast.surface, iconColor: theme.colors.toast.defaultText, textColor: theme.colors.toast.defaultText },
  }), [theme]);

  const config = PRESET_CONFIGS[type];

  const opacity = useSharedValue(0);
  const translateY = useSharedValue(position === 'top' ? -50 : 50);
  const scale = useSharedValue(0.9);
  const translateX = useSharedValue(0);

  const hideToast = useCallback(() => {
    'worklet';
    const finalTranslateY = position === 'top' ? -100 : 100;
    opacity.value = withTiming(0, { duration: 200 });
    translateY.value = withTiming(finalTranslateY, { duration: 200 });
    scale.value = withTiming(0.9, { duration: 200 }, (isFinished) => {
      if (isFinished) {
        runOnJS(onHide)();
      }
    });
  }, [onHide, opacity, position, scale, translateY]);

  useEffect(() => {
    if (visible) {
      translateX.value = 0;
      translateY.value = position === 'top' ? -50 : 50;
      opacity.value = withTiming(1, { duration: 250 });
      translateY.value = withSpring(0, { damping: 15, stiffness: 120 });
      scale.value = withSpring(1, { damping: 15, stiffness: 120 });

      const timer = setTimeout(() => hideToast(), duration);
      return () => clearTimeout(timer);
    }
  }, [visible, duration, hideToast, opacity, position, scale, translateX, translateY]);

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      translateX.value = event.translationX;
      opacity.value = withTiming(1 - Math.abs(event.translationX) / width, { duration: 50 });
    })
    .onEnd((event) => {
      if (Math.abs(event.translationX) > width / 3 || Math.abs(event.velocityX) > 800) {
        translateX.value = withTiming(Math.sign(event.translationX) * width, { duration: 200 });
        runOnJS(hideToast)();
      } else {
        translateX.value = withSpring(0, { damping: 20, stiffness: 150 });
        opacity.value = withTiming(1, { duration: 100 });
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [
        { translateY: translateY.value },
        { translateX: translateX.value },
        { scale: scale.value },
      ],
    };
  });

  const top = position === 'top' ? insets.top + SPACING.sm : undefined;
  const bottom = position === 'bottom' ? insets.bottom + SPACING.sm : undefined;

  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View
        style={[
          styles.container,
          { top, bottom, backgroundColor: config.backgroundColor },
          animatedStyle,
        ]}
      >
        <PhosphorIcon name={config.icon} size={22} color={config.iconColor} style={styles.icon} />
        <Text style={[styles.message, { color: config.textColor }]}>{message}</Text>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: SPACING.md,
    right: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 9999,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: SPACING.sm,
  },
  message: {
    flex: 1,
    // ✅ FIX: Use specific typography properties instead of a pre-composed style
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.normal,
  },
});

export default Toast;