// Unified Product-Inventory Item Type
export interface Product {
  id: string;
  name: string;
  description?: string;
  category: string;
  sku?: string;

  // Pricing (unified)
  basePrice: number; // Selling price
  costPrice: number; // Purchase price
  purchasePrice: number; // Added for InventoryItem compatibility
  sellingPrice: number; // Added for InventoryItem compatibility

  // Product type
  itemType: 'product' | 'fabric' | 'accessory' | 'service';
  isService: boolean;
  requiresMeasurements?: boolean;
  estimatedTimeMinutes?: number;

  // Inventory fields (for physical items)
  baseUnit: string; // 'pieces', 'meters', 'yards', etc.
  availableUnits: string[]; // Available units for this item
  minimumStockLevel: number;
  currentStock?: number; // Current total stock across all warehouses

  // Status
  isActive: boolean;

  // Metadata
  metadata?: string;
  images?: string[]; // Only images array

  // Legacy fields for compatibility
  price?: number; // Maps to basePrice
  stock?: number; // Maps to currentStock

  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;

  name: string;
  email?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  gender?: string;
  preferences?: string;
  notes?: string;
  totalOrders?: number;
  totalSpent?: number;
  lastOrderDate?: string;
  isVIP?: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;

  customerId: string;
  staffId?: string;
  orderNumber: string;
  status: string;
  orderDate: string;
  dueDate?: string;
  completedDate?: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  paymentStatus: string;
  notes?: string;
  metadata?: string;
  items?: OrderItem[];
  // Legacy fields for compatibility
  customerName?: string;
  customer?: string;
  email?: string;
  phone?: string;
  date?: string;
  time?: string;
  orderType?: OrderType;
  tax?: number;
  discount?: number;
  total?: number;
  image?: string;
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  // Legacy fields for compatibility
  price?: number;
  total?: number;
}

// Enums and Types
export type OrderStatus = 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery' | 'custom';





// Staff Management
export interface Staff {
  id: string;
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;

  isActive: boolean;
  profileImage?: string;
  skills?: any[];
  experience?: any;
  performance?: StaffPerformance;
  workload?: StaffWorkload;
  paymentStructure?: any;
  availability?: any;
  emergencyContact?: any;
  documents?: any;
  createdAt: string;
  updatedAt: string;
}

export interface Outlet {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

export type StaffRole =
  | 'manager'
  | 'tailor'
  | 'assistant'
  | 'customer_service'
  | 'cutter'
  | 'finisher'
  | 'quality_checker'
  | 'designer';

// Data Context State
export interface DataState {
  // Core entities
  products: Product[];
  customers: Customer[];
  orders: Order[];
  orderItems: OrderItem[];
  measurements: CustomerMeasurement[];

  // Outlets and staff
  staff: Staff[];
  outlets: Outlet[];

  // Inventory
  warehouses: Warehouse[];

  // App state
  loading: boolean;
  error: string | null;
  settings: AppSettings;
  searchQuery: string;
  activeFilters: Record<string, any>;
  isDataLoaded: boolean;

  // Current selections
  activeOrderId: string | null;

  // ID counters
  nextProductId: number;
  nextOrderId: number;
  nextCustomerId: number;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  currency: string;
  taxRate: number;
  notifications: boolean;
  autoBackup: boolean;
  profileImage?: string;
  // Business profile settings
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
}

// Data Context Actions
export interface DataActions {
  // Product actions
  addProduct: (product: CreateProductData) => Promise<Product>;
  updateProduct: (id: string, updates: UpdateProductData) => Promise<Product>;
  deleteProduct: (id: string) => Promise<void>;

  // Customer actions
  addCustomer: (customer: CreateCustomerData) => Promise<Customer>;
  updateCustomer: (id: string, updates: UpdateCustomerData) => Promise<Customer>;
  deleteCustomer: (id: string) => Promise<void>;

  // Order actions
  addOrder: (order: CreateOrderData) => Promise<Order>;
  updateOrder: (id: string, updates: UpdateOrderData) => Promise<Order>;
  deleteOrder: (id: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;

  

  // Staff actions
  addStaff: (staff: CreateStaffData) => Promise<Staff>;
  updateStaff: (id: string, updates: UpdateStaffData) => Promise<Staff>;
  deleteStaff: (id: string) => Promise<void>;

  // Settings actions
  updateSettings: (settings: Partial<AppSettings>) => void;

  // Data management
  loadData: () => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
}

// Create/Update Data Types
export interface CreateProductData {
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  images?: string[]; // Changed from image to images array
  isActive: boolean;
}

export interface UpdateProductData extends Partial<CreateProductData> {}

export interface CreateCustomerData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {}

export interface CreateOrderData {
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  orderType: OrderType;
  items: CreateOrderItemData[];
  notes?: string;
}

export interface CreateOrderItemData {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
}

export interface UpdateOrderData extends Partial<Omit<CreateOrderData, 'items'>> {
  status?: OrderStatus;
}





export interface CreateStaffData {
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;

  profileImage?: string;
}

export interface UpdateStaffData extends Partial<CreateStaffData> {}

// Filter Types
export interface ProductFilters {
  category?: string;
  isActive?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface CustomerFilters {
  isActive?: boolean;
  search?: string;
  minOrders?: number;
  minSpent?: number;
}

export interface OrderFilters {
  status?: OrderStatus;
  orderType?: OrderType;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}



export interface StaffFilters {
  role?: StaffRole;

  isActive?: boolean;
  search?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ProductResponse extends ApiResponse<Product> {}
export interface ProductsResponse extends ApiResponse<Product[]> {}
export interface CustomerResponse extends ApiResponse<Customer> {}
export interface CustomersResponse extends ApiResponse<Customer[]> {}
export interface OrderResponse extends ApiResponse<Order> {}
export interface OrdersResponse extends ApiResponse<Order[]> {}

export interface StaffResponse extends ApiResponse<Staff> {}
export interface StaffListResponse extends ApiResponse<Staff[]> {}

// Form Data Types
export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  image?: string;
}

export interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface OrderFormData {
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  orderType: OrderType;
  notes?: string;
}



export interface StaffFormData {
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;

  profileImage?: string;
}

// Garment Order Types
export interface GarmentOrder {
  id: string;
  orderNumber?: string;
  customerId: string;
  customerName: string;

  garmentType: string;
  specifications?: any;
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    shoulder?: number;
    sleeveLength?: number;
    neck?: number;
    inseam?: number;
    height?: number;
    weight?: number;
  };
  pricing?: any;
  timeline?: any;
  workflow?: any;
  fittings?: any;
  qualityChecks?: any;
  images?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  tags?: string[];
  fabricId?: string;
  fabricName?: string;
  fabricQuantity?: number;
  fabricCost?: number;
  laborCost?: number;
  totalAmount?: number;
  status: GarmentOrderStatus;
  assignedTo?: string[];
  assignedToName?: string;
  deliveryDate?: string;
  notes?: string[];
  createdAt: string;
  updatedAt: string;
}

export type GarmentOrderStatus =
  | 'draft'
  | 'confirmed'
  | 'in_progress'
  | 'stitching'
  | 'quality_check'
  | 'completed'
  | 'delivered'
  | 'cancelled';

export interface CreateGarmentOrderData {
  customerId: string;
  customerName: string;
  garmentType: string;
  measurements?: {
    chest?: number;
    waist?: number;
    hips?: number;
    shoulder?: number;
    sleeveLength?: number;
    neck?: number;
    inseam?: number;
    height?: number;
    weight?: number;
  };
  fabricId?: string;
  fabricName?: string;
  fabricQuantity?: number;
  fabricCost?: number;
  laborCost?: number;
  deliveryDate?: string;
  notes?: string;
}

export interface UpdateGarmentOrderData extends Partial<CreateGarmentOrderData> {
  status?: GarmentOrderStatus;
  assignedTo?: string;
  assignedToName?: string;
}

export interface GarmentOrderFilters {
  status?: GarmentOrderStatus;
  customerId?: string;
  assignedTo?: string;
  garmentType?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

// Missing types that were causing errors
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  cleanupInterval: number;
}

export interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  responseTime: number;
  errorRate: number;
  loadTime: number;
  startupTime: number;
  fps: number;
  cacheHitRate: number;
  renderTime: number;
  navigationTime: number;
}

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// Garment-related types
export type GarmentType =
  | 'shirt'
  | 'pants'
  | 'dress'
  | 'suit'
  | 'blouse'
  | 'skirt'
  | 'jacket'
  | 'coat'
  | 'vest'
  | 'custom';

export type WorkflowStageStatus = 'pending' | 'in_progress' | 'completed' | 'blocked' | 'cancelled';

export interface Fitting {
  id: string;
  type: FittingType;
  status: FittingStatus;
  date: string;
  notes?: string;
  measurements?: Record<string, number>;
}

export type FittingType = 'initial' | 'interim' | 'final';
export type FittingStatus = 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';

// Customer Measurement types
export interface CustomerMeasurement {
  id: string;
  customerId: string;
  garmentType: GarmentType;
  measurements: Record<string, number>;
  unit: MeasurementUnit;
  takenBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MeasurementTemplate {
  id: string;
  name: string;
  garmentType: GarmentType;
  measurements: string[];
  description?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export type MeasurementUnit = 'cm' | 'inch' | 'mm';

// Product Category type
export type ProductCategory = 'fabric' | 'accessories' | 'tools' | 'materials' | 'other';

// Staff-related types
export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export type PaymentStructureType = 'hourly' | 'piece_rate' | 'commission' | 'salary';

export interface StaffWorkload {
  currentOrders: number;
  maxCapacity: number;
  efficiency: number;
  availableHours: number;
  scheduledHours: number;
  overtimeHours: number;
  capacity: number;
  lastUpdated: string;
}

export interface StaffPerformance {
  qualityRating: number;
  customerSatisfactionRating: number;
  averageCompletionTime: number;
  totalOrdersCompleted: number;
  ordersCompleted: number;
  onTimeDeliveryRate: number;
  reworkRate: number;
  goals: any[];
  lastUpdated: string;
}

// Outlet Inventory types
export interface OutletInventory {
  id: string;

  productId: string;
  itemId?: string;
  quantity: number;
  minStock: number;
  maxStock: number;
  lastUpdated: string;
}

export interface InventoryTransaction {
  id: string;
  itemId: string;
  warehouseId: string;
  type: 'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT';
  quantity: number;
  unit: string;
  performedBy: string;
  reference?: string;
  note?: string;
  date: string;
  productId?: string;
  reason?: string;
  fromLocation?: string;
  toLocation?: string;
  orderId?: string;
  staffId?: string;
  notes?: string;
  cost?: number;
  batchNumber?: string;
  timestamp?: string;
}

// Import PhosphorIconName type
import { PhosphorIconName } from '../utils/phosphorIconRegistry';
// Import inventory types
import { Warehouse } from './inventory';

// Missing type definitions
export interface StatCard {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: PhosphorIconName;
  color?: string;
  key?: string;
  onPress?: () => void;
  iconColor?: string;
  elevation?: 1 | 2 | 3 | 4 | 5;
  props?: Record<string, unknown>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export interface StatCardGroupProps {
  title?: string;
  cards: StatCard[];
  columns?: number;
  showTitle?: boolean;
  titleStyle?: any;
  containerStyle?: any;
  style?: any;
  onCardPress?: (card: StatCard) => void;
}

export interface ImagePickerProps {
  value?: string;
  onChange: (imageUri: string) => void;
  onImageSelected?: (imageUri: string | null) => void;
  currentImage?: string | null;
  placeholder?: string;
  style?: any;
  disabled?: boolean;
}

// Missing type exports
export type QualityGrade = 'A' | 'B' | 'C' | 'D';

export type InventoryItemType = 'fabric' | 'accessory' | 'tool' | 'material' | 'other';

export interface AddOrderScreenProps {
  route?: {
    params?: {
      orderId?: string;
      customerId?: string;
      editMode?: boolean;
    };
  };
  navigation: any;
}

// Missing types for database operations
export interface CreateInventoryItemData {
  name: string;
  category: string;
  baseUnit: string;
  availableUnits: string[];
  purchasePrice: number;
  sellingPrice: number;
  minimumStockLevel: number;
  imageUri?: string;
  isActive: boolean;
}

export interface UpdateInventoryItemData extends Partial<CreateInventoryItemData> {}

// Missing types for confirmation dialog


// Re-export ActionSheet types from their source
import { ActionSheetOption } from '../components/ui/ActionSheet';
export type { ActionSheetOption };



// Missing types for ListRenderItem
export interface ListRenderItem<T> {
  item: T;
  index: number;
  separators: {
    highlight: () => void;
    unhighlight: () => void;
    updateProps: (select: 'leading' | 'trailing' | 'cell', newProps: any) => void;
  };
}

// Missing types for Alert
export interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

export interface AlertOptions {
  title?: string;
  message?: string;
  buttons?: AlertButton[];
  cancelable?: boolean;
  onDismiss?: () => void;
}

// Missing types for navigation
export interface AboutScreenProps {
  navigation: any;
  route?: any;
}

// Missing types for data management
export interface DataManagementState {
  isLoading: boolean;
  isExporting: boolean;
  isImporting: boolean;
  error: string | null;
  success: string | null;
}

// Missing types for stock operations
export interface StockOperationFormData {
  selectedItem: string;
  selectedWarehouse: string;
  quantity: string;
  unit: string;
  reference: string;
  note: string;
}

// Missing types for logout confirmation
export interface LogoutConfirmationState {
  confirmLogout: boolean;
  isLoggingOut: boolean;
}

// Enhanced Order Types for Create Order Enhancements
export interface GarmentDetail {
  id: string;
  garmentType: string;
  templateId: string;
  measurements: Record<string, number>;
  fabricSelection: 'inhouse' | 'customer';
  fabricAmount?: number;
  fabricPrice?: number;
  notes: string;
  images: string[];
  quantity: number;
  isUrgent: boolean;
  sampleGiven: boolean;
  extraCharge?: number;
  unitPrice: number;
  totalPrice: number;
}

export interface EnhancedOrderData {
  // Existing order fields
  id?: string;
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  date: string;
  status: OrderStatus;
  orderType: OrderType;
  total: number;
  items: CreateOrderItemData[];
  notes?: string;

  // Enhanced fields
  garmentDetails: GarmentDetail[];
  discount: {
    amount: number;
    type: 'amount' | 'percentage';
  };
  totalFabricCost: number;
  subtotal: number;
  discountAmount: number;
  finalTotal: number;
}

export interface MeasurementFieldDefinition {
  name: string;
  label: string;
  unit: string;
  required: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
}

export interface ExtendedGarmentTemplate {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
  measurementFieldDefinitions?: MeasurementFieldDefinition[];
  category?: string;
  gender?: 'male' | 'female' | 'unisex';
}

export interface GarmentImage {
  id: string;
  garmentDetailId: string;
  uri: string;
  type: 'sample' | 'reference';
  uploadedAt: string;
}

export interface GarmentValidationErrors {
  measurements: Record<string, string>;
  fabricAmount?: string;
  fabricPrice?: string;
  quantity?: string;
  images?: string;
  notes?: string;
}

export interface CreateOrderValidationErrors {
  customer?: string;
  garmentType?: string;
  price?: string;
  dueDate?: string;
  discount?: string;
  garmentDetails?: Record<string, GarmentValidationErrors>;
}

// Enhanced Create Order Data
export interface EnhancedCreateOrderData extends CreateOrderData {
  garmentDetails: GarmentDetail[];
  discount: {
    amount: number;
    type: 'amount' | 'percentage';
  };
  totalFabricCost: number;
}

// Business Types (from business.ts)
export interface Measurement {
  id: string;
  customerId: string;
  itemId: string;
  measurements: Record<string, number>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Appointment {
  id: string;
  customerId: string;
  staffId: string;
  appointmentDate: string;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  method: string;
  status: string;
  reference?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  changes: string;
  createdAt: string;
  updatedAt: string;
}

// Database Types (from database.ts)
export interface DatabaseRecord {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateData {
  [key: string]: string | number | boolean | null | undefined;
}

export interface UpdateData {
  [key: string]: string | number | boolean | null | undefined;
}

export interface QueryOptions {
  where?: Record<string, unknown>;
  orderBy?: string;
  limit?: number;
  offset?: number;
}

export interface SearchResult<T extends DatabaseRecord> {
  items: T[];
  total: number;
  hasMore: boolean;
}

export interface DatabaseMetrics {
  totalQueries: number;
  averageQueryTime: number;
  errorRate: number;
  cacheHitRate?: number;
  databaseSize?: number;
}

export interface DatabaseHealth {
  isConnected: boolean;
  connectionCount: number;
  lastError?: string;
  uptime: number;
  performanceMetrics: DatabaseMetrics;
}

// Settings Types (from settings.ts)
export interface AppSetting {
  id: string;
  
  category: string;
  key: string;
  value: string;
  isJson: boolean;
  type?: string;
  description?: string;
  is_system?: boolean;
  created_at: string;
  updated_at: string;
}
