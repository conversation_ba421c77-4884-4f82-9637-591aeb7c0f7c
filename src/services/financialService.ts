import { AppError } from '../utils/errorHandler';

import LoggingService from './LoggingService';
import { StorageService } from './storageService';

// Financial interfaces
interface Expense {
  id: string;

  category: string;
  amount: number;
  description: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

interface CashReconciliation {
  id: string;

  date: string;
  expectedCash: number;
  actualCash: number;
  difference: number;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProfitLossData {
  revenue: number;
  expenses: number;
  grossProfit: number;
  netProfit: number;
  profitMargin: number;

  period: {
    startDate: string;
    endDate: string;
  };
}

interface PaymentAnalytics {
  totalTransactions: number;
  totalAmount: number;

  paymentMethods: Record<
    string,
    {
      count: number;
      amount: number;
      percentage: number;
    }
  >;
  trends: Array<{
    date: string;
    amount: number;
    method: string;
  }>;
}

interface TaxSummary {
  totalTaxableAmount: number;
  totalTaxCollected: number;
  taxRate: number;

  period: {
    startDate: string;
    endDate: string;
  };
  breakdown: Record<
    string,
    {
      amount: number;
      tax: number;
    }
  >;
}

// New interfaces for multi-outlet financial management




interface CostAllocation {
  outletName: string;
  directCosts: number;
  allocatedCosts: number;
  totalCosts: number;
  allocationMethod: 'revenue' | 'orders' | 'custom';
  period: {
    startDate: string;
    endDate: string;
  };
}

interface ProfitabilityAnalysis {
  outletName: string;
  garmentType: string;
  revenue: number;
  costs: number;
  profit: number;
  profitMargin: number;
  orderCount: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

interface ExpenseFilters {
  category?: string;

  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

interface ReconciliationFilters {

  startDate?: string;
  endDate?: string;
  status?: string;
}

/**
 * Financial Service for managing all financial operations with multi-outlet support
 */
export class FinancialService {
  static STORAGE_KEYS = {
    EXPENSES: 'financial_expenses',
    RECONCILIATIONS: 'cash_reconciliations',
    TAX_SETTINGS: 'tax_settings',
    FINANCIAL_SETTINGS: 'financial_settings',
    
    COST_ALLOCATIONS: 'cost_allocations',
  };

  /**
   * Expense Management with Multi-Outlet Support
   */
  static async addExpense(
    expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Expense> {
    try {
      const expenses = await this.getExpenses();
      const newExpense: Expense = {
        id: Date.now().toString(),
        ...expense,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      expenses.push(newExpense);
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);

      LoggingService.info('Expense added:', 'FINANCIAL', newExpense);
      return newExpense;
    } catch (error) {
      LoggingService.error(
        'Failed to add expense:',
        'FINANCIAL',
        error instanceof Error ? error : undefined
      );
      throw new AppError('Failed to add expense');
    }
  }

  static async getExpenses(filters: ExpenseFilters = {}): Promise<Expense[]> {
    try {
      const expenses: Expense[] = (await StorageService.get(this.STORAGE_KEYS.EXPENSES)) || [];

      let filtered = expenses;

      

      if (filters.category) {
        filtered = filtered.filter(e => e.category === filters.category);
      }

      if (filters.startDate) {
        filtered = filtered.filter(e => e.date >= filters.startDate!);
      }

      if (filters.endDate) {
        filtered = filtered.filter(e => e.date <= filters.endDate!);
      }

      if (filters.minAmount !== undefined) {
        filtered = filtered.filter(e => e.amount >= filters.minAmount!);
      }

      if (filters.maxAmount !== undefined) {
        filtered = filtered.filter(e => e.amount <= filters.maxAmount!);
      }

      return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      LoggingService.error(
        'Failed to get expenses:',
        'FINANCIAL',
        error instanceof Error ? error : undefined
      );
      // Return empty array instead of throwing error
      return [];
    }
  }

  static async updateExpense(id: string, updates: Partial<Expense>): Promise<Expense> {
    try {
      const expenses = await this.getExpenses();
      const index = expenses.findIndex(e => e.id === id);

      if (index === -1) {
        throw new AppError('Expense not found');
      }

      const updatedExpense: Expense = {
        ...expenses[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      expenses[index] = updatedExpense;
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);

      LoggingService.info('Expense updated:', updatedExpense as any);
      return updatedExpense;
    } catch (error) {
      LoggingService.error('Failed to update expense:', error as any);
      throw new AppError('Failed to update expense');
    }
  }

  static async deleteExpense(id: string): Promise<void> {
    try {
      const expenses = await this.getExpenses();
      const filtered = expenses.filter(e => e.id !== id);

      if (filtered.length === expenses.length) {
        throw new AppError('Expense not found');
      }

      await StorageService.set(this.STORAGE_KEYS.EXPENSES, filtered);
      LoggingService.info('Expense deleted:', id as any);
    } catch (error) {
      LoggingService.error('Failed to delete expense:', error as any);
      throw new AppError('Failed to delete expense');
    }
  }

  /**
   * Cash Reconciliation with Multi-Outlet Support
   */
  static async performCashReconciliation(
    reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<CashReconciliation> {
    try {
      const reconciliations = await this.getCashReconciliations();
      const newReconciliation: CashReconciliation = {
        id: Date.now().toString(),
        ...reconciliationData,
        difference: reconciliationData.actualCash - reconciliationData.expectedCash,
        status:
          Math.abs(reconciliationData.actualCash - reconciliationData.expectedCash) < 0.01
            ? 'balanced'
            : 'unbalanced',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reconciliations.push(newReconciliation);
      await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, reconciliations);

      LoggingService.info('Cash reconciliation performed:', newReconciliation as any);
      return newReconciliation;
    } catch (error) {
      LoggingService.error('Failed to perform cash reconciliation:', error as any);
      throw new AppError('Failed to perform cash reconciliation');
    }
  }

  static async getCashReconciliations(
    filters: ReconciliationFilters = {}
  ): Promise<CashReconciliation[]> {
    try {
      const reconciliations: CashReconciliation[] =
        (await StorageService.get(this.STORAGE_KEYS.RECONCILIATIONS)) || [];

      let filtered = reconciliations;

      

      if (filters.startDate) {
        filtered = filtered.filter(r => r.date >= filters.startDate!);
      }

      if (filters.endDate) {
        filtered = filtered.filter(r => r.date <= filters.endDate!);
      }

      if (filters.status) {
        filtered = filtered.filter(r => r.status === filters.status);
      }

      return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      LoggingService.error(
        'Failed to get cash reconciliations:',
        'FINANCIAL',
        error instanceof Error ? error : undefined
      );
      // Return empty array instead of throwing error
      return [];
    }
  }

  static async calculateDailyCashExpected(date: string, outletId?: string): Promise<number> {
    try {
      // This would typically integrate with your order/sales data
      // For now, return a placeholder calculation
      const orders: any[] = []; // Would get orders for the date and outlet
      const cashOrders = orders.filter(
        (order: any) => order.paymentMethod === 'cash' && (!outletId || order.outletId === outletId)
      );
      return cashOrders.reduce((total: number, order: any) => total + order.total, 0);
    } catch (error) {
      LoggingService.error('Failed to calculate daily cash expected:', error as any);
      throw new AppError('Failed to calculate expected cash');
    }
  }

  /**
   * Multi-Outlet Financial Reports
   */
  

  

  

  

  /**
   * Financial Reports (existing methods with outlet support)
   */
  static async generateProfitLossStatement(
    orders: any[],
    startDate: string,
    endDate: string
  ): Promise<ProfitLossData> {
    try {
      

      // Get revenue data (would integrate with sales/orders)
      const revenue = await this.calculateRevenue(startDate, endDate, orders);

      // Get expenses
      const expenses = await this.getExpenses({ startDate, endDate });
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

      const grossProfit = revenue - totalExpenses;
      const netProfit = grossProfit; // Simplified - would include taxes, etc.
      const profitMargin = revenue > 0 ? (netProfit / revenue) * 100 : 0;

      const profitLoss: ProfitLossData = {
        revenue,
        expenses: totalExpenses,
        grossProfit,
        netProfit,
        profitMargin,
        period: { startDate, endDate },
      };

      LoggingService.info('P&L statement generated:', profitLoss as any);
      return profitLoss;
    } catch (error) {
      LoggingService.error('Failed to generate P&L statement:', error as any);
      throw new AppError('Failed to generate profit & loss statement');
    }
  }

  static async getPaymentMethodAnalytics(
    orders: any[],
    startDate: string,
    endDate: string
  ): Promise<PaymentAnalytics> {
    try {
      // This would integrate with your order/payment data
      // For now, return placeholder data
      const analytics: PaymentAnalytics = {
        totalTransactions: 0,
        totalAmount: 0,
        paymentMethods: {
          cash: { count: 0, amount: 0, percentage: 0 },
          card: { count: 0, amount: 0, percentage: 0 },
          digital: { count: 0, amount: 0, percentage: 0 },
        },
        trends: [],
      };

      // Process orders to calculate analytics
      let totalTransactions = 0;
      let totalAmount = 0;
      const paymentMethods: Record<string, { count: number; amount: number; percentage: number }> = {};
      const trends: Array<{ date: string; amount: number; method: string }> = [];

      for (const order of orders) {
        if (order.paymentStatus === 'paid' && order.paidAmount > 0) {
          totalTransactions++;
          totalAmount += order.paidAmount;

          const method = order.paymentMethod || 'unknown'; // Assuming paymentMethod exists on order
          if (!paymentMethods[method]) {
            paymentMethods[method] = { count: 0, amount: 0, percentage: 0 };
          }
          paymentMethods[method].count++;
          paymentMethods[method].amount += order.paidAmount;

          trends.push({
            date: order.updatedAt.split('T')[0], // Use updated date for trend
            amount: order.paidAmount,
            method,
          });
        }
      }

      // Calculate percentages
      for (const method in paymentMethods) {
        if (paymentMethods.hasOwnProperty(method)) {
          paymentMethods[method].percentage = totalAmount > 0 ? (paymentMethods[method].amount / totalAmount) * 100 : 0;
        }
      }

      analytics.totalTransactions = totalTransactions;
      analytics.totalAmount = totalAmount;
      analytics.paymentMethods = paymentMethods;
      analytics.trends = trends.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      LoggingService.info('Payment analytics generated:', analytics as any);
      return analytics;
    } catch (error) {
      LoggingService.error('Failed to get payment method analytics', 'FINANCIAL', error as Error);
      throw new Error('Failed to generate payment analytics');
    }
  }

  static async getTaxSummary(
    orders: any[],
    startDate: string,
    endDate: string
  ): Promise<TaxSummary> {
    try {
      const revenue = await this.calculateRevenue(startDate, endDate, orders);
      const taxRate = 0.08; // Would get from settings
      const totalTaxCollected = revenue * taxRate;

      const taxSummary: TaxSummary = {
        totalTaxableAmount: revenue,
        totalTaxCollected,
        taxRate,
        period: { startDate, endDate },
        breakdown: {
          sales: {
            amount: revenue,
            tax: totalTaxCollected,
          },
        },
      };

      LoggingService.info('Tax summary generated:', taxSummary as any);
      return taxSummary;
    } catch (error) {
      LoggingService.error('Failed to get tax summary:', error as any);
      throw new AppError('Failed to generate tax summary');
    }
  }

  /**
   * Helper Methods for Multi-Outlet Support
   */
  

  

  

  

  

  

  

  /**
   * Helper Methods (existing)
   */
  private static async calculateRevenue(startDate: string, endDate: string, orders: any[]): Promise<number> {
    try {
      // This would integrate with your order/sales data
      // For now, return a placeholder calculation
      
      let totalRevenue = 0;
      // Calculate revenue from orders
      for (const order of orders) {
        // Assuming order.totalAmount exists and is the total revenue for the order
        totalRevenue += order.totalAmount || 0;
      }

      

      return totalRevenue;
    } catch (error) {
      LoggingService.error('Failed to calculate revenue:', 'FINANCIAL', error as Error);
      return 0;
    }
  }

  /**
   * Settings Management
   */
  static async getFinancialSettings(): Promise<Record<string, any>> {
    try {
      return (await StorageService.get(this.STORAGE_KEYS.FINANCIAL_SETTINGS)) || {};
    } catch (error) {
      LoggingService.error(
        'Failed to get financial settings:',
        'FINANCIAL',
        error instanceof Error ? error : undefined
      );
      // Return empty object instead of throwing error
      return {};
    }
  }

  static async updateFinancialSettings(settings: Record<string, any>): Promise<void> {
    try {
      const currentSettings = await this.getFinancialSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await StorageService.set(this.STORAGE_KEYS.FINANCIAL_SETTINGS, updatedSettings);
      LoggingService.info('Financial settings updated:', updatedSettings as any);
    } catch (error) {
      LoggingService.error('Failed to update financial settings:', error as any);
      throw new AppError('Failed to update financial settings');
    }
  }

  /**
   * Data Export/Import
   */
  static async exportFinancialData(): Promise<{
    expenses: Expense[];
    reconciliations: CashReconciliation[];
    settings: Record<string, any>;
  }> {
    try {
      const [expenses, reconciliations, settings] = await Promise.all([
        this.getExpenses(),
        this.getCashReconciliations(),
        this.getFinancialSettings(),
      ]);

      return { expenses, reconciliations, settings };
    } catch (error) {
      LoggingService.error('Failed to export financial data:', error as any);
      throw new AppError('Failed to export financial data');
    }
  }

  static async importFinancialData(data: {
    expenses?: Expense[];
    reconciliations?: CashReconciliation[];
    settings?: Record<string, any>;
  }): Promise<void> {
    try {
      if (data.expenses) {
        await StorageService.set(this.STORAGE_KEYS.EXPENSES, data.expenses);
      }

      if (data.reconciliations) {
        await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, data.reconciliations);
      }

      if (data.settings) {
        await StorageService.set(this.STORAGE_KEYS.FINANCIAL_SETTINGS, data.settings);
      }

      LoggingService.info('Financial data imported successfully');
    } catch (error) {
      LoggingService.error('Failed to import financial data:', error as any);
      throw new AppError('Failed to import financial data');
    }
  }
}
