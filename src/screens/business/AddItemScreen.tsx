import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useReducer, useCallback } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform, TouchableOpacity } from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';

import ImagePicker from '../../components/forms/ImagePicker';
import Header from '../../components/navigation/Header';
import { Button, Dropdown, Switch, TextInput } from '../../components/ui';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { ImageProcessingService } from '../../services/ImageProcessingService';
import LoggingService from '../../services/LoggingService';
import { Product } from '../../types';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// --- Type Definitions (Simplified) ---
interface ItemFormData {
  name: string;
  description: string;
  category: string;
  sku: string;
  itemType: 'product' | 'fabric';
  basePrice: string;
  costPrice: string;
  baseUnit: string;
  minimumStockLevel: string;
  isActive: boolean;
  images: string[]; // Changed from imageUri to images array
}

// --- Constants (Moved outside component) ---
const ITEM_TYPE_OPTIONS = [
    { label: 'Product', value: 'product' as const, icon: 'package' as const },
    { label: 'Fabric', value: 'fabric' as const, icon: 'palette' as const },
];

const CATEGORIES = {
    product: ['Shirts', 'Pants', 'Dresses', 'Suits', 'Jackets'],
    fabric: ['Cotton', 'Silk', 'Wool', 'Linen', 'Polyester'],
};

const UNITS = {
    product: ['pieces', 'sets'],
    fabric: ['meters', 'yards', 'inches'],
};

// --- State Management with Reducer ---
type FormState = {
    formData: ItemFormData;
    errors: Partial<Record<keyof ItemFormData, string>>;
    status: 'idle' | 'loading' | 'saving' | 'error';
};
type FormAction = 
    | { type: 'LOAD_DATA'; payload: Product }
    | { type: 'SET_FIELD'; field: keyof ItemFormData; value: any }
    | { type: 'SET_ITEM_TYPE'; payload: 'product' | 'fabric' }
    | { type: 'SET_ERRORS'; payload: Partial<Record<keyof ItemFormData, string>> }
    | { type: 'SUBMIT' }
    | { type: 'SUBMIT_SUCCESS' }
    | { type: 'SUBMIT_FAILURE' };

const createInitialState = (initialItemType: 'product' | 'fabric'): FormState => ({
    formData: {
        name: '', description: '', category: '', sku: '',
        itemType: initialItemType, basePrice: '', costPrice: '',
        baseUnit: UNITS[initialItemType][0],
        minimumStockLevel: '10', isActive: true, images: [], // Changed imageUri to images
    },
    errors: {},
    status: 'idle',
});

const formReducer = (state: FormState, action: FormAction): FormState => {
    switch (action.type) {
        case 'LOAD_DATA':
            const item = action.payload;
            const validItemType = (item.itemType === 'product' || item.itemType === 'fabric') ? item.itemType : 'product';
            return { ...state, status: 'idle', formData: {
                name: item.name, description: item.description || '', category: item.category,
                sku: item.sku || '', itemType: validItemType,
                basePrice: item.basePrice.toString(), costPrice: item.costPrice?.toString() || '',
                baseUnit: item.baseUnit || 'pieces',
                minimumStockLevel: item.minimumStockLevel?.toString() || '10', isActive: item.isActive,
                images: item.images || [],
            }};
        case 'SET_FIELD':
            return { ...state, formData: { ...state.formData, [action.field]: action.value }, errors: { ...state.errors, [action.field]: undefined } };
        case 'SET_ITEM_TYPE':
            return { ...state, formData: { ...state.formData, itemType: action.payload, category: '', baseUnit: UNITS[action.payload][0] }};
        case 'SET_ERRORS':
            return { ...state, errors: action.payload };
        case 'SUBMIT':
            return { ...state, status: 'saving' };
        case 'SUBMIT_SUCCESS':
            return { ...state, status: 'idle' };
        case 'SUBMIT_FAILURE':
            return { ...state, status: 'error' };
        default:
            return state;
    }
};

// --- Main Component ---
const AddItemScreen: React.FC<{ route?: any }> = ({ route }) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const { showSuccess, showError } = useToast();
  const navigation = useNavigation<any>();

  const itemId = route?.params?.itemId ? String(route.params.itemId) : undefined;
  const isEditing = !!itemId;
  const initialItemType = route?.params?.itemType === 'fabric' ? 'fabric' : 'product';

  const [state, dispatch] = useReducer(formReducer, createInitialState(initialItemType));
  const { formData, errors, status } = state;

  useEffect(() => {
    if (isEditing && itemId && dataState.products.length > 0) {
      dispatch({ type: 'SUBMIT' });
      const item = dataState.products.find(p => p.id === itemId);
      if (item) {
        dispatch({ type: 'LOAD_DATA', payload: item });
      }
    }
  }, [isEditing, itemId]); // Removed dataState.products from dependencies
  
  const validateAndSave = useCallback(async () => {
    const newErrors: Partial<Record<keyof ItemFormData, string>> = {};
    if (!formData.name.trim()) newErrors.name = 'Item name is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.basePrice || parseFloat(formData.basePrice) <= 0) newErrors.basePrice = 'Valid selling price is required';
    
    if (Object.keys(newErrors).length > 0) {
        dispatch({ type: 'SET_ERRORS', payload: newErrors });
        return;
    }

    dispatch({ type: 'SUBMIT' });
    try {
      let finalImageUris = [...formData.images];
      const newImageFiles = formData.images.filter(uri => uri && uri.startsWith('file://'));
      const existingImageUrls = formData.images.filter(uri => uri && uri.startsWith('http'));

      if (newImageFiles.length > 0) {
        const uploadedUrls = await Promise.all(
          newImageFiles.map(localUri => ImageProcessingService.optimizeAndUpload(localUri))
        );
        finalImageUris = [...existingImageUrls, ...uploadedUrls];
      }

      if (isEditing && itemId) {
        const existingItem = dataState.products.find(p => p.id === itemId);
        if (!existingItem) {
            showError('Item not found. Could not update.');
            dispatch({ type: 'SUBMIT_FAILURE' });
            return;
        }

        await actions.updateProduct({ ...existingItem, ...formData, images: finalImageUris, basePrice: parseFloat(formData.basePrice), costPrice: parseFloat(formData.costPrice || '0'), minimumStockLevel: parseInt(formData.minimumStockLevel) });
        showSuccess('Item updated successfully!');
      } else {
        const itemToSave: Omit<Product, 'id' | 'createdAt' | 'updatedAt'> = {
            name: formData.name.trim(), description: formData.description.trim(), category: formData.category,
            sku: formData.sku.trim(), itemType: formData.itemType, basePrice: parseFloat(formData.basePrice),
            costPrice: parseFloat(formData.costPrice || '0'), // Ensure costPrice is always a number
            purchasePrice: parseFloat(formData.costPrice || '0'), // Add purchasePrice
            sellingPrice: parseFloat(formData.basePrice), // Add sellingPrice
            baseUnit: formData.baseUnit,
            availableUnits: UNITS[formData.itemType], // Add availableUnits
            minimumStockLevel: parseInt(formData.minimumStockLevel),
            isActive: formData.isActive, images: finalImageUris,
            isService: false, currentStock: 0,
        };
        await actions.addProduct(itemToSave);
        showSuccess('Item added successfully!');
      }
      dispatch({ type: 'SUBMIT_SUCCESS' });
      navigation.goBack();
    } catch (error) {
      LoggingService.error('Failed to save item', 'ADD_ITEM', error as Error);
      showError('Failed to save item. Please try again.');
      dispatch({ type: 'SUBMIT_FAILURE' });
    }
  }, [formData, isEditing, itemId, dataState.products, actions, showSuccess, showError, navigation]);
  
  const generateSku = useCallback(() => {
      if (!formData.name.trim() || !formData.category) {
          showError('Please enter a name and category first.');
          return;
      }
      const typePrefix = formData.itemType.slice(0, 3).toUpperCase();
      const categoryPrefix = formData.category.slice(0, 3).toUpperCase();
      const timestamp = Date.now().toString().slice(-4);
      dispatch({type: 'SET_FIELD', field: 'sku', value: `${typePrefix}-${categoryPrefix}-${timestamp}`});
  }, [formData.name, formData.category, formData.itemType, showError]);

  if (status === 'loading') {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title={isEditing ? 'Edit Item' : 'Add Item'} showBack={true} />
        <View style={styles.loadingContainer}><ActivityIndicator /></View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title={isEditing ? 'Edit Item' : 'Add Item'} showBack={true} />
      <KeyboardAvoidingView style={styles.flex} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <ScrollView style={styles.flex} contentContainerStyle={styles.scrollContent}>
          {!isEditing && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Item Type</Text>
              <View style={styles.itemTypeGrid}>
                {ITEM_TYPE_OPTIONS.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={styles.radioOption}
                    onPress={() => dispatch({ type: 'SET_ITEM_TYPE', payload: option.value })}
                  >
                    <View
                      style={[
                        styles.radioCircle,
                        {
                          borderColor:
                            formData.itemType === option.value ? theme.colors.primary : theme.colors.outline,
                        },
                      ]}
                    >
                      {formData.itemType === option.value && (
                        <View style={[styles.radioDot, { backgroundColor: theme.colors.primary }]} />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.radioLabel,
                        {
                          color:
                            formData.itemType === option.value ? theme.colors.primary : theme.colors.onSurface,
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Form Sections as before */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            <TextInput label="Item Name" value={formData.name} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'name', value: v})} error={errors.name} required />
            <TextInput label="Description" value={formData.description} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'description', value: v})} multiline />
            <Dropdown label="Category" value={formData.category} onValueChange={(v) => dispatch({type: 'SET_FIELD', field: 'category', value: v})} options={CATEGORIES[formData.itemType].map(cat => ({ label: cat, value: cat }))} error={errors.category} required />
            <View style={styles.row}>
                <TextInput label="SKU" value={formData.sku} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'sku', value: v})} style={styles.flex} />
                <Button onPress={generateSku} variant="text">Generate</Button>
            </View>
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing (BDT)</Text>
            <View style={styles.row}>
              <TextInput label="Selling Price" value={formData.basePrice} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'basePrice', value: v})} keyboardType="numeric" error={errors.basePrice} required style={styles.flex} />
              <TextInput label="Cost Price" value={formData.costPrice} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'costPrice', value: v})} keyboardType="numeric" style={styles.flex} />
            </View>
          </View>
          <View style={styles.section}>
              <Text style={styles.sectionTitle}>Inventory</Text>
              <View style={styles.row}>
                <Dropdown label="Base Unit" value={formData.baseUnit} onValueChange={(v) => dispatch({type: 'SET_FIELD', field: 'baseUnit', value: v})} options={UNITS[formData.itemType].map(unit => ({ label: unit, value: unit }))} style={styles.flex} />
                <TextInput label="Min. Stock" value={formData.minimumStockLevel} onChangeText={(v) => dispatch({type: 'SET_FIELD', field: 'minimumStockLevel', value: v})} keyboardType="numeric" style={styles.flex} />
              </View>
            </View>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Settings</Text>
            
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Item is Active</Text>
              <Switch value={formData.isActive} onValueChange={(v) => dispatch({type: 'SET_FIELD', field: 'isActive', value: v})} />
            </View>
          </View>
          {formData.itemType === 'product' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Product Images (Up to 3)</Text>
              <View style={styles.imagesContainer}>
                {[0, 1, 2].map(index => (
                  <View key={index} style={styles.imageWrapper}>
                    <ImagePicker
                      onImageSelected={(uri: string) => dispatch({ type: 'SET_FIELD', field: 'images', value: [...formData.images.slice(0, index), uri, ...formData.images.slice(index + 1)] })}
                      currentImage={formData.images[index] || null}
                      placeholder={`Image ${index + 1}`}
                      size='medium'
                    />
                  </View>
                ))}
              </View>
            </View>
          )}
          {formData.itemType === 'fabric' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Fabric Image</Text>
              <ImagePicker
                currentImage={formData.images[0] || null}
                onImageSelected={(uri: string) => dispatch({ type: 'SET_FIELD', field: 'images', value: [uri] })}
              />
            </View>
          )}
          
          <Button onPress={validateAndSave} loading={status === 'saving'} disabled={status === 'saving'} icon="check">{isEditing ? 'Update Item' : 'Save Item'}</Button>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  flex: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  scrollContent: { padding: SPACING.md, paddingBottom: SPACING.xl },
  section: { marginBottom: SPACING.lg },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.bold, marginBottom: SPACING.md },
  itemTypeGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  radioCircle: {
    width: 24,
    height: 24,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  radioDot: {
    width: 12,
    height: 12,
    borderRadius: 8,
  },
  radioLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  row: { flexDirection: 'row', gap: SPACING.md, alignItems: 'center' },
  switchContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.sm },
  switchLabel: { fontSize: TYPOGRAPHY.fontSize.md },
  imagesContainer: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.md, marginTop: SPACING.sm },
  imageWrapper: { flex: 1, minWidth: 100, maxWidth: 120 },
});

export default AddItemScreen;