import React, {
  useState,
  useCallback,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextInput as RNTextInput,
  Text,
  FlatList,
  Keyboard,
} from 'react-native';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// Types
interface SearchItem {
  id?: string | number;
  name?: string;
  [key: string]: any;
}

export interface SearchRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
}

interface SearchProps {
  data?: SearchItem[];
  placeholder?: string;
  onSearch?: (query: string) => void;
  onResultSelect?: (item: SearchItem) => void;
  searchFields?: string[];
  style?: ViewStyle;
}

const searchItems = (items: SearchItem[], query: string): SearchItem[] => {
  if (!query.trim()) return [];
  const searchTerm = query.toLowerCase();
  return items
    .filter(item => 
      item.name?.toLowerCase().includes(searchTerm) ||
      Object.values(item).some(value => 
        typeof value === 'string' && value.toLowerCase().includes(searchTerm)
      )
    )
    .slice(0, 5); // Max 5 results
};

const DEBOUNCE_DELAY = 250; // Add a debounce delay

const Search = forwardRef<SearchRef, SearchProps>(({
  data = [],
  placeholder = 'Search...',
  onSearch,
  onResultSelect,
  style,
}, ref) => {
  const theme = useTheme();
  const textInputRef = useRef<RNTextInput>(null);
  const debounceTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchItem[]>([]);
  const [isFocused, setIsFocused] = useState(false);

  useImperativeHandle(ref, () => ({
    focus: () => textInputRef.current?.focus(),
    blur: () => textInputRef.current?.blur(),
    clear: () => {
      setQuery('');
      setSuggestions([]);
      textInputRef.current?.clear();
      Keyboard.dismiss();
    },
  }));

  const handleQueryChange = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
    onSearch?.(searchQuery);

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    if (!searchQuery.trim()) {
      setSuggestions([]);
      return;
    }

    debounceTimeoutRef.current = setTimeout(() => {
      const results = searchItems(data, searchQuery);
      setSuggestions(results);
    }, DEBOUNCE_DELAY);
  }, [data, onSearch]);

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleSuggestionSelect = useCallback((item: SearchItem) => {
    setQuery(item.name || '');
    setSuggestions([]);
    onResultSelect?.(item);
    Keyboard.dismiss(); // Dismiss keyboard on selection
  }, [onResultSelect]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setSuggestions([]);
  }, []);

  const areSuggestionsVisible = isFocused && suggestions.length > 0;

  return (
    <View style={[styles.container, style]}>
      <View style={styles.searchInputContainer}>
        <RNTextInput
          ref={textInputRef}
          value={query}
          onChangeText={handleQueryChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          style={[
            styles.searchInput,
            {
              backgroundColor: theme.colors.surfaceVariant,
              color: theme.colors.onSurface,
            }
          ]}
        />
        {query ? (
          <TouchableOpacity style={styles.clearButton} onPress={clearSearch}>
            <PhosphorIcon name='x' size={16} color={theme.colors.onSurfaceVariant} />
          </TouchableOpacity>
        ) : null}
      </View>

      {areSuggestionsVisible && (
        <View style={[styles.suggestionsContainer, { backgroundColor: theme.colors.surface }]}>
          <FlatList
            data={suggestions}
            keyExtractor={(item, index) => item.id?.toString() || index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.suggestionItem}
                // Use onMouseDown-like behavior to prevent blur before press
                onPressIn={() => textInputRef.current?.blur()}
                onPress={() => handleSuggestionSelect(item)}
              >
                <Text style={[styles.suggestionText, { color: theme.colors.onSurface }]}>
                  {item.name || Object.values(item)[0]}
                </Text>
              </TouchableOpacity>
            )}
            keyboardShouldPersistTaps="handled"
          />
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1, // Ensure suggestions appear on top
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40, // Slightly taller for better touch target
  },
  searchInput: {
    height: 40,
    flex: 1,
    paddingHorizontal: 16,
    paddingRight: 40,
    borderRadius: 20, // More rounded for modern look
    fontSize: 16,
  },
  clearButton: {
    position: 'absolute',
    right: 8,
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 44, // Position below the input
    left: 0,
    right: 0,
    borderRadius: 12,
    backgroundColor: 'white', // Explicit background
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200, // Prevent list from being too long
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  suggestionText: {
    fontSize: 16,
  },
});

Search.displayName = 'Search';

export default Search;