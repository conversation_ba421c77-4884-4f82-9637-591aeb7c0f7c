/**
 * Transaction History Screen
 * Full view of all inventory transactions with filtering and search
 */

import React, { useState, useCallback, useEffect } from 'react';
import { FlatList, View, StyleSheet, RefreshControl } from 'react-native';
import { Text, Chip, FAB } from 'react-native-paper';
import ActionSheet from '../../components/ui/ActionSheet';
import { ActionSheetOption } from '../../types';

import { TransactionHistoryList } from '../../components/inventory';
import Header from '../../components/navigation/Header';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { InventoryTransaction } from '../../types/inventory';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface TransactionHistoryScreenProps {
  navigation: any;
  route?: {
    params?: {
      filter?: 'all' | 'in' | 'out' | 'transfer' | 'adjustment';
      itemId?: string;
      warehouseId?: string;
    };
  };
}

const TransactionHistoryScreen: React.FC<TransactionHistoryScreenProps> = ({
  navigation,
  route,
}) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [loadingTransactions, setLoadingTransactions] = useState<boolean>(false);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);

  const loadTransactions = useCallback(async () => {
    setLoadingTransactions(true);
    setTransactionsError(null);
    try {
      const fetchedTransactions = await dataActions.getTransactionHistory();
      setTransactions(fetchedTransactions);
    } catch (error) {
      setTransactionsError('Failed to load transactions.');
      LoggingService.error('Failed to load transactions', 'TRANSACTION_HISTORY', error as Error);
    } finally {
      setLoadingTransactions(false);
    }
  }, [dataActions]);

  const state = {
    recentTransactions: transactions,
    items: dataState.products,
    warehouses: dataState.warehouses || [],
    isLoading: loadingTransactions,
    error: transactionsError,
  };

  const initialFilter = route?.params?.filter || 'all';
  const initialItemId = route?.params?.itemId;
  const initialWarehouseId = route?.params?.warehouseId;

  const [filteredTransactions, setFilteredTransactions] = useState<InventoryTransaction[]>([]);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState<string>(initialFilter);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Load transactions on mount
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  // Apply filters to transactions
  const applyFilters = useCallback(() => {
    let filtered = state.recentTransactions;

    // Apply type filter
    switch (selectedFilter) {
      case 'in':
        filtered = filtered.filter((t: InventoryTransaction) => t.type === 'IN');
        break;
      case 'out':
        filtered = filtered.filter((t: InventoryTransaction) => t.type === 'OUT');
        break;
      case 'transfer':
        filtered = filtered.filter((t: InventoryTransaction) => t.type === 'TRANSFER');
        break;
      case 'adjustment':
        filtered = filtered.filter((t: InventoryTransaction) => t.type === 'ADJUSTMENT');
        break;
      case 'all':
      default:
        // No type filtering
        break;
    }

    // Apply item filter
    if (initialItemId) {
      filtered = filtered.filter((t: InventoryTransaction) => t.itemId === initialItemId);
    }

    // Apply warehouse filter
    if (initialWarehouseId) {
      filtered = filtered.filter((t: InventoryTransaction) => t.warehouseId === initialWarehouseId);
    }

    // Sort by date (newest first)
    filtered.sort(
      (a: InventoryTransaction, b: InventoryTransaction) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    setFilteredTransactions(filtered);
  }, [state.recentTransactions, selectedFilter, initialItemId, initialWarehouseId]);

  // Apply filters when dependencies change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadTransactions();
    } catch (error) {
      LoggingService.error('Failed to refresh transactions', 'TRANSACTION_HISTORY', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [loadTransactions]);

  // Handle filter change
  const handleFilterChange = useCallback((filter: string) => {
    setSelectedFilter(filter);
  }, []);

  // Handle transaction press
  const handleTransactionPress = useCallback((transaction: InventoryTransaction) => {
    LoggingService.info('Transaction selected', 'TRANSACTION_HISTORY', transaction);
    // Navigate to transaction detail or item detail
    if (transaction.itemId) {
      navigation.navigate('InventoryItemDetail', { itemId: transaction.itemId });
    }
  }, [navigation]);

  // Get screen title
  const getScreenTitle = (): string => {
    if (initialItemId) {
      const item = state.items.find(i => i.id === initialItemId);
      return item ? `${item.name} - Transactions` : 'Item Transactions';
    }
    if (initialWarehouseId) {
      const warehouse = state.warehouses.find(w => w.id === initialWarehouseId);
      return warehouse ? `${warehouse.name} - Transactions` : 'Warehouse Transactions';
    }
    return 'Transaction History';
  };

  // Removed unused getScreenSubtitle function

  // Filter options
  const filterOptions = [
    { key: 'all', label: 'All', icon: 'list' },
    { key: 'in', label: 'Stock In', icon: 'arrow-down' },
    { key: 'out', label: 'Stock Out', icon: 'arrow-up' },
    { key: 'transfer', label: 'Transfer', icon: 'arrows-left-right' },
    { key: 'adjustment', label: 'Adjustment', icon: 'wrench' },
  ];

  // Render filter chips
  const renderFilterChips = () => (
    <View style={styles.filterContainer}>
      <FlatList
        data={filterOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterList}
        keyExtractor={item => item.key}
        renderItem={({ item }) => (
          <Chip
            selected={selectedFilter === item.key}
            onPress={() => handleFilterChange(item.key)}
            style={[
              styles.filterChip,
              selectedFilter === item.key && { backgroundColor: theme.colors.primaryContainer },
            ]}
            textStyle={[
              styles.filterChipText,
              selectedFilter === item.key && { color: theme.colors.onPrimaryContainer },
            ]}
            icon={({ size, color }) => (
              <PhosphorIcon
                name={item.icon as any}
                size={size}
                color={selectedFilter === item.key ? theme.colors.onPrimaryContainer : color}
              />
            )}
          >
            {item.label}
          </Chip>
        )}
      />
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <PhosphorIcon name='clock' size={64} color={theme.colors.onSurfaceVariant} />
      <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
        No Transactions Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.onSurfaceVariant }]}>
        {selectedFilter === 'all'
          ? 'No transactions have been recorded yet.'
          : `No ${selectedFilter} transactions found.`}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={getScreenTitle()}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'magnifying-glass',
            onPress: () => {
              // TODO: Implement search functionality
              LoggingService.info('Search pressed', 'TRANSACTION_HISTORY');
            },
          },
        ]}
        
      />

      {/* Filter Chips */}
      {renderFilterChips()}

      {/* Transaction List */}
      <FlatList
        data={filteredTransactions}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <TransactionHistoryList
            transactions={[item]}
            showItemName={true}
            showWarehouseName={true}
            showPerformedBy={true}
            compact={false}
            onTransactionPress={handleTransactionPress}
          />
        )}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Floating Action Button */}
      <FAB
        icon='plus'
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('StockOperations')}
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  filterList: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  filterChip: {
    marginRight: SPACING.sm,
  },
  filterChipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  listContainer: {
    flexGrow: 1,
    padding: SPACING.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
  },
});

export default TransactionHistoryScreen;
