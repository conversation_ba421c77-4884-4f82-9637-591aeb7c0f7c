import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Chip, Text } from 'react-native-paper';
import { ActionSheetOption } from '../../types';
import ImagePicker from '../../components/forms/ImagePicker';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import Dropdown from '../../components/ui/Dropdown';
import Switch from '../../components/ui/Switch';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { ImageProcessingService } from '../../services/ImageProcessingService';
import LoggingService from '../../services/LoggingService';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface AddStaffScreenProps {
  route?: {
    params?: {
      staff?: any;
    };
  };
}

interface StaffData {
  employeeId: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  outletId: string;
  profileImage: string;
  isActive: boolean;
  skills: string[];
}

const AddStaffScreen: React.FC<AddStaffScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  const navigation = useNavigation();
  

  const editingStaff = route?.params?.staff;
  const isEditing = !!editingStaff;

  const [staffData, setStaffData] = useState<StaffData>({
    employeeId: editingStaff?.employeeId || '',
    name: editingStaff?.name || '',
    email: editingStaff?.email || '',
    phone: editingStaff?.phone || '',
    role: editingStaff?.role || 'tailor',
    outletId: editingStaff?.outletId || '',
    profileImage: editingStaff?.profileImage || '',
    isActive: editingStaff?.isActive ?? true,
    skills: editingStaff?.skills || [],
  });

  const [newSkill, setNewSkill] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  

  const handleInputChange = (field: keyof StaffData, value: string | boolean | string[]) => {
    setStaffData(prev => ({ ...prev, [field]: value }));
    if (field === 'name' && value && !staffData.employeeId.trim()) {
      setStaffData(prev => ({ ...prev, employeeId: `EMP${Date.now().toString().slice(-6)}` }));
    }
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addSkill = () => {
    if (newSkill.trim() && !staffData.skills.includes(newSkill.trim())) {
      setStaffData(prev => ({ ...prev, skills: [...prev.skills, newSkill.trim()] }));
      setNewSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setStaffData(prev => ({ ...prev, skills: prev.skills.filter(skill => skill !== skillToRemove) }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!staffData.employeeId.trim()) newErrors.employeeId = 'Employee ID is required';
    if (!staffData.name.trim()) newErrors.name = 'Name is required';
    if (staffData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(staffData.email)) newErrors.email = 'Invalid email format';
    if (staffData.phone && !/^(\+?880|0?1)[3-9]\d{8}$/.test(staffData.phone.replace(/[^\d+]/g, ''))) newErrors.phone = 'Please enter a valid Bangladeshi phone number';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsSaving(true);
    try {
      const isNewImage = staffData.profileImage && staffData.profileImage.startsWith('file://');
      let finalImageUrl = staffData.profileImage;

      const staffPayload = {
        employeeId: staffData.employeeId,
        name: staffData.name,
        email: staffData.email || undefined,
        phone: staffData.phone || undefined,
        role: staffData.role as any,
        profileImage: finalImageUrl || undefined,
        isActive: staffData.isActive,
        skills: staffData.skills,
      };

      if (isEditing) {
        await dataActions.updateStaff(editingStaff.id, staffPayload);
        if (isNewImage) {
          ImageProcessingService.processImageAndUpdate({
            localUri: finalImageUrl,
            entityId: editingStaff.id,
            updateAction: async (id, remoteUrl) => {
              await dataActions.updateStaff(id, { ...staffPayload, profileImage: remoteUrl });
            },
          });
        }
      } else {
        if (isNewImage) {
          finalImageUrl = await ImageProcessingService.optimizeAndUpload(finalImageUrl);
        }
        await dataActions.addStaff({ ...staffPayload, profileImage: finalImageUrl });
      }

      navigation.goBack();
    } catch (error) {
      LoggingService.error('Failed to save staff', 'STAFF', error as Error);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [ { label: 'Manager', value: 'manager' }, { label: 'Tailor', value: 'tailor' }, { label: 'Assistant', value: 'assistant' } ];
  const outletOptions = dataState.warehouses.map(warehouse => ({ label: warehouse.name, value: warehouse.id }));
  

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title={isEditing ? 'Edit Staff Member' : 'Add Staff Member'} onBackPress={() => navigation.goBack()} showBack={true} actions={[{ text: isEditing ? 'Update' : 'Save', onPress: handleSave, color: theme.colors.primary, disabled: isSaving }]}  />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.profileSection}>
          <ImagePicker onImageSelected={(uri: string) => handleInputChange('profileImage', uri)} currentImage={staffData.profileImage || null} placeholder='Add Photo' size='medium' rounded={true} />
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Staff Details</Text>
          <TextInput label='Full Name' value={staffData.name} onChangeText={(value: string) => handleInputChange('name', value)} error={errors.name} required style={styles.input} />
          <TextInput label='Phone Number' value={staffData.phone} onChangeText={(value: string) => handleInputChange('phone', value)} error={errors.phone} style={styles.input} />
          <TextInput label='Email Address' value={staffData.email} onChangeText={(value: string) => handleInputChange('email', value)} error={errors.email} style={styles.input} />
          <TextInput label='Employee ID' value={staffData.employeeId} onChangeText={(value: string) => handleInputChange('employeeId', value)} error={errors.employeeId} required style={styles.input} placeholder='Will be auto-generated' />
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Role & Assignment</Text>
          <Dropdown label='Role' placeholder='Select Role' value={staffData.role} options={roleOptions} onValueChange={value => handleInputChange('role', value)} error={errors.role} style={styles.input} />
          <Dropdown label='Outlet' placeholder='Select Outlet' value={staffData.outletId} options={outletOptions} onValueChange={value => handleInputChange('outletId', value)} error={errors.outletId} style={styles.input} />
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Settings</Text>
          <View style={styles.switchRow}>
            <Text style={[styles.switchTitle, { color: theme.colors.onSurface }]}>Active Staff Member</Text>
            <Switch value={staffData.isActive} onValueChange={(value: boolean) => handleInputChange('isActive', value)} />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Skills & Expertise</Text>
          <View style={styles.row}>
            <TextInput label='Add Skill' value={newSkill} onChangeText={setNewSkill} style={{ ...styles.input, flex: 1 }} />
            <TouchableOpacity style={[styles.addButton, { backgroundColor: theme.colors.primary }]} onPress={addSkill}><PhosphorIcon name='plus' size={20} color='white' /></TouchableOpacity>
          </View>
          <View style={styles.skillsContainer}>
            {staffData.skills.map((skill, index) => (<Chip key={index} onClose={() => removeSkill(skill)} style={styles.skill}>{skill}</Chip>))}
          </View>
        </View>

        <View style={styles.bottomButtonContainer}>
          <Button variant='primary' size='md' onPress={handleSave} loading={isSaving} disabled={isSaving} style={styles.bottomSaveButton}>{isEditing ? 'Update' : 'Save Staff Member'}</Button>
        </View>
        <View style={{ height: 32 }} />
      </ScrollView>

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, padding: SPACING.lg },
  section: { marginBottom: SPACING.xl },
  profileSection: { alignItems: 'center', marginBottom: SPACING.lg },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.semibold, marginBottom: SPACING.md },
  input: { marginBottom: SPACING.md },
  row: { flexDirection: 'row', alignItems: 'center', gap: SPACING.md },
  addButton: { width: 56, height: 56, borderRadius: BORDER_RADIUS.md, justifyContent: 'center', alignItems: 'center' },
  switchRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.md },
  switchTitle: { fontSize: TYPOGRAPHY.fontSize.md, fontWeight: TYPOGRAPHY.fontWeight.medium },
  skillsContainer: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.xs, marginTop: SPACING.md },
  skill: { marginBottom: SPACING.xs },
  bottomButtonContainer: { marginTop: SPACING.xl, marginBottom: SPACING.lg },
  bottomSaveButton: { width: '100%' },
});

export default AddStaffScreen;