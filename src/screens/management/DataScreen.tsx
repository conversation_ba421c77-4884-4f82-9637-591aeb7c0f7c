
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, ActivityIndicator } from 'react-native';
import { Text, Surface, Divider, ProgressBar, Title, Paragraph, Portal } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { useNotifications } from '../../services/notificationService';
import { StorageService } from '../../services/storageService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import { ActionSheetOption } from '../../types';

// Constants moved outside the component
const importFormats = [
  { title: 'JSON Format', description: 'Standard JSON file with app data', icon: 'code-json', supported: true, },
  { title: 'CSV Format', description: 'Coming soon', icon: 'file-delimited', supported: false, },
  { title: 'Excel Format', description: 'Coming soon', icon: 'file-excel', supported: false, },
];
const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${['Bytes', 'KB', 'MB', 'GB'][i]}`;
};
const formatDate = (date: Date | null) => {
  if (!date) return 'Never';
  return new Intl.DateTimeFormat('en-US', { dateStyle: 'medium', timeStyle: 'short' } as any).format(date);
};

type ModalConfig = {
  title: string;
  description: string;
  options: ActionSheetOption[];
};

// Main Component
const DataScreen = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const { generateDummyNotifications } = useNotifications();
  const navigation = useNavigation();

  // Component State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backupInfo, setBackupInfo] = useState<{ lastBackup: Date | null; backupSize: number; backupCount: number }>({ lastBackup: null, backupSize: 0, backupCount: 0 });
  const [autoBackup, setAutoBackup] = useState(true);

  // --- FIX: Consolidated all modal/actionsheet states into one ---
  const [modal, setModal] = useState({
    visible: false,
    title: '',
    description: '',
    options: [] as ActionSheetOption[],
  });

  const openModal = (config: Partial<ModalConfig>) => setModal((prev) => ({ ...prev, ...config, visible: true }));
  const closeModal = () => setModal((prev) => ({ ...prev, visible: false }));

  // --- Data Loading ---
  const loadScreenData = useCallback(async () => {
    try {
      const backupKeys = (await StorageService.getAllKeys()).filter(key => key.startsWith('backup_'));
      let totalSize = 0;
      let lastBackupDate: Date | null = null;
      // In a real app, you'd calculate size more efficiently
      if (backupKeys.length > 0) {
        lastBackupDate = new Date(parseInt(backupKeys[0].split('_')[1]));
      }
      setBackupInfo({ lastBackup: lastBackupDate, backupSize: totalSize, backupCount: backupKeys.length });
    } catch (error) {
      LoggingService.error('Failed to load backup info', 'DataScreen', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadScreenData();
  }, [loadScreenData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadScreenData();
  }, [loadScreenData]);

  // --- Handlers ---
  const handleGenerateSampleData = useCallback(() => {
    openModal({
      title: 'Generate Sample Data?',
      description: 'This will clear all existing data and create new sample content. This action cannot be undone.',
      options: [
        { text: 'Cancel', style: 'cancel', onPress: closeModal },
        { text: 'Generate', style: 'destructive', onPress: async () => {
          closeModal();
          setLoading(true);
          try {
            console.log('Starting sample data generation...');
            LoggingService.info('Starting sample data generation', 'DATA_SCREEN');

            // Generate comprehensive sample data
            await actions.generateSampleData();
            console.log('Sample data generated successfully');

            await generateDummyNotifications();
            console.log('Dummy notifications generated successfully');

            openModal({
              title: 'Success!',
              description: 'Sample data and notifications have been generated.',
              options: [{ text: 'OK', onPress: closeModal }],
            });
          } catch (error) {
            console.error('Sample data generation failed:', error);
            LoggingService.error('Sample data generation failed', 'DATA_SCREEN', error as Error);
            openModal({
              title: 'Error',
              description: `Failed to generate sample data: ${(error as Error).message}`,
              options: [{ text: 'OK', onPress: closeModal }]
            });
          } finally {
            setLoading(false);
            loadScreenData(); // Refresh info
          }
        }},
      ],
    });
  }, [actions, generateDummyNotifications, openModal, closeModal, loadScreenData]);
  
  const handleCreateBackup = useCallback(() => {
    openModal({
      title: 'Create Backup?',
      description: 'A new backup of your current data will be created.',
      options: [
        { text: 'Cancel', style: 'cancel', onPress: closeModal },
        { text: 'Create', style: 'primary', onPress: async () => {
          closeModal();
          setIsBackingUp(true);
          try {
            const backupId = `backup_${Date.now()}`;
            await StorageService.set(backupId, { timestamp: Date.now(), data: state });
            await loadScreenData();
            openModal({ title: 'Success!', description: 'Backup created successfully.', options: [{ text: 'OK', onPress: closeModal }] });
          } catch (error) {
            openModal({ title: 'Error', description: 'Failed to create backup.', options: [{ text: 'OK', onPress: closeModal }] });
          } finally {
            setIsBackingUp(false);
          }
        }},
      ],
    });
  }, [state, openModal, closeModal, loadScreenData]);

  const handleExportData = useCallback(() => {
    const data = actions.exportData();
    openModal({
      title: 'Export Data',
      description: `Data has been prepared. File export is coming soon.\n\nProducts: ${data.products.length}\nCustomers: ${data.customers.length}\nOrders: ${data.orders.length}`,
      options: [{ text: 'OK', onPress: closeModal }],
    });
  }, [actions, openModal, closeModal]);
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>Loading Data...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Data Management" showBack onBackPress={() => navigation.goBack()} />
      <ScrollView
        contentContainerStyle={{ padding: SPACING.lg, paddingBottom: insets.bottom + 20 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Backup & Restore</Text>
          <View style={styles.infoRow}>
            <Text>Last Backup:</Text>
            <Text>{formatDate(backupInfo.lastBackup)}</Text>
          </View>
          <View style={styles.buttonContainer}>
            <Button variant="primary" onPress={handleCreateBackup} loading={isBackingUp} disabled={isBackingUp}>Create Backup</Button>
            <Button variant="outline" disabled={backupInfo.backupCount === 0}>Restore Backup</Button>
          </View>
        </Surface>

        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Import / Export</Text>
          <Paragraph style={styles.description}>Move your data in or out of the app.</Paragraph>
           <View style={styles.buttonContainer}>
             <Button variant="primary" onPress={() => openModal({ title: 'Coming Soon!', description: 'File import will be available in a future update.', options: [{ text: 'OK', onPress: closeModal }] })}>Import Data</Button>
             <Button variant="outline" onPress={handleExportData}>Export Data</Button>
           </View>
        </Surface>

        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Sample Data</Text>
          <Paragraph style={styles.description}>Generate sample data for testing. This will clear existing data.</Paragraph>
          <Button variant="danger" onPress={handleGenerateSampleData} loading={loading}>Generate Sample Data</Button>
        </Surface>
        
        <Surface style={[styles.warningSection, { backgroundColor: theme.colors.errorContainer }]}>
          <PhosphorIcon name="warning" size={24} color={theme.colors.onErrorContainer} />
          <Text style={styles.warningContent}>Important: Always backup data before generating sample data or restoring.</Text>
        </Surface>

      </ScrollView>

      <Portal>
        <ActionSheet
          visible={modal.visible}
          onDismiss={closeModal}
          title={modal.title}
          description={modal.description}
          options={modal.options}
          showCancel={!modal.options.some(opt => opt.style === 'cancel')}
        />
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  loadingText: { marginTop: SPACING.md },
  section: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: '600', marginBottom: SPACING.md },
  description: { marginBottom: SPACING.md, lineHeight: 20 },
  buttonContainer: { flexDirection: 'row', gap: SPACING.md, marginTop: SPACING.sm },
  infoRow: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: SPACING.sm },
  warningSection: { flexDirection: 'row', padding: SPACING.lg, borderRadius: BORDER_RADIUS.lg, alignItems: 'center' },
  warningContent: { flex: 1, marginLeft: SPACING.md, color: 'white' }, // Assuming onErrorContainer is a light color
});

export default DataScreen;