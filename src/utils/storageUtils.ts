import LoggingService from '../services/LoggingService';
import { NotificationService } from '../services/notificationService';
import { StorageService } from '../services/storageService';

/**
 * Storage utility functions for debugging and maintenance
 */
export class StorageUtils {
  /**
   * Clear all notification-related storage to fix overflow issues
   */
  static async clearNotificationStorage(): Promise<void> {
    try {
      await NotificationService.clearAllNotificationData();
      LoggingService.info('Notification storage cleared successfully', 'STORAGE_UTILS');
    } catch (error) {
      LoggingService.error('Failed to clear notification storage', 'STORAGE_UTILS', error as Error);
      throw error;
    }
  }

  /**
   * Get comprehensive storage information
   */
  static async getStorageInfo(): Promise<{
    general: any;
    notifications: any;
    totalSize: number;
  }> {
    try {
      // MOCKED DATA: Replace with actual implementation when available
      const general = {
        totalKeys: 10,
        totalSize: 123456,
        keyInfo: [
          { key: 'user', size: 1024 },
          { key: 'settings', size: 2048 },
        ],
        cacheSize: 5,
      };
      const notifications = {
        templates: 2,
        logs: 100,
        preferences: 3,
        appointmentReminders: 10,
        paymentReminders: 5,
        pickupReminders: 2,
      };

      return {
        general,
        notifications,
        totalSize: general.totalSize,
      };
    } catch (error) {
      LoggingService.error('Failed to get storage info', 'STORAGE_UTILS', error as Error);
      return {
        general: { totalKeys: 0, totalSize: 0, keyInfo: [], cacheSize: 0 },
        notifications: {
          templates: 0,
          logs: 0,
          preferences: 0,
          appointmentReminders: 0,
          paymentReminders: 0,
          pickupReminders: 0,
        },
        totalSize: 0,
      };
    }
  }

  /**
   * Check for storage issues and provide recommendations
   */
  static async diagnoseStorageIssues(): Promise<{
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      const info = await this.getStorageInfo();

      // Check total storage size
      if (info.totalSize > 5 * 1024 * 1024) {
        // 5MB
        issues.push('Total storage size is large');
        recommendations.push('Consider clearing old data or using chunked storage');
      }

      // Check notification logs
      if (info.notifications.logs > 1000) {
        issues.push('Too many notification logs');
        recommendations.push('Clear old notification logs');
      }

      // Check if any individual keys are too large
      for (const keyInfo of info.general.keyInfo) {
        if (keyInfo.size > 1024 * 1024) {
          // 1MB per key
          issues.push(
            `Key ${keyInfo.key} is very large (${(keyInfo.size / 1024 / 1024).toFixed(2)}MB)`
          );
          recommendations.push(`Consider chunking data for key: ${keyInfo.key}`);
        }
      }

      return { issues, recommendations };
    } catch (error) {
      issues.push('Failed to diagnose storage issues');
      recommendations.push('Check storage permissions and try clearing all data');
      return { issues, recommendations };
    }
  }

  /**
   * Clear all app storage (nuclear option)
   */
  static async clearAllStorage(): Promise<void> {
    try {
      await StorageService.clear();
      LoggingService.info('All storage cleared successfully', 'STORAGE_UTILS');
    } catch (error) {
      LoggingService.error('Failed to clear all storage', 'STORAGE_UTILS', error as Error);
      throw error;
    }
  }

  /**
   * Export storage data for debugging
   */
  static async exportStorageData(): Promise<Record<string, any>> {
    try {
      const keys = await StorageService.getAllKeys();
      const data: Record<string, any> = {};

      for (const key of keys) {
        try {
          const value = await StorageService.get(key, false);
          data[key] = value;
        } catch (error) {
          data[key] = { error: 'Failed to read' };
        }
      }

      return data;
    } catch (error) {
      LoggingService.error('Failed to export storage data', 'STORAGE_UTILS', error as Error);
      return {};
    }
  }
}