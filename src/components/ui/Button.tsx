import React, { useMemo } from 'react';
import {
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  GestureResponderEvent,
} from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- Type Definitions ---

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'text';
type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'fullWidth';
type IconPosition = 'left' | 'right';
type Theme = ReturnType<typeof useTheme>;

interface ButtonStyle {
  backgroundColor: string;
  borderColor: string;
  textColor: string;
}

interface SizeStyle {
  paddingVertical: number;
  paddingHorizontal: number;
  fontSize: number;
  borderRadius: number;
}

interface UnifiedButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: PhosphorIconName | React.ReactElement | ((color: string) => React.ReactElement);
  iconPosition?: IconPosition;
  loading?: boolean;
  disabled?: boolean;
  rounded?: boolean;
  onPress?: (event: GestureResponderEvent) => void;
  children?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
  textColor?: string;
  accessibilityLabel?: string;
}

// --- Style Constants (Factories) ---

const VARIANT_STYLES = (theme: Theme): Record<ButtonVariant, ButtonStyle> => ({
  primary: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
    textColor: theme.colors.onPrimary,
  },
  secondary: {
    backgroundColor: theme.colors.secondary,
    borderColor: theme.colors.secondary,
    textColor: theme.colors.onSecondary,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.primary,
    textColor: theme.colors.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    textColor: theme.colors.primary,
  },
  danger: {
    backgroundColor: theme.colors.error,
    borderColor: theme.colors.error,
    textColor: theme.colors.onError,
  },
  text: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    textColor: theme.colors.primary,
  },
});

const SIZE_STYLES = (theme: Theme): Record<Exclude<ButtonSize, 'fullWidth'>, SizeStyle> => ({
  xs: { paddingVertical: 2, paddingHorizontal: 8, fontSize: 12, borderRadius: theme.borderRadius.sm },
  sm: { paddingVertical: 6, paddingHorizontal: 12, fontSize: 14, borderRadius: theme.borderRadius.md },
  md: { paddingVertical: 12, paddingHorizontal: 20, fontSize: 16, borderRadius: theme.borderRadius.lg },
  lg: { paddingVertical: 16, paddingHorizontal: 28, fontSize: 18, borderRadius: theme.borderRadius.xl },
});

// --- Main Component ---

const Button: React.FC<UnifiedButtonProps> = React.memo(({
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  rounded = false,
  onPress,
  children,
  style,
  textStyle: textStyleProp,
  textColor: textColorProp,
  accessibilityLabel,
}) => {
  const theme = useTheme();

  // Memoize style calculations for performance
  const { buttonStyle, textStyle, iconColor } = useMemo(() => {
    const variantStyles = VARIANT_STYLES(theme)[variant];
    const sizeStyles = size !== 'fullWidth' ? SIZE_STYLES(theme)[size] : SIZE_STYLES(theme).md;

    const resolvedTextColor = textColorProp || variantStyles.textColor;

    const baseButtonStyle: ViewStyle = {
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
      borderWidth: variant === 'outline' ? 1.5 : 0,
      borderRadius: rounded ? 9999 : sizeStyles.borderRadius,
      paddingVertical: sizeStyles.paddingVertical,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      opacity: disabled || loading ? 0.6 : 1,
      width: size === 'fullWidth' ? '100%' : undefined,
    };

    const combinedButtonStyle = StyleSheet.flatten([styles.button, baseButtonStyle, style]);

    const combinedTextStyle = StyleSheet.flatten([
      { color: resolvedTextColor, fontSize: sizeStyles.fontSize, fontWeight: 'bold' as const },
      textStyleProp,
    ]);

    return { buttonStyle: combinedButtonStyle, textStyle: combinedTextStyle, iconColor: resolvedTextColor };
  }, [variant, size, rounded, disabled, loading, theme, style, textStyleProp, textColorProp]);

  const renderIcon = () => {
    if (!icon) return null;
    const iconStyle = iconPosition === 'left' ? styles.iconLeft : styles.iconRight;

    if (typeof icon === 'string') {
      return <PhosphorIcon name={icon} size={20} color={iconColor} style={iconStyle} />;
    }
    if (typeof icon === 'function') {
      return icon(iconColor);
    }
    // **FIX:** Assert the type of `icon` to inform TypeScript it's an element that can accept a style prop.
    if (React.isValidElement(icon)) {
      const element = icon as React.ReactElement<{ style?: ViewStyle | TextStyle[] }>;
      return React.cloneElement(element, {
        style: StyleSheet.flatten([element.props.style, iconStyle]),
      });
    }
    return null;
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      activeOpacity={0.8}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole='button'
      accessibilityLabel={accessibilityLabel || (typeof children === 'string' ? children : 'Button')}
    >
      {loading && <ActivityIndicator color={iconColor} style={styles.iconLeft} />}
      {!loading && iconPosition === 'left' && renderIcon()}
      {children != null && <Text style={textStyle}>{children}</Text>}
      {!loading && iconPosition === 'right' && renderIcon()}
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  button: {
    marginVertical: 4,
    minHeight: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});

export default Button;