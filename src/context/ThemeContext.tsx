import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

import LoggingService from '../services/LoggingService';
import { StorageService } from '../services/storageService';
import {
  lightTheme,
  darkTheme,
  SPACING,
  BORDER_RADIUS,
  TYPOGRAPHY,
  SHADOWS,
  COMPONENT_SIZES,
  LAYOUT,
  ANIMATIONS,
  OPACITY,
  Z_INDEX,
  BREAKPOINTS,
  COMMON_STYLES,
} from '../theme/theme';

// Theme interfaces
interface ThemeColors {
  primary: string;
  primaryContainer: string;
  secondary: string;
  secondaryContainer: string;
  tertiary: string;
  tertiaryContainer: string;
  surface: string;
  surfaceVariant: string;
  background: string;
  error: string;
  errorContainer: string;
  onPrimary: string;
  onPrimaryContainer: string;
  onSecondary: string;
  onSecondaryContainer: string;
  onTertiary: string;
  onTertiaryContainer: string;
  onSurface: string;
  onSurfaceVariant: string;
  on: string;
  onVariant: string;
  onError: string;
  onErrorContainer: string;
  onBackground: string;
  outline: string;
  outlineVariant: string;
  inverse: string;
  inverseOn: string;
  inversePrimary: string;
  // Additional colors needed by components
  primaryDark: string;
  errorDark: string;
  warning: string;
  warningDark: string;
  success: string;
  successDark: string;
  info: string;
  infoDark: string;
  // Toast-specific colors
  toast: {
    surface: string;
    surfaceVariant: string;
    success: string;
    error: string;
    warning: string;
    info: string;
    successText: string;
    errorText: string;
    warningText: string;
    infoText: string;
    defaultText: string;
  };
}

interface Theme {
  colors: ThemeColors;
  spacing: typeof SPACING;
  borderRadius: typeof BORDER_RADIUS;
  typography: typeof TYPOGRAPHY;
  shadows: typeof SHADOWS;
  componentSizes: typeof COMPONENT_SIZES;
  layout: typeof LAYOUT;
  animations: typeof ANIMATIONS;
  opacity: typeof OPACITY;
  zIndex: typeof Z_INDEX;
  breakpoints: typeof BREAKPOINTS;
  commonStyles: typeof COMMON_STYLES;
  mode: 'light' | 'dark';
}

interface ThemeContextType extends Theme {
  isDarkMode: boolean;
  toggleTheme: () => Promise<void>;
  isLoading: boolean;
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

// Helper function to create a timeout promise
const withTimeout = <T,>(promise: Promise<T>, timeoutMs: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Operation timed out')), timeoutMs)
    ),
  ]);
};

// Preload theme data for faster access
const preloadThemeData = () => {
  // Preload both themes to ensure they're available immediately
  const lightThemeData: Theme = {
    ...lightTheme,
    spacing: SPACING,
    borderRadius: BORDER_RADIUS,
    typography: TYPOGRAPHY,
    shadows: SHADOWS,
    componentSizes: COMPONENT_SIZES,
    layout: LAYOUT,
    animations: ANIMATIONS,
    opacity: OPACITY,
    zIndex: Z_INDEX,
    breakpoints: BREAKPOINTS,
    commonStyles: COMMON_STYLES,
    mode: 'light',
  };

  const darkThemeData: Theme = {
    ...darkTheme,
    spacing: SPACING,
    borderRadius: BORDER_RADIUS,
    typography: TYPOGRAPHY,
    shadows: SHADOWS,
    componentSizes: COMPONENT_SIZES,
    layout: LAYOUT,
    animations: ANIMATIONS,
    opacity: OPACITY,
    zIndex: Z_INDEX,
    breakpoints: BREAKPOINTS,
    commonStyles: COMMON_STYLES,
    mode: 'dark',
  };

  return { lightThemeData, darkThemeData };
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Preload theme data on mount
  const { lightThemeData, darkThemeData } = preloadThemeData();

  // Load theme preference on app start
  useEffect(() => {
    loadThemePreference();
  }, []);

  const loadThemePreference = async (): Promise<void> => {
    try {
      // Use StorageService with timeout for better performance and caching
      const savedTheme = await withTimeout(
        StorageService.get<boolean>('darkMode', true, 10 * 60 * 1000), // 10 minute cache
        1000 // Reduced to 1 second timeout for even faster loading
      );

      if (savedTheme !== null) {
        setIsDarkMode(savedTheme);
      }
      LoggingService.info('Theme preference loaded successfully', 'THEME');
    } catch (error) {
      LoggingService.warn(
        'Theme loading timeout or error, using default light theme',
        'THEME',
        error as Error
      );
      // Use default light theme on timeout or error
      setIsDarkMode(false);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTheme = async (): Promise<void> => {
    try {
      const newTheme = !isDarkMode;
      setIsDarkMode(newTheme);

      // Use StorageService with timeout for better performance
      await withTimeout(
        StorageService.set('darkMode', newTheme, true),
        3000 // Increased to 3 seconds
      );

      LoggingService.info(`Theme switched to ${newTheme ? 'dark' : 'light'} mode`, 'THEME');
    } catch (error) {
      LoggingService.error('Error saving theme preference', 'THEME', error as Error);
      // Revert the state change if save fails
      setIsDarkMode(!isDarkMode);
    }
  };

  // Use preloaded theme data for better performance
  const theme: Theme = isDarkMode ? darkThemeData : lightThemeData;

  const value: ThemeContextType = {
    isDarkMode,
    ...theme,
    theme,
    toggleTheme,
    isLoading,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    // Return a minimal default theme instead of throwing an error
    const baseTheme: Theme = {
      colors: {
        primary: '#1877F2',
        primaryContainer: '#E3F2FD',
        secondary: '#42A5F5',
        secondaryContainer: '#E1F5FE',
        tertiary: '#FF6B35',
        tertiaryContainer: '#FFE0B2',
        surface: '#FFFFFF',
        surfaceVariant: '#F0F2F5',
        background: '#F8F9FA',
        error: '#F44336',
        errorContainer: '#FFEBEE',
        onPrimary: '#FFFFFF',
        onPrimaryContainer: '#0D47A1',
        onSecondary: '#FFFFFF',
        onSecondaryContainer: '#01579B',
        onTertiary: '#FFFFFF',
        onTertiaryContainer: '#E65100',
        onSurface: '#1C1E21',
        onSurfaceVariant: '#65676B',
        on: '#1C1E21',
        onVariant: '#65676B',
        onError: '#FFFFFF',
        onErrorContainer: '#B71C1C',
        onBackground: '#1C1E21',
        outline: '#CED0D4',
        outlineVariant: '#E4E6EA',
        inverse: '#242526',
        inverseOn: '#E4E6EA',
        inversePrimary: '#4FC3F7',
        // Additional colors needed by components
        primaryDark: '#0D47A1',
        errorDark: '#D32F2F',
        warning: '#FF9800',
        warningDark: '#F57C00',
        success: '#4CAF50',
        successDark: '#388E3C',
        info: '#2196F3',
        infoDark: '#1976D2',
        // Toast-specific colors
        toast: {
          surface: '#FFFFFF',
          surfaceVariant: '#F8F9FA',
          success: '#10B981',
          error: '#EF4444',
          warning: '#F59E0B',
          info: '#3B82F6',
          successText: '#065F46',
          errorText: '#991B1B',
          warningText: '#92400E',
          infoText: '#1E40AF',
          defaultText: '#374151',
        },
      },
      spacing: SPACING,
      borderRadius: BORDER_RADIUS,
      typography: TYPOGRAPHY,
      shadows: SHADOWS,
      componentSizes: COMPONENT_SIZES,
      layout: LAYOUT,
      animations: ANIMATIONS,
      opacity: OPACITY,
      zIndex: Z_INDEX,
      breakpoints: BREAKPOINTS,
      commonStyles: COMMON_STYLES,
      mode: 'light',
    };

    const defaultTheme: ThemeContextType = {
      ...baseTheme,
      isDarkMode: false,
      toggleTheme: async () => {},
      isLoading: false,
      theme: baseTheme,
    };
    return defaultTheme;
  }
  return context;
};

export default ThemeContext;
