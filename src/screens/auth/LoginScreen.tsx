import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Text, TextInput, Surface } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { AuthStackParamList } from '../../navigation/AuthNavigator';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// **FIX:** Derive the theme type directly from the useTheme hook's return value.
type ThemeType = ReturnType<typeof useTheme>;

type UnifiedLoginNavigationProp = StackNavigationProp<AuthStackParamList, 'Login'>;
interface FormErrors { email?: string; password?: string; adminCode?: string; }

// --- Constants ---
const DEMO_USERS = [
  { role: 'Admin', email: '<EMAIL>', password: 'owner123' },
  { role: 'Manager', email: '<EMAIL>', password: 'admin123', adminCode: 'ADMIN2024' },
  { role: 'Staff', email: '<EMAIL>', password: 'staff123' },
  { role: 'User', email: '<EMAIL>', password: 'demo123' },
];

// --- Memoized Header Component ---
const LoginHeader = React.memo(() => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);

  return (
    <View style={styles.header}>
      <Surface style={styles.logo}>
        <PhosphorIcon name='lock' size={40} color={theme.colors.onPrimary} />
      </Surface>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>Welcome Back!</Text>
      <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
        Sign in to your TailorZap account
      </Text>
    </View>
  );
});

// --- Main Screen Component ---
const LoginScreen: React.FC = () => {
  const theme = useTheme();
  const { state, login, clearError } = useAuth();
  const navigation = useNavigation<UnifiedLoginNavigationProp>();
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [adminCode, setAdminCode] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});

  const [showDemoActionSheet, setShowDemoActionSheet] = useState(false);

  const requiresAdminCode = useMemo(() => {
    const emailLower = email.toLowerCase();
    return emailLower.includes('admin') || emailLower.includes('manager');
  }, [email]);

  const createChangeHandler = (setter: React.Dispatch<React.SetStateAction<string>>, field: keyof FormErrors) =>
    (text: string) => {
      if (state.error) clearError();
      if (errors[field]) setErrors(prev => ({ ...prev, [field]: undefined }));
      setter(text);
    };

  const validateForm = useCallback(() => {
    const newErrors: FormErrors = {};
    if (!email) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = 'Email is invalid';
    if (!password) newErrors.password = 'Password is required';
    if (requiresAdminCode && !adminCode) newErrors.adminCode = 'Admin code is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [email, password, adminCode, requiresAdminCode]);

  const handleLogin = useCallback(async () => {
    if (state.isLoading || !validateForm()) return;

    try {
      const emailLower = email.toLowerCase();
      const userType = (emailLower.includes('admin') || emailLower.includes('manager')) ? 'admin' :
                       (emailLower.includes('staff') || emailLower.includes('employee')) ? 'staff' : 'user';

      await login({ email, password, userType, adminCode: requiresAdminCode ? adminCode : undefined });
    } catch (err) {
      LoggingService.error('Login attempt failed in LoginScreen', 'AUTH', err as Error);
    }
  }, [email, password, adminCode, requiresAdminCode, login, validateForm, state.isLoading]);

  const handleDemoLogin = useCallback((user: typeof DEMO_USERS[0]) => {
    setEmail(user.email);
    setPassword(user.password);
    setAdminCode(user.adminCode || '');
    setErrors({});
    if (state.error) clearError();
    setShowDemoActionSheet(false);
  }, [state.error, clearError]);

  const demoOptions = useMemo(() => DEMO_USERS.map(user => ({
    text: `${user.role} (${user.email})`,
    onPress: () => handleDemoLogin(user),
  })), [handleDemoLogin]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps='handled'>
          <Surface style={styles.card}>
            <LoginHeader />
            {state.error && (
              <View style={styles.authError}>
                <Text style={styles.authErrorText}>{state.error}</Text>
              </View>
            )}
            <View style={styles.form}>
              <View>
                <TextInput
                  label='Email Address' value={email} onChangeText={createChangeHandler(setEmail, 'email')}
                  mode='outlined' style={styles.input} error={!!errors.email} disabled={state.isLoading}
                />
                {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
              </View>
              <View>
                <TextInput
                  label='Password' value={password} onChangeText={createChangeHandler(setPassword, 'password')}
                  mode='outlined' style={styles.input} secureTextEntry error={!!errors.password} disabled={state.isLoading}
                />
                {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}
              </View>
              {requiresAdminCode && (
                <View>
                  <TextInput
                    label='Admin Access Code' value={adminCode} onChangeText={createChangeHandler(setAdminCode, 'adminCode')}
                    mode='outlined' style={styles.input} secureTextEntry error={!!errors.adminCode} disabled={state.isLoading}
                  />
                  {errors.adminCode && <Text style={styles.errorText}>{errors.adminCode}</Text>}
                </View>
              )}
              <Button onPress={handleLogin} style={styles.loginButtonPrimary} disabled={state.isLoading}>
                {state.isLoading ? <ActivityIndicator size='small' color={theme.colors.onPrimary} /> : 'Sign In'}
              </Button>
              <Button onPress={() => setShowDemoActionSheet(true)} style={styles.loginButtonSecondary} disabled={state.isLoading}>
                Demo Login
              </Button>
            </View>
          </Surface>
        </ScrollView>
      </KeyboardAvoidingView>
      <ActionSheet
        visible={showDemoActionSheet}
        onDismiss={() => setShowDemoActionSheet(false)}
        title='Select Demo Role'
        description='Choose a role to pre-fill demo credentials.'
        options={demoOptions}
      />
    </SafeAreaView>
  );
};

// --- Stylesheet Factory ---
// **FIX:** Use the derived ThemeType for the function parameter
const createStyles = (theme: ThemeType) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.colors.background },
  scrollContainer: { flexGrow: 1, justifyContent: 'center', padding: SPACING.lg },
  card: { padding: SPACING.xl, borderRadius: BORDER_RADIUS.lg, elevation: 4, borderColor: theme.colors.outline, borderWidth: 1 },
  header: { alignItems: 'center', marginBottom: SPACING.xl },
  logo: { width: 80, height: 80, borderRadius: 40, alignItems: 'center', justifyContent: 'center', marginBottom: SPACING.md, backgroundColor: theme.colors.primary },
  title: { fontSize: TYPOGRAPHY.fontSize.xxl, fontWeight: TYPOGRAPHY.fontWeight.bold, marginBottom: SPACING.xs },
  subtitle: { fontSize: TYPOGRAPHY.fontSize.md, textAlign: 'center', marginBottom: SPACING.xs },
  form: { gap: SPACING.md },
  input: { backgroundColor: theme.colors.surface },
  errorText: { color: theme.colors.error, fontSize: TYPOGRAPHY.fontSize.sm, marginTop: SPACING.xs, paddingLeft: SPACING.xs },
  loginButton: { marginTop: SPACING.md, paddingVertical: SPACING.xs },
  loginButtonPrimary: { marginTop: SPACING.md, paddingVertical: SPACING.xs, backgroundColor: theme.colors.primary },
  loginButtonSecondary: { marginTop: SPACING.sm, paddingVertical: SPACING.xs, backgroundColor: theme.colors.secondary },
  authError: { padding: SPACING.md, borderRadius: BORDER_RADIUS.md, marginBottom: SPACING.md, backgroundColor: theme.colors.errorContainer },
  authErrorText: { fontSize: TYPOGRAPHY.fontSize.sm, textAlign: 'center', color: theme.colors.onErrorContainer },
});

export default LoginScreen;