/**
 * Stock Level Indicator Component
 * Visual indicator for stock levels with color-coded status
 */

import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, ProgressBar } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

type StockStatus = 'out-of-stock' | 'low-stock' | 'adequate' | 'high-stock';
type IndicatorSize = 'small' | 'medium' | 'large';

interface StockLevelIndicatorProps {
  currentStock: number;
  minimumLevel: number;
  unit: string;
  size?: IndicatorSize;
  showProgressBar?: boolean;
  showIcon?: boolean;
  showText?: boolean;
  style?: ViewStyle;
  maxLevel?: number; // For progress bar calculation
  precision?: number;
}

const StockLevelIndicator: React.FC<StockLevelIndicatorProps> = ({
  currentStock,
  minimumLevel,
  unit,
  size = 'medium',
  showProgressBar = true,
  showIcon = true,
  showText = true,
  style,
  maxLevel,
  precision = 2,
}) => {
  const theme = useTheme();

  // Calculate stock status and colors
  const stockInfo = useMemo(() => {
    let status: StockStatus;
    let color: string;
    let backgroundColor: string;
    let iconName: string;
    let statusText: string;
    let progress: number = 0;

    if (currentStock <= 0) {
      status = 'out-of-stock';
      color = theme.colors.error;
      backgroundColor = `${theme.colors.error}15`;
      iconName = 'x-circle';
      statusText = 'Out of Stock';
      progress = 0;
    } else if (currentStock <= minimumLevel) {
      status = 'low-stock';
      color = theme.colors.tertiary; // Use tertiary color for warning
      backgroundColor = `${theme.colors.tertiary}15`;
      iconName = 'warning';
      statusText = 'Low Stock';
      progress = Math.min(currentStock / (minimumLevel * 2), 0.5);
    } else if (currentStock <= minimumLevel * 2) {
      status = 'adequate';
      color = theme.colors.primary;
      backgroundColor = `${theme.colors.primary}15`;
      iconName = 'check-circle';
      statusText = 'Adequate';
      progress = Math.min(currentStock / (minimumLevel * 3), 0.8);
    } else {
      status = 'high-stock';
      color = theme.colors.primary; // Use primary color for good stock
      backgroundColor = `${theme.colors.primary}15`;
      iconName = 'check-circle';
      statusText = 'Good Stock';
      progress = maxLevel ? Math.min(currentStock / maxLevel, 1) : 0.9;
    }

    return {
      status,
      color,
      backgroundColor,
      iconName,
      statusText,
      progress,
    };
  }, [currentStock, minimumLevel, maxLevel, theme.colors]);

  // Size configurations
  const sizeConfig = useMemo(() => {
    switch (size) {
      case 'small':
        return {
          iconSize: 16,
          textSize: 12,
          quantitySize: 14,
          containerPadding: 8,
          progressHeight: 4,
        };
      case 'large':
        return {
          iconSize: 24,
          textSize: 16,
          quantitySize: 20,
          containerPadding: 16,
          progressHeight: 8,
        };
      default: // medium
        return {
          iconSize: 20,
          textSize: 14,
          quantitySize: 16,
          containerPadding: 12,
          progressHeight: 6,
        };
    }
  }, [size]);

  // Format stock quantity
  const formattedStock = UnitConverter.formatQuantity(currentStock, unit, precision);
  const formattedMinimum = UnitConverter.formatQuantity(minimumLevel, unit, precision);

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: stockInfo.backgroundColor,
          padding: sizeConfig.containerPadding,
        },
        style,
      ]}
    >
      {/* Header Row */}
      <View style={styles.headerRow}>
        {showIcon && (
          <PhosphorIcon
            name={stockInfo.iconName as any}
            size={sizeConfig.iconSize}
            color={stockInfo.color}
          />
        )}

        {showText && (
          <View style={styles.textContainer}>
            <Text
              style={[
                styles.statusText,
                {
                  color: stockInfo.color,
                  fontSize: sizeConfig.textSize,
                  fontWeight: '600',
                },
              ]}
            >
              {stockInfo.statusText}
            </Text>

            <Text
              style={[
                styles.quantityText,
                {
                  color: theme.colors.onSurface,
                  fontSize: sizeConfig.quantitySize,
                  fontWeight: 'bold',
                },
              ]}
            >
              {formattedStock}
            </Text>
          </View>
        )}
      </View>

      {/* Progress Bar */}
      {showProgressBar && (
        <View style={styles.progressContainer}>
          <ProgressBar
            progress={stockInfo.progress}
            color={stockInfo.color}
            style={[styles.progressBar, { height: sizeConfig.progressHeight }]}
          />

          {/* Minimum Level Indicator */}
          {minimumLevel > 0 && (
            <View style={styles.minimumIndicator}>
              <View
                style={[
                  styles.minimumLine,
                  {
                    backgroundColor: theme.colors.onSurfaceVariant,
                    left: `${Math.min((minimumLevel / (maxLevel || minimumLevel * 3)) * 100, 100)}%`,
                  },
                ]}
              />
              <Text
                style={[
                  styles.minimumText,
                  {
                    color: theme.colors.onSurfaceVariant,
                    fontSize: sizeConfig.textSize - 2,
                  },
                ]}
              >
                Min: {formattedMinimum}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Stock Details */}
      {size === 'large' && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>
              Current:
            </Text>
            <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
              {formattedStock}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>
              Minimum:
            </Text>
            <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
              {formattedMinimum}
            </Text>
          </View>

          {maxLevel && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>
                Maximum:
              </Text>
              <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                {UnitConverter.formatQuantity(maxLevel, unit, precision)}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Stock Percentage (for large size) */}
      {size === 'large' && minimumLevel > 0 && (
        <View style={styles.percentageContainer}>
          <Text style={[styles.percentageText, { color: stockInfo.color }]}>
            {Math.round((currentStock / minimumLevel) * 100)}% of minimum level
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginVertical: 4,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  textContainer: {
    flex: 1,
  },
  statusText: {
    fontWeight: '600',
  },
  quantityText: {
    marginTop: 2,
  },
  progressContainer: {
    marginTop: 8,
    position: 'relative',
  },
  progressBar: {
    borderRadius: 3,
  },
  minimumIndicator: {
    marginTop: 4,
    position: 'relative',
  },
  minimumLine: {
    position: 'absolute',
    top: -8,
    width: 2,
    height: 12,
  },
  minimumText: {
    textAlign: 'center',
    marginTop: 2,
  },
  detailsContainer: {
    marginTop: 12,
    gap: 4,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  percentageContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  percentageText: {
    fontSize: 11,
    fontWeight: '500',
  },
});

export default StockLevelIndicator;
