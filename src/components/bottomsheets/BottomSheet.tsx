import GorhomBottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import React, { forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface BottomSheetProps {
  children: React.ReactNode;
  title?: string;
  snapPoints?: string[];
  onClose?: () => void;
  showCloseButton?: boolean;
  initialIndex?: number;
}

export interface BottomSheetRef {
  present: () => void;
  open: () => void;
  close: () => void;
  expand: () => void;
  collapse: () => void;
}

const BottomSheet = memo(
  forwardRef<BottomSheetRef, BottomSheetProps>(
    (
      { children, title, snapPoints = ['50%'], onClose, showCloseButton = true, initialIndex = -1 },
      ref
    ) => {
      const theme = useTheme();
      const bottomSheetRef = useRef<GorhomBottomSheet>(null);

      // This callback now directly depends on `onClose`, which is cleaner.
      const handleSheetChanges = useCallback((index: number) => {
        if (index === -1) {
          onClose?.();
        }
      }, [onClose]);

      // This function is static and doesn't need to be recreated.
      const renderBackdrop = useCallback(
        (props: BottomSheetBackdropProps) => (
          <BottomSheetBackdrop
            {...props}
            disappearsOnIndex={-1}
            appearsOnIndex={0}
            opacity={0.5}
            pressBehavior="close"
          />
        ),
        []
      );

      // All dynamic, theme-dependent styles are memoized together here.
      const themedStyles = useMemo(
        () => ({
          background: { backgroundColor: theme.colors.surface },
          handleIndicator: { backgroundColor: theme.colors.outlineVariant },
          header: { borderBottomColor: theme.colors.outlineVariant },
          title: {
            color: theme.colors.onSurface,
            fontSize: TYPOGRAPHY.fontSize.lg,
            fontWeight: TYPOGRAPHY.fontWeight.bold,
          },
          closeIconColor: theme.colors.onSurfaceVariant,
        }),
        [theme.colors]
      );

      // Expose controls to the parent component in a more compact way.
      useImperativeHandle(ref, () => ({
          present: () => bottomSheetRef.current?.snapToIndex(0),
          open: () => bottomSheetRef.current?.snapToIndex(0),
          close: () => bottomSheetRef.current?.close(),
          expand: () => bottomSheetRef.current?.expand(),
          collapse: () => bottomSheetRef.current?.collapse(),
        }),
        []
      );

      return (
        <GorhomBottomSheet
          ref={bottomSheetRef}
          index={initialIndex}
          snapPoints={snapPoints}
          onChange={handleSheetChanges}
          backdropComponent={renderBackdrop}
          keyboardBehavior="extend"
          backgroundStyle={themedStyles.background}
          handleIndicatorStyle={themedStyles.handleIndicator}
          style={styles.bottomSheet}
        >
          <View style={styles.container}>
            {title && (
              <View style={[styles.header, themedStyles.header]}>
                <Text style={themedStyles.title}>{title}</Text>
                {showCloseButton && (
                  <TouchableOpacity onPress={() => bottomSheetRef.current?.close()}>
                    <PhosphorIcon name="x" size={24} color={themedStyles.closeIconColor} />
                  </TouchableOpacity>
                )}
              </View>
            )}
            <BottomSheetView style={styles.content}>{children}</BottomSheetView>
          </View>
        </GorhomBottomSheet>
      );
    }
  )
);

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  content: {
    flex: 1,
  },
});

export default BottomSheet;