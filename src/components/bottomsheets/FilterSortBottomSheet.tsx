import React, { useState, useCallback, forwardRef, useEffect, memo, useMemo, useRef, useImperativeHandle } from 'react'; // 👈 Fixed: Added useMemo
import { View, StyleSheet, FlatList, ListRenderItem } from 'react-native';
import { Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TabView, SceneRendererProps, NavigationState } from 'react-native-tab-view';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import OriginalButton from '../ui/Button';
import OriginalItemRow from '../ui/ItemRow';
import BottomSheet, { BottomSheetRef } from './BottomSheet';

// --- Type Inference and Component Casting ---
type Theme = ReturnType<typeof useTheme>;

interface ItemRowProps {
  children: React.ReactNode;
  active: boolean;
  onPress: () => void;
  theme: Theme;
}
const ItemRow = OriginalItemRow as React.FC<ItemRowProps>;

interface ButtonProps extends React.ComponentProps<typeof OriginalButton> {}
const Button = OriginalButton as React.FC<ButtonProps>;


// --- Types ---
interface SortOption {
  key: string;
  label: string;
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

interface FilterSortBottomSheetProps {
  filters?: string[];
  selectedFilter?: string;
  onSelectFilter: (filter: string) => void;
  sortOptions?: SortOption[];
  selectedSort?: SelectedSort;
  onSelectSort: React.Dispatch<React.SetStateAction<SelectedSort | undefined>>;
  onConfirm: () => void;
  title?: string;
  snapPoints?: string[];
}

type TabRoute = {
  key: 'filters' | 'sorting';
  title: string;
};

// --- Sub-components ---

const FilterTab = memo(
  ({ filters, selectedFilter, onSelectFilter, theme }: { filters: string[]; selectedFilter?: string; onSelectFilter: (filter: string) => void; theme: Theme; }) => {
    const renderItem: ListRenderItem<string> = useCallback(({ item: filter }) => {
      const active = selectedFilter === filter;
      return (
        <ItemRow active={active} onPress={() => onSelectFilter(filter)} theme={theme}>
          <Text style={[styles.tabLabel, { color: active ? theme.colors.primary : theme.colors.onSurfaceVariant }]}>{filter}</Text>
          <PhosphorIcon name={(active ? 'check-circle' : 'circle') as PhosphorIconName} size={22} color={active ? theme.colors.primary : theme.colors.onSurfaceVariant} />
        </ItemRow>
      );
    }, [selectedFilter, onSelectFilter, theme]);

    return ( <FlatList data={filters} renderItem={renderItem} keyExtractor={item => item} contentContainerStyle={styles.tabContentContainer} /> );
  }
);

const SortTab = memo(
  ({ sortOptions, selectedSort, onSelectSort, theme }: { sortOptions: SortOption[]; selectedSort?: SelectedSort; onSelectSort: React.Dispatch<React.SetStateAction<SelectedSort | undefined>>; theme: Theme; }) => {
    const handleSelect = useCallback((option: SortOption) => {
      onSelectSort(prev => {
        if (!prev || prev.key !== option.key) {
          return { key: option.key, direction: 'asc' };
        }
        return { ...prev, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      });
    }, [onSelectSort]);

    const renderItem: ListRenderItem<SortOption> = useCallback(({ item: option }) => {
      const active = selectedSort?.key === option.key;
      return (
        <ItemRow active={active} onPress={() => handleSelect(option)} theme={theme}>
          <Text style={[styles.tabLabel, { color: active ? theme.colors.primary : theme.colors.onSurfaceVariant }]}>
            {option.label} {active ? (selectedSort?.direction === 'asc' ? '\u2191' : '\u2193') : ''}
          </Text>
          <PhosphorIcon name={(active ? 'check-circle' : 'circle') as PhosphorIconName} size={22} color={active ? theme.colors.primary : theme.colors.onSurfaceVariant} />
        </ItemRow>
      );
    }, [selectedSort, handleSelect, theme]);

    return ( <FlatList data={sortOptions} renderItem={renderItem} keyExtractor={item => item.key} contentContainerStyle={styles.tabContentContainer} /> );
  }
);

const SwitchTabBar = ({ navigationState, jumpTo, theme }: SceneRendererProps & { navigationState: NavigationState<TabRoute>; theme: Theme }) => {
  const tabBarStyles = useMemo(() => createTabBarStyles(theme), [theme]);
  return (
    <View style={tabBarStyles.container}>
      {navigationState.routes.map((route, i) => {
        const active = navigationState.index === i;
        return (
          <Button
            key={route.key}
            onPress={() => jumpTo(route.key)}
            style={StyleSheet.flatten([tabBarStyles.button, active && tabBarStyles.buttonActive])}
            // 👇 Fixed: Flatten the textStyle array into an object
            textStyle={StyleSheet.flatten([tabBarStyles.label, active && tabBarStyles.labelActive])}
          >
            {route.title}
          </Button>
        );
      })}
    </View>
  );
};

// --- Main Component ---
const FilterSortBottomSheet = forwardRef<BottomSheetRef, FilterSortBottomSheetProps>(
  ({ filters = [], selectedFilter, onSelectFilter, sortOptions = [], selectedSort, onSelectSort, onConfirm, title = 'Filter & Sort', snapPoints = ['50%'], }, ref) => {
    const theme = useTheme();
    const insets = useSafeAreaInsets();
    const bottomSheetRef = useRef<BottomSheetRef>(null);
    const [tabIndex, setTabIndex] = useState(0);
    const [routes] = useState<TabRoute[]>([
      { key: 'filters', title: 'Filters' },
      { key: 'sorting', title: 'Sorting' },
    ]);

    useImperativeHandle(ref, () => ({
      present: () => bottomSheetRef.current?.open(),
      open: () => bottomSheetRef.current?.open(),
      close: () => bottomSheetRef.current?.close(),
      expand: () => bottomSheetRef.current?.expand(),
      collapse: () => bottomSheetRef.current?.collapse(),
    }));

    const [tempFilter, setTempFilter] = useState(selectedFilter);
    const [tempSort, setTempSort] = useState(selectedSort);

    useEffect(() => {
      setTempFilter(selectedFilter);
      setTempSort(selectedSort);
    }, [selectedFilter, selectedSort]);

    const renderScene = useCallback(
      ({ route }: { route: TabRoute }) => {
        switch (route.key) {
          case 'filters':
            return <FilterTab filters={filters} selectedFilter={tempFilter} onSelectFilter={setTempFilter} theme={theme} />;
          case 'sorting':
            return <SortTab sortOptions={sortOptions} selectedSort={tempSort} onSelectSort={setTempSort} theme={theme} />;
          default:
            return null;
        }
      }, [filters, tempFilter, sortOptions, tempSort, theme]
    );

    const handleShowResults = () => {
      if (onSelectFilter && tempFilter !== undefined) onSelectFilter(tempFilter);
      if (onSelectSort && tempSort) onSelectSort(tempSort);
      if (onConfirm) onConfirm();
    };

    const handleClose = () => {
      setTempFilter(selectedFilter);
      setTempSort(selectedSort);
    };

    return (
      <BottomSheet ref={bottomSheetRef} title={title} snapPoints={snapPoints} onClose={handleClose}>
        <View style={styles.container}>
          <TabView
            navigationState={{ index: tabIndex, routes }}
            renderScene={renderScene}
            onIndexChange={setTabIndex}
            renderTabBar={props => <SwitchTabBar {...props} theme={theme} />}
            lazy
          />
          <View style={[ styles.buttonContainer, { paddingBottom: insets.bottom + 16, borderTopColor: theme.colors.outlineVariant }, ]}>
            <Button variant="primary" onPress={handleShowResults}>Show Results</Button>
          </View>
        </View>
      </BottomSheet>
    );
  }
);

const styles = StyleSheet.create({
  container: { flex: 1, minHeight: 400 },
  buttonContainer: { paddingHorizontal: 16, paddingTop: 8, borderTopWidth: 1 },
  tabContentContainer: { padding: 8, paddingBottom: 16 },
  tabLabel: { flex: 1, fontSize: 16 },
});

const createTabBarStyles = (theme: Theme) => StyleSheet.create({
  container: { flexDirection: 'row', backgroundColor: theme.colors.surfaceVariant, borderRadius: 12, marginHorizontal: 16, marginVertical: 8, padding: 4, },
  button: { flex: 1, borderRadius: 10, elevation: 0, minHeight: 36 },
  buttonActive: { backgroundColor: theme.colors.primaryContainer },
  label: { color: theme.colors.onSurfaceVariant, fontWeight: 'normal' },
  labelActive: { color: theme.colors.onPrimaryContainer, fontWeight: 'bold' },
});

export default FilterSortBottomSheet;