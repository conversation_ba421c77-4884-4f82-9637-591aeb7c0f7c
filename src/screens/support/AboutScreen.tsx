import React, { useMemo, useState, useCallback } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, View } from 'react-native';
import { Card, List, Text } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useTheme } from '../../context/ThemeContext';
import { commonStyles } from '../../theme/commonStyles';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { AboutScreenProps } from '../../types/navigation';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

const appInfo = {
  name: 'TailorZa',
  version: '1.0.0',
  build: '2024.1.0',
  description: 'Complete tailoring business management solution',
  company: 'TailorZa Inc.',
  website: 'https://tailorza.com',
  email: '<EMAIL>',
  phone: '+****************',
};

interface ListItem {
  id: string;
  title: string;
  description: string;
  icon: PhosphorIconName;
  action?: () => void;
}

const sections: { title: string; items: ListItem[] }[] = [
  {
    title: 'Contact Us',
    items: [
      { id: 'email', title: 'Email Support', description: appInfo.email, icon: 'envelope', action: () => Linking.openURL(`mailto:${appInfo.email}`) },
      { id: 'phone', title: 'Phone Support', description: appInfo.phone, icon: 'phone', action: () => Linking.openURL(`tel:${appInfo.phone}`) },
      { id: 'website', title: 'Website', description: appInfo.website, icon: 'globe', action: () => Linking.openURL(appInfo.website) },
    ],
  },
  {
    title: 'Legal',
    items: [
      { id: 'privacy', title: 'Privacy Policy', description: 'How we protect your data', icon: 'shield' },
      { id: 'terms', title: 'Terms of Service', description: 'Our terms and conditions', icon: 'file' },
    ],
  },
];

const allItemsById = new Map<string, ListItem>(
  sections
    .reduce((acc: ListItem[], section) => acc.concat(section.items), [])
    .map((item: ListItem) => [item.id, item])
);


const AboutScreen: React.FC<AboutScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const [activeSheet, setActiveSheet] = useState<string | null>(null);

  const openSheet = useCallback((id: string) => setActiveSheet(id), []);
  const closeSheet = useCallback(() => setActiveSheet(null), []);

  const actionSheetConfig = useMemo(() => {
    if (!activeSheet) return null;
    
    const item = allItemsById.get(activeSheet);
    if (!item) return null;

    const baseConfigs: { [key: string]: { title: string; description: string; options: any[] } } = {
      email: { title: 'Contact via Email', description: 'Would you like to send us an email?', options: [{ text: 'Send Email', style: 'primary', isAction: true }] },
      phone: { title: 'Contact via Phone', description: 'Would you like to call us?', options: [{ text: 'Call Now', style: 'primary', isAction: true }] },
      website: { title: 'Visit Website', description: 'Would you like to visit our website?', options: [{ text: 'Open Website', style: 'primary', isAction: true }] },
      privacy: { title: 'Privacy Policy', description: 'Privacy policy details would be shown here.', options: [{ text: 'Close', style: 'cancel', isAction: true }] },
      terms: { title: 'Terms of Service', description: 'Terms of service details would be shown here.', options: [{ text: 'Close', style: 'cancel', isAction: true }] },
    };

    const config = baseConfigs[activeSheet];
    if (!config) return null;

    if (item.action) {
      config.options[0].onPress = item.action;
    } else {
      config.options[0].onPress = closeSheet;
    }

    return config;
  }, [activeSheet, closeSheet]);
  
  const renderListItemLeft = useCallback(
    (iconName: PhosphorIconName) => (
      <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
        <PhosphorIcon name={iconName} size={20} color={theme.colors.primary} />
      </View>
    ),
    [theme.colors.primary]
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='About' showBack onBackPress={navigation.goBack} />
      <ScrollView style={commonStyles.flex1} showsVerticalScrollIndicator={false}>
        <Card style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.headerContent}>
            <View style={[styles.logoContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name='scissors' size={48} color={theme.colors.primary} />
            </View>
            <Text variant='headlineSmall' style={[styles.appName, { color: theme.colors.onSurface }]}>
              {appInfo.name}
            </Text>
            <Text variant='bodyMedium' style={[styles.version, { color: theme.colors.onSurfaceVariant }]}>
              Version {appInfo.version} (Build {appInfo.build})
            </Text>
            <Text variant='bodyMedium' style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
              {appInfo.description}
            </Text>
          </View>
        </Card>

        {sections.map(section => (
          <Card key={section.title} style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
            <Text variant='titleMedium' style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              {section.title}
            </Text>
            {section.items.map(item => (
              <List.Item
                key={item.id}
                title={item.title}
                description={item.description}
                left={() => renderListItemLeft(item.icon)}
                onPress={() => openSheet(item.id)}
                style={styles.contactItem}
              />
            ))}
          </Card>
        ))}

        <View style={styles.footer}>
          <Text variant='bodySmall' style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
            © {new Date().getFullYear()} {appInfo.company}. All rights reserved.
          </Text>
          <Text variant='bodySmall' style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
            Made with ❤️ for tailors worldwide
          </Text>
        </View>
      </ScrollView>

      {actionSheetConfig && (
        <ActionSheet
          visible={!!activeSheet}
          onDismiss={closeSheet}
          title={actionSheetConfig.title}
          description={actionSheetConfig.description}
          options={[
            ...actionSheetConfig.options.map(opt => ({ ...opt, onPress: () => { closeSheet(); opt.onPress?.(); } })),
            ...((allItemsById.get(activeSheet!)?.action) 
              ? [{ text: 'Cancel', onPress: closeSheet, style: 'cancel', isAction: false }]
              : [])
          ]}
        />
      )}
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  headerContent: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  appName: {
    fontWeight: '700',
    marginBottom: SPACING.xs,
  },
  version: {
    marginBottom: SPACING.sm,
  },
  description: {
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.lg,
  },
  contactItem: {
    paddingVertical: SPACING.xs,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  footerText: {
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
});

export default AboutScreen;