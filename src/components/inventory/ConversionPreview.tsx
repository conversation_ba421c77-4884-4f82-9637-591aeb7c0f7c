/**
 * Conversion Preview Component
 * Shows real-time unit conversions with bidirectional display
 */

import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Card, Divider } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

interface ConversionPreviewProps {
  quantity: number;
  fromUnit: string;
  toUnit: string;
  showBothDirections?: boolean;
  precision?: number;
  style?: ViewStyle;
  compact?: boolean;
  showCalculation?: boolean;
}

const ConversionPreview: React.FC<ConversionPreviewProps> = ({
  quantity,
  fromUnit,
  toUnit,
  showBothDirections = false,
  precision = 2,
  style,
  compact = false,
  showCalculation = false,
}) => {
  const theme = useTheme();

  // Calculate conversions
  const conversions = useMemo(() => {
    try {
      if (quantity <= 0) {
        return {
          forward: { value: 0, isValid: false, error: 'Quantity must be greater than 0' },
          backward: { value: 0, isValid: false, error: 'Quantity must be greater than 0' },
        };
      }

      const forwardValue = UnitConverter.convertBetweenUnits(quantity, fromUnit, toUnit);
      const backwardValue = showBothDirections
        ? UnitConverter.convertBetweenUnits(quantity, toUnit, fromUnit)
        : 0;

      return {
        forward: {
          value: UnitConverter.roundToPrecision(forwardValue, toUnit),
          isValid: true,
          error: null,
        },
        backward: {
          value: showBothDirections ? UnitConverter.roundToPrecision(backwardValue, fromUnit) : 0,
          isValid: showBothDirections,
          error: null,
        },
      };
    } catch (error) {
      LoggingService.warn('Conversion calculation failed', 'CONVERSION_PREVIEW', error as Error);
      return {
        forward: { value: 0, isValid: false, error: (error as Error).message },
        backward: { value: 0, isValid: false, error: (error as Error).message },
      };
    }
  }, [quantity, fromUnit, toUnit, showBothDirections]);

  // Get conversion rate for calculation display
  const conversionRate = useMemo(() => {
    if (!showCalculation) return null;

    try {
      const rate = UnitConverter.getConversionRate(fromUnit, toUnit);
      return rate;
    } catch (error) {
      return null;
    }
  }, [fromUnit, toUnit, showCalculation]);

  // Format unit display names
  const fromUnitDisplay = UnitConverter.getUnitDisplayName(fromUnit);
  const toUnitDisplay = UnitConverter.getUnitDisplayName(toUnit);
  const fromUnitAbbr = UnitConverter.getUnitAbbreviation(fromUnit);
  const toUnitAbbr = UnitConverter.getUnitAbbreviation(toUnit);

  // Don't render if same units
  if (fromUnit === toUnit) {
    return null;
  }

  if (compact) {
    return (
      <View style={[styles.compactContainer, style]}>
        {conversions.forward.isValid ? (
          <Text style={[styles.compactText, { color: theme.colors.onSurfaceVariant }]}>
            {UnitConverter.formatQuantity(quantity, fromUnit, precision)} →{' '}
            {UnitConverter.formatQuantity(conversions.forward.value, toUnit, precision)}
          </Text>
        ) : (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>Conversion error</Text>
        )}
      </View>
    );
  }

  return (
    <Card style={[styles.container, style]} mode='outlined'>
      <Card.Content style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <PhosphorIcon name='arrows-left-right' size={20} color={theme.colors.primary} />
          <Text style={[styles.headerText, { color: theme.colors.primary }]}>Unit Conversion</Text>
        </View>

        {/* Forward Conversion */}
        <View style={styles.conversionRow}>
          <View style={styles.conversionItem}>
            <Text style={[styles.quantityText, { color: theme.colors.onSurface }]}>
              {UnitConverter.formatQuantity(quantity, fromUnit, precision)}
            </Text>
            <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
              {fromUnitDisplay}
            </Text>
          </View>

          <View style={styles.arrowContainer}>
            <PhosphorIcon name='arrow-right' size={16} color={theme.colors.onSurfaceVariant} />
          </View>

          <View style={styles.conversionItem}>
            {conversions.forward.isValid ? (
              <>
                <Text style={[styles.quantityText, { color: theme.colors.primary }]}>
                  {UnitConverter.formatQuantity(conversions.forward.value, toUnit, precision)}
                </Text>
                <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
                  {toUnitDisplay}
                </Text>
              </>
            ) : (
              <Text style={[styles.errorText, { color: theme.colors.error }]}>Error</Text>
            )}
          </View>
        </View>

        {/* Calculation Display */}
        {showCalculation && conversionRate && conversions.forward.isValid && (
          <View style={styles.calculationContainer}>
            <Divider style={{ marginVertical: 8 }} />
            <Text style={[styles.calculationText, { color: theme.colors.onSurfaceVariant }]}>
              1 {fromUnitAbbr} = {conversionRate.toFixed(4)} {toUnitAbbr}
            </Text>
            <Text style={[styles.calculationText, { color: theme.colors.onSurfaceVariant }]}>
              {quantity} × {conversionRate.toFixed(4)} ={' '}
              {conversions.forward.value.toFixed(precision)}
            </Text>
          </View>
        )}

        {/* Backward Conversion */}
        {showBothDirections && (
          <>
            <Divider style={{ marginVertical: 12 }} />
            <View style={styles.conversionRow}>
              <View style={styles.conversionItem}>
                <Text style={[styles.quantityText, { color: theme.colors.onSurface }]}>
                  {UnitConverter.formatQuantity(quantity, toUnit, precision)}
                </Text>
                <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
                  {toUnitDisplay}
                </Text>
              </View>

              <View style={styles.arrowContainer}>
                <PhosphorIcon name='arrow-right' size={16} color={theme.colors.onSurfaceVariant} />
              </View>

              <View style={styles.conversionItem}>
                {conversions.backward.isValid ? (
                  <>
                    <Text style={[styles.quantityText, { color: theme.colors.secondary }]}>
                      {UnitConverter.formatQuantity(
                        conversions.backward.value,
                        fromUnit,
                        precision
                      )}
                    </Text>
                    <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
                      {fromUnitDisplay}
                    </Text>
                  </>
                ) : (
                  <Text style={[styles.errorText, { color: theme.colors.error }]}>Error</Text>
                )}
              </View>
            </View>
          </>
        )}

        {/* Error Display */}
        {conversions.forward.error && (
          <View style={styles.errorContainer}>
            <PhosphorIcon name='warning' size={16} color={theme.colors.error} />
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {conversions.forward.error}
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  content: {
    paddingVertical: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
  },
  conversionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  conversionItem: {
    flex: 1,
    alignItems: 'center',
  },
  arrowContainer: {
    paddingHorizontal: 16,
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  unitText: {
    fontSize: 12,
    marginTop: 2,
  },
  calculationContainer: {
    marginTop: 8,
  },
  calculationText: {
    fontSize: 11,
    textAlign: 'center',
    marginVertical: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    gap: 4,
  },
  errorText: {
    fontSize: 12,
  },
  compactContainer: {
    paddingVertical: 4,
  },
  compactText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default ConversionPreview;
