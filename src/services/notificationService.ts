import React from 'react';
import { AppError } from '../utils/errorHandler';
import LoggingService from './LoggingService';
import { StorageService } from './storageService';


// ---------------- Types ----------------
export interface NotificationTemplate {
  id: string;
  name: string;
  message: string;
  category: NotificationCategory;
  isActive: boolean;
  createdAt: string;
}

export type NotificationCategory =
  | "order_status"
  | "payment"
  | "pickup"
  | "reminder"
  | "general";

export interface NotificationPreference {
  category: NotificationCategory;
  enabled: boolean;
}

export interface NotificationLog {
  id: string;
  category: NotificationCategory;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

export interface PickupReminder {
  orderId: string;
  reminderType: "ready" | "reminder" | "final";
  scheduledFor: string;
  sent: boolean;
}

// ---------------- Config ----------------
const REMINDER_OFFSETS = {
  payment: { due: -1, overdue: +1, final: +7 }, // days relative to dueDate
  pickup: { ready: 0, reminder: +3, final: +7 }, // days relative to readyDate
};

// ---------------- Service ----------------
export class NotificationService {
    static async clearAllNotificationData(): Promise<void> {
    try {
      await this.clearAll();
      return Promise.resolve();
    } catch (error) {
      return this.handleError("clear all notification data", error);
    }
  }
  static getNotificationStorageInfo() {
    throw new Error('Method not implemented.');
  }
  // Centralized error handler
  private static handleError(action: string, error: unknown): Promise<never> {
    LoggingService.error(`Failed to ${action}:`, "NOTIFICATION", error as Error);
    return Promise.reject(new AppError(`Failed to ${action}`));
  }

  // ---------- Templates ----------
  static async createTemplate(template: Omit<NotificationTemplate, "id" | "createdAt">) {
    try {
      const templates = (await StorageService.get<NotificationTemplate[]>("notificationTemplates")) || [];
      const newTemplate: NotificationTemplate = {
        ...template,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      templates.push(newTemplate);
      await StorageService.set("notificationTemplates", templates);
      return newTemplate;
    } catch (error) {
      throw this.handleError("create notification template", error);
    }
  }

  static async getTemplates() {
    return (await StorageService.get<NotificationTemplate[]>("notificationTemplates")) || [];
  }

  // ---------- Preferences ----------
  static async getPreferences() {
    return (await StorageService.get<NotificationPreference[]>("notificationPreferences")) || [];
  }

  static async updatePreference(category: NotificationCategory, enabled: boolean) {
    try {
      const prefs = (await this.getPreferences()) || [];
      const idx = prefs.findIndex((p) => p.category === category);
      if (idx > -1) prefs[idx].enabled = enabled;
      else prefs.push({ category, enabled });
      await StorageService.set("notificationPreferences", prefs);
    } catch (error) {
      throw this.handleError("update preferences", error);
    }
  }

  // ---------- Logs ----------
  static async logNotification(log: Omit<NotificationLog, "id" | "timestamp" | "read">) {
    try {
      const logs = (await StorageService.get<NotificationLog[]>("notificationLogs")) || [];
      const newLog: NotificationLog = {
        ...log,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      };
      logs.unshift(newLog); // newest first
      await StorageService.set("notificationLogs", logs);
      return newLog;
    } catch (error) {
      throw this.handleError("log notification", error);
    }
  }

  static async getLogs() {
    return (await StorageService.get<NotificationLog[]>("notificationLogs")) || [];
  }

  static async markAsRead(id: string) {
    try {
      const logs = (await this.getLogs()) || [];
      const idx = logs.findIndex((l) => l.id === id);
      if (idx > -1) logs[idx].read = true;
      await StorageService.set("notificationLogs", logs);
    } catch (error) {
      throw this.handleError("mark notification as read", error);
    }
  }

  static async deleteNotification(id: string) {
    try {
      let logs = (await this.getLogs()) || [];
      logs = logs.filter((l) => l.id !== id);
      await StorageService.set("notificationLogs", logs);
    } catch (error) {
      throw this.handleError("delete notification", error);
    }
  }

  // ---------- Reminders ----------
  static calculateReminderTime(type: "payment" | "pickup", baseDate: Date, reminder: string) {
    const offset =
      type === "payment"
        ? REMINDER_OFFSETS.payment[reminder as keyof typeof REMINDER_OFFSETS.payment]
        : REMINDER_OFFSETS.pickup[reminder as keyof typeof REMINDER_OFFSETS.pickup];
    const scheduled = new Date(baseDate);
    scheduled.setDate(baseDate.getDate() + offset);
    return scheduled;
  }

  static async schedulePickupReminder(orderId: string, readyDate: string, reminderType: "ready" | "reminder" | "final") {
    try {
      const readyDateTime = new Date(readyDate);
      const scheduledFor = this.calculateReminderTime("pickup", readyDateTime, reminderType);
      const reminders = (await StorageService.get<PickupReminder[]>("pickupReminders")) || [];
      const newReminder: PickupReminder = {
        orderId,
        reminderType,
        scheduledFor: scheduledFor.toISOString(),
        sent: false,
      };
      reminders.push(newReminder);
      await StorageService.set("pickupReminders", reminders);
      return newReminder;
    } catch (error) {
      throw this.handleError("schedule pickup reminder", error);
    }
  }

  // ---------- Seeding ----------
  static async seedDummyData(): Promise<void> {
    try {
      const templates: Omit<NotificationTemplate, "id" | "createdAt">[] = [
        { name: "Order Ready", message: "Your order is ready for pickup!", category: "pickup", isActive: true },
        { name: "Payment Reminder", message: "Your payment is due soon.", category: "payment", isActive: true },
      ];
      await Promise.all(templates.map((t) => this.createTemplate(t)));

      const prefs: NotificationPreference[] = [
        { category: "pickup", enabled: true },
        { category: "payment", enabled: true },
        { category: "order_status", enabled: true },
      ];
      await StorageService.set("notificationPreferences", prefs);
      return Promise.resolve();
    } catch (error) {
      throw this.handleError("seed dummy notifications", error);
    }
  }

  // ---------- Clear ----------
  static async clearAll() {
    await Promise.all([
      StorageService.remove("notificationTemplates"),
      StorageService.remove("notificationPreferences"),
      StorageService.remove("notificationLogs"),
      StorageService.remove("pickupReminders"),
    ]);
  }

  // ---------- Dummy Data Generation ----------
  static async generateDummyNotifications() {
    try {
      // Clear existing notifications before generating new ones
      await this.clearAll();
      await this.seedDummyData();
      // Log some dummy notifications
      await this.logNotification({
        category: "general",
        title: "Welcome!",
        message: "Welcome to TailorZa! Explore our features.",
      });
      await this.logNotification({
        category: "order_status",
        title: "Order #TZ1001 Ready",
        message: "Your order #TZ1001 is ready for pickup!",
      });
      await this.logNotification({
        category: "payment",
        title: "Payment Due",
        message: "Your payment for order #TZ1002 is due tomorrow.",
      });
      await this.logNotification({
        category: "reminder",
        title: "Meeting Reminder",
        message: "Don't forget your staff meeting at 3 PM.",
      });
      await this.logNotification({
        category: "general",
        title: "New Feature Alert",
        message: "Check out our new inventory management system!",
      });
      await this.logNotification({
        category: "pickup",
        title: "Pickup Reminder",
        message: "Order #TZ1003 is awaiting pickup.",
      });
      await this.logNotification({
        category: "payment",
        title: "Payment Received",
        message: "Thank you for your payment for order #TZ1004.",
      });
      await this.logNotification({
        category: "order_status",
        title: "Order #TZ1005 Completed",
        message: "Your order #TZ1005 has been successfully completed.",
      });
      const logs = await this.getLogs();
      console.log("Generated Notifications:", logs);
    } catch (error) {
      throw this.handleError("generate dummy notifications", error);
    }
  }

  static async resetToSampleNotifications() {
    try {
      await this.clearAll();
      await this.seedDummyData();
      await this.logNotification({
        category: "general",
        title: "Welcome to TailorZa!",
        message: "This is a sample notification. Explore the app!",
      });
      await this.logNotification({
        category: "order_status",
        title: "Sample Order Ready",
        message: "Your sample order #XYZ789 is ready for pickup.",
      });
      await this.logNotification({
        category: "payment",
        title: "Sample Payment Reminder",
        message: "A sample payment for #ABC123 is due soon.",
      });
    } catch (error) {
      throw this.handleError("reset to sample notifications", error);
    }
  }
}

// ---------------- React Hook ----------------
export function useNotifications() {
  const [notifications, setNotifications] = React.useState<NotificationLog[]>([]);

  const loadNotifications = React.useCallback(async () => {
    const logs = await NotificationService.getLogs();
    setNotifications(logs);
  }, []);

  React.useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  const markAsRead = async (id: string) => {
    await NotificationService.markAsRead(id);
    await loadNotifications();
  };

  const generateDummyNotifications = async () => {
    await NotificationService.generateDummyNotifications();
    await loadNotifications(); // Refresh notifications after generation
  };

  const resetToSampleNotifications = async () => {
    await NotificationService.resetToSampleNotifications();
    await loadNotifications(); // Refresh notifications after reset
  };

  const deleteNotification = async (id: string) => {
    await NotificationService.deleteNotification(id);
    await loadNotifications(); // Refresh notifications after deletion
  };

  return { notifications, refresh: loadNotifications, markAsRead, generateDummyNotifications, resetToSampleNotifications, deleteNotification, unreadCount: notifications.filter(n => !n.read).length };
}
