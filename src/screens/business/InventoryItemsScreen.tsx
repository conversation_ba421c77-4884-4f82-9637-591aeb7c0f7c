/**
 * Inventory Items Screen
 * List view of all inventory items with search and filtering
 */

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl } from 'react-native';
import { Text, FAB } from 'react-native-paper';
import ActionSheet from '../../components/ui/ActionSheet';
import { ActionSheetOption } from '../../types';

import { InventoryCard } from '../../components/inventory';
import Header from '../../components/navigation/Header';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { InventoryItem, InventoryFilters } from '../../types/inventory';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface InventoryItemsScreenProps {
  navigation: any;
  route?: {
    params?: {
      filter?: 'active' | 'low-stock' | 'all';
      category?: string;
    };
  };
}

const InventoryItemsScreen: React.FC<InventoryItemsScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const { products: rawProducts } = dataState; // rawProducts might still be needed for some logic
  const items = dataState.products;
  const isLoading = dataState.loading;
  const error = dataState.error; // Use inventoryState for items, loading, error
  const loadItems = actions.reloadData;
  const getStockByItem = actions.getStockByItem;

  const initialFilter = route?.params?.filter || 'all';
  const initialCategory = route?.params?.category;

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [itemsWithStock, setItemsWithStock] = useState<Map<string, number>>(new Map());

  const [searchQuery, setSearchQuery] = useState('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Load items on mount
  useEffect(() => {
    if (!isLoading && !error && items.length === 0) { // Only load if not already loading, no error, and no items
      loadItems();
    }
  }, [isLoading, error, items.length]); // Removed loadItems from dependencies

  // Load stock data for items
  useEffect(() => {
    const loadStockData = async () => {
      const stockMap = new Map<string, number>();

      for (const item of items) { // Changed from state.items to items
        try {
          const stockData = await getStockByItem(item.id);
          const totalStock = stockData.reduce((sum, stock: any) => sum + stock.quantity, 0);
          stockMap.set(item.id, totalStock);
        } catch (error) {
          LoggingService.warn(
            `Failed to load stock for item ${item.id}`,
            'INVENTORY_ITEMS',
            error as Error
          );
          stockMap.set(item.id, 0);
        }
      }

      // Compare newStockMap with current itemsWithStock to prevent unnecessary updates
      let mapsAreEqual = true;
      if (stockMap.size !== itemsWithStock.size) {
        mapsAreEqual = false;
      } else {
        for (let [key, value] of stockMap) {
          if (itemsWithStock.get(key) !== value) {
            mapsAreEqual = false;
            break;
          }
        }
      }

      if (!mapsAreEqual) {
        setItemsWithStock(stockMap);
      }
    };

    if (items.length > 0) { // Changed from state.items to items
      loadStockData();
    }
  }, [items]); // Removed getStockByItem from dependencies

  // Apply initial filters
  const initialFilteredItems = useMemo(() => {
    let filtered = items;

    // Apply route-based filters
    switch (initialFilter) {
      case 'active':
        filtered = filtered.filter(item => item.isActive);
        break;
      case 'low-stock':
        filtered = filtered.filter(item => {
          const stock = itemsWithStock.get(item.id) || 0;
          return stock <= (item.minimumStockLevel || 0) && (item.minimumStockLevel || 0) > 0;
        });
        break;
      case 'all':
      default:
        // No additional filtering
        break;
    }

    // Apply category filter if provided
    if (initialCategory) {
      filtered = filtered.filter(item => item.category === initialCategory);
    }

    return filtered;
  }, [items, initialFilter, initialCategory, itemsWithStock]);

  const filteredItems = useMemo(() => {
    let currentFiltered = initialFilteredItems;

    if (searchQuery) {
      currentFiltered = currentFiltered.filter(
        item =>
          item.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    return currentFiltered;
  }, [initialFilteredItems, searchQuery]);

  

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadItems();
      LoggingService.info('Inventory items refreshed', 'INVENTORY_ITEMS');
    } catch (error) {
      LoggingService.warn('Failed to refresh inventory items', 'INVENTORY_ITEMS', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [loadItems]);

  // Handle item press
  const handleItemPress = useCallback((item: InventoryItem) => {
    navigation.navigate('InventoryItemDetail', { itemId: item.id });
  }, [navigation]);

  // Handle add item
  const handleAddItem = useCallback(() => {
    navigation.navigate('AddEditInventoryItem');
  }, [navigation]);

  // Handle edit item
  const handleEditItem = useCallback((item: InventoryItem) => {
    navigation.navigate('AddEditInventoryItem', {
      itemId: item.id,
      mode: 'edit',
    });
  }, [navigation]);

  // Handle stock operations
  const handleStockIn = useCallback((item: InventoryItem) => {
    navigation.navigate('StockOperations', {
      operation: 'in',
      itemId: item.id,
    });
  }, [navigation]);

  const handleStockOut = useCallback((item: InventoryItem) => {
    navigation.navigate('StockOperations', {
      operation: 'out',
      itemId: item.id,
    });
  }, [navigation]);

  // Render item
  const renderItem = useCallback(
    ({ item }: { item: InventoryItem }) => {
      const stockLevel = itemsWithStock.get(item.id) || 0;

      return (
        <InventoryCard
          item={item}
          stockLevel={stockLevel}
          onPress={() => handleItemPress(item)}
          showStockLevel={true}
          showActions={true}
          showPrices={true}
          onEditPress={() => handleEditItem(item)}
          onStockPress={() => handleStockIn(item)}
        />
      );
    },
    [itemsWithStock, handleItemPress, handleEditItem, handleStockIn]
  );

  // Get screen title based on filter
  const getScreenTitle = (): string => {
    switch (initialFilter) {
      case 'active':
        return 'Active Items';
      case 'low-stock':
        return 'Low Stock Items';
      case 'all':
      default:
        return 'Inventory Items';
    }
  };

  // Get screen subtitle
  const getScreenSubtitle = (): string => {
    const totalItems = filteredItems.length;
    const activeItems = filteredItems.filter(item => item.isActive).length;

    if (initialFilter === 'low-stock') {
      return `${totalItems} items need attention`;
    }

    return `${totalItems} items (${activeItems} active)`;
  };

  // Empty state component
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <PhosphorIcon name='package' size={64} color={theme.colors.onSurfaceVariant} />
      <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
        {initialFilter === 'low-stock' ? 'No Low Stock Items' : 'No Items Found'}
      </Text>
      <Text style={[styles.emptyMessage, { color: theme.colors.onSurfaceVariant }]}>
        {initialFilter === 'low-stock'
          ? 'All items have adequate stock levels.'
          : 'Try adjusting your search or filters, or add your first inventory item.'}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={getScreenTitle()}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchData={items}
        searchFields={['name', 'category']}
        searchType='products'
        onSearch={query => setSearchQuery(query)}
        actions={[
          {
            icon: 'plus',
            onPress: handleAddItem,
          },
        ]}
        
      />

      <View style={styles.content}>
        {/* Items List */}
        <FlatList
          data={filteredItems}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.listContent,
            filteredItems.length === 0 && styles.emptyListContent,
          ]}
          ListEmptyComponent={renderEmptyState}
          ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
        />
      </View>

      {/* Floating Action Button */}
      <FAB
        icon='plus'
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddItem}
        label='Add Item'
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  listContent: {
    paddingBottom: 100, // Space for FAB
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 32,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default InventoryItemsScreen;
