import { useNavigation } from '@react-navigation/native';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, BackHandler, ViewStyle, Text } from 'react-native';
import { Badge } from 'react-native-paper';
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import Search, { SearchRef } from '../forms/Search';

// --- Type Definitions ---
interface HeaderAction {
  icon?: PhosphorIconName;
  text?: string;
  onPress: () => void;
  disabled?: boolean;
  color?: string;
}

interface HeaderProps {
  title?: string;
  onBackPress?: () => void;
  showBack?: boolean;
  actions?: HeaderAction[];
  showSearch?: boolean;
  searchData?: any[];
  searchFields?: string[];
  searchPlaceholder?: string;
  searchType?: string;
  onSearch?: (query: string) => void;
  showNotifications?: boolean;
  notificationCount?: number;
  onNotificationPress?: () => void;
  backgroundColor?: string;
  style?: ViewStyle;
  rightComponent?: React.ReactElement;
}

const HEADER_HEIGHT = 56;

const Header: React.FC<HeaderProps> = ({
  title = 'TailorZap',
  onBackPress,
  showBack = false,
  actions = [],
  showSearch = false,
  searchData = [],
  searchFields = ['name'],
  searchPlaceholder,
  onSearch,
  showNotifications = false,
  notificationCount = 0,
  onNotificationPress,
  backgroundColor,
  style,
  rightComponent,
}) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const searchInputRef = useRef<SearchRef>(null);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const searchProgress = useSharedValue(0);

  const barBackgroundColor = backgroundColor || theme.colors.surface;

  // ✅ FIX: Moved this function definition before the useEffect that uses it.
  const toggleSearch = useCallback(() => {
    const nextState = !isSearchVisible;
    setIsSearchVisible(nextState);
    searchProgress.value = withTiming(nextState ? 1 : 0, { duration: 250 });

    if (nextState) {
      setTimeout(() => searchInputRef.current?.focus(), 200);
    } else {
      searchInputRef.current?.blur();
    }
  }, [isSearchVisible, searchProgress]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (isSearchVisible) {
        // Now this call is valid.
        toggleSearch();
        return true;
      }
      return false;
    });
    return () => backHandler.remove();
  }, [isSearchVisible, toggleSearch]);

  const handleBackPress = useCallback(() => {
    onBackPress ? onBackPress() : navigation.goBack();
  }, [onBackPress, navigation]);

  const headerContentStyle = useAnimatedStyle(() => ({
    opacity: interpolate(searchProgress.value, [0, 1], [1, 0]),
    transform: [{ translateY: interpolate(searchProgress.value, [0, 1], [0, -10]) }],
  }));

  const searchContentStyle = useAnimatedStyle(() => ({
    opacity: searchProgress.value,
    transform: [{ translateY: interpolate(searchProgress.value, [0, 1], [10, 0]) }],
    zIndex: isSearchVisible ? 1 : -1,
  }));

  return (
    <View style={[ styles.container, { backgroundColor: barBackgroundColor, paddingTop: insets.top }, style ]}>
      <View style={styles.content}>
        <Animated.View style={[styles.headerContentContainer, headerContentStyle]}>
          <View style={styles.leftSection}>
            {showBack && (
              <TouchableOpacity onPress={handleBackPress} style={styles.button}>
                <PhosphorIcon name='arrow-left' size={24} color={theme.colors.onSurface} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.centerSection}>
            <Text style={[styles.title, { color: theme.colors.onSurface }]} numberOfLines={1}>
                {title}
            </Text>
          </View>

          <View style={styles.rightSection}>
            {rightComponent ? rightComponent :
              <>
                {showSearch && (
                  <TouchableOpacity onPress={toggleSearch} style={styles.button}>
                    <PhosphorIcon name='magnifying-glass' size={24} color={theme.colors.onSurface} />
                  </TouchableOpacity>
                )}
                {showNotifications && (
                  <TouchableOpacity onPress={onNotificationPress} style={styles.notificationButton}>
                    <PhosphorIcon name='bell' size={24} color={theme.colors.onSurface} />
                    {notificationCount > 0 && (
                      <Badge size={16} style={[styles.badge, { backgroundColor: theme.colors.error }]}>
                        {notificationCount > 99 ? '99+' : notificationCount}
                      </Badge>
                    )}
                  </TouchableOpacity>
                )}
                {actions.map((action, index) => (
                  <TouchableOpacity key={index} onPress={action.onPress} disabled={action.disabled} style={styles.button}>
                    {action.icon ? (
                      <PhosphorIcon name={action.icon} size={24} color={action.color || theme.colors.onSurface} />
                    ) : action.text ? (
                      <Text style={{ color: action.color || theme.colors.onSurface }}>{action.text}</Text>
                    ) : null}
                  </TouchableOpacity>
                ))}
              </>
            }
          </View>
        </Animated.View>

        <Animated.View style={[styles.searchContentContainer, searchContentStyle]}>
            <TouchableOpacity onPress={toggleSearch} style={styles.button}>
                <PhosphorIcon name='arrow-left' size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>
            <Search
                ref={searchInputRef}
                placeholder={searchPlaceholder || 'Search...'}
                onSearch={onSearch}
                style={styles.searchBar}
            />
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  content: {
    height: HEADER_HEIGHT,
    paddingHorizontal: SPACING.sm,
  },
  headerContentContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchContentContainer: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
  },
  leftSection: {
    justifyContent: 'flex-start',
  },
  centerSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  rightSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: TYPOGRAPHY.fontWeight.bold,
    marginLeft: SPACING.md,
  },
  button: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.round,
  },
  searchBar: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  notificationButton: {
    position: 'relative',
    padding: SPACING.sm,
  },
  badge: {
    position: 'absolute',
    top: 6,
    right: 6,
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
  },
});

export default Header;