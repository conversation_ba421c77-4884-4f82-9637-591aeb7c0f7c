import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Surface, Text } from 'react-native-paper';

import { useTheme } from '@/context/ThemeContext';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon, PhosphorIconName } from '@/utils/phosphorIconRegistry';

// --- Type Definitions ---
type Theme = ReturnType<typeof useTheme>;

// Base props shared by all card types
interface BaseInfoCardProps {
  onPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  elevation?: 1 | 2 | 3 | 4 | 5;
}

// Props for specific card types
interface StatCardProps extends BaseInfoCardProps {
  type: 'stat';
  title: string;
  value: string | number;
  icon: PhosphorIconName;
  iconColor?: string;
}

interface MetricCardProps extends BaseInfoCardProps {
  type: 'metric';
  title: string;
  value: string | number;
  subtitle?: string;
  growth?: number; // e.g., 15.2 for +15.2%
}

interface OrderCardProps extends BaseInfoCardProps {
  type: 'order';
  orderId: string | number;
  customerName: string;
  date: string | Date;
  total: number;
  itemCount: number;
  status: string;
  statusColor?: string;
}

// A discriminated union for the main component's props
type InfoCardProps = StatCardProps | MetricCardProps | OrderCardProps;

// --- Specialized Content Components ---

const StatContent: React.FC<StatCardProps> = ({ title, value, icon, iconColor }) => {
  const theme = useTheme();
  return (
    <View style={styles.contentRow}>
      <View style={styles.textContainer}>
        <Text variant="bodySmall" style={{ color: theme.colors.onSurface }}>{title}</Text>
        <Text variant="titleMedium" style={{ color: theme.colors.onSurfaceVariant }}>{value}</Text>
      </View>
      <PhosphorIcon name={icon} size={20} color={iconColor || theme.colors.onSurfaceVariant} />
    </View>
  );
};

const MetricContent: React.FC<MetricCardProps> = ({ title, value, subtitle, growth }) => {
  const theme = useTheme();
  const isPositive = growth !== undefined && growth >= 0;
  const growthColor = isPositive ? theme.colors.success : theme.colors.error;
  const growthIcon = isPositive ? 'trend-up' : 'trend-down';

  return (
    <View>
      <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, fontWeight: 'bold' }}>{value}</Text>
      <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>{title}</Text>
      {subtitle && <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>{subtitle}</Text>}
      {growth !== undefined && (
        <View style={styles.growthContainer}>
          <PhosphorIcon name={growthIcon} size={14} color={growthColor} />
          <Text variant="bodySmall" style={{ color: growthColor, marginLeft: 4, fontWeight: 'bold' }}>
            {Math.abs(growth).toFixed(1)}%
          </Text>
        </View>
      )}
    </View>
  );
};

const OrderContent: React.FC<OrderCardProps> = ({ orderId, customerName, date, total, itemCount, status, statusColor }) => {
  const theme = useTheme();
  const finalStatusColor = statusColor || theme.colors.primary;
  return (
    <View>
      <View style={[styles.statusBar, { backgroundColor: finalStatusColor }]} />
      <View style={styles.orderMainContent}>
        <View style={styles.textContainer}>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: 'bold' }}>Order #{orderId}</Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>{customerName}</Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
            {new Date(date).toLocaleDateString()}
          </Text>
        </View>
        <View style={styles.orderRight}>
          <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: 'bold' }}>
            {formatCurrency(total)}
          </Text>
          <View style={[styles.statusChip, { backgroundColor: `${finalStatusColor}20` }]}>
            <Text style={[styles.statusChipText, { color: finalStatusColor }]}>{status}</Text>
          </View>
        </View>
      </View>
      <View style={[styles.orderItems, { borderTopColor: theme.colors.outlineVariant }]}>
        <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
          {itemCount} item{itemCount !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );
};

// --- Base Wrapper Component ---

const BaseCard: React.FC<{
  type: InfoCardProps['type'];
  onPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  elevation?: 1 | 2 | 3 | 4 | 5;
  children: React.ReactNode;
}> = ({ type, onPress, disabled, style, elevation, children }) => {
  const theme = useTheme();
  const CardComponent = onPress && !disabled ? TouchableOpacity : View;

  const cardStyle = useMemo(() => [
    styles.cardBase,
    {
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.outlineVariant,
    },
    style
  ], [theme, style]);

  return (
    <CardComponent style={style} onPress={onPress} disabled={disabled} activeOpacity={0.8}>
      <Surface style={cardStyle} elevation={elevation}>
        <View style={styles.cardInner}>{children}</View>
      </Surface>
    </CardComponent>
  );
};

// --- Main Dispatcher Component (This is the short part you noticed) ---

const InfoCard: React.FC<InfoCardProps> = (props) => {
  const renderContent = () => {
    switch (props.type) {
      case 'stat':
        return <StatContent {...props} />;
      case 'metric':
        return <MetricContent {...props} />;
      case 'order':
        return <OrderContent {...props} />;
      default:
        // This ensures that if a new type is added to InfoCardProps but not here, TypeScript will error.
        const exhaustiveCheck: never = props;
        return null;
    }
  };

  return (
    <BaseCard
      type={props.type}
      onPress={props.onPress}
      disabled={props.disabled}
      style={props.style}
      elevation={props.elevation}
    >
      {renderContent()}
    </BaseCard>
  );
};

const styles = StyleSheet.create({
  cardBase: { borderRadius: 12, borderWidth: 0 },
  cardInner: { padding: 12 },
  contentRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  textContainer: { flex: 1, marginRight: 8 },
  growthContainer: { flexDirection: 'row', alignItems: 'center', marginTop: 8 },
  // Order specific styles
  statusBar: { position: 'absolute', top: 0, left: 0, right: 0, height: 4 },
  orderMainContent: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', paddingTop: 8 },
  orderRight: { alignItems: 'flex-end' },
  statusChip: { paddingHorizontal: 10, paddingVertical: 4, borderRadius: 12, marginTop: 6 },
  statusChipText: { fontWeight: 'bold', fontSize: 11, textTransform: 'uppercase' },
  orderItems: { paddingTop: 12, marginTop: 12, borderTopWidth: 1 },
});

export default InfoCard;