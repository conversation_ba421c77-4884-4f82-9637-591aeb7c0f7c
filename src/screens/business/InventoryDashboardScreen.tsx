import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';
import { InventoryCard, TransactionHistoryList } from '@/components/inventory';
import Header from '@/components/navigation/Header';
import ChipGroup from '@/components/ui/ChipGroup';
import StatCardGroup from '@/components/ui/StatCardGroup';
import ActionSheet from '@/components/ui/ActionSheet';
import { ActionSheetOption } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useData } from '../../context/DataContext';
// Inventory functionality now handled by DataContext
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { useNotifications } from '@/services/notificationService';
import { SPACING } from '@/theme/theme';
import { StatCard } from '@/types';
import { InventoryItem } from '@/types/inventory';
import { Product } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

interface InventoryDashboardScreenProps {
  navigation: any;
  navigateToTab?: (tabName: string) => void;
}

const InventoryDashboardScreen: React.FC<InventoryDashboardScreenProps> = ({
  navigation,
  navigateToTab,
}) => {
  const theme = useTheme();
  const { unreadCount } = useNotifications();
  const { state: dataState } = useData();
  const { showSuccess, showError } = useToast();
  // Inventory functionality now handled by DataContext
  const { actions } = useData();
  
  // Create inventory-like interface using DataContext
  const inventoryState = {
    items: dataState.products || [],
    warehouses: [],
    lowStockItems: [],
    recentTransactions: [],
    isLoading: dataState.loading,
    error: dataState.error,
  };

  // Map inventory functions from DataContext
  const { state: authState } = useAuth();

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchQuery, setSearchQueryLocal] = useState<string>('');
  const [activeTab, setActiveTab] = useState<
    'all-products' | 'warehouse' | 'low-stock' | 'recent-activities'
  >('all-products');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Load initial data
  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        await actions.loadDashboardData();
      } catch (error) {
        LoggingService.error(
          'Failed to initialize inventory dashboard',
          'INVENTORY_DASHBOARD',
          error as Error
        );
      }
    };

    initializeDashboard();
  }, []); // Empty dependency array to run only once

  // Handle refresh
  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Load dashboard data functionality would go here
      LoggingService.info('Inventory dashboard refreshed', 'INVENTORY_DASHBOARD');
      showSuccess('Inventory dashboard refreshed successfully');
    } catch (error) {
      LoggingService.warn(
        'Failed to refresh inventory dashboard',
        'INVENTORY_DASHBOARD',
        error as Error
      );
      showError('Failed to refresh inventory dashboard');
    } finally {
      setRefreshing(false);
    }
  }, [showSuccess, showError]);

  // Handle search
  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQueryLocal(query);
      actions.setSearchQuery(query);

      if (query.trim()) {
        actions.setActiveFilters({ search: query.trim() });
      } else {
        actions.clearFilters();
      }
    },
    [setSearchQueryLocal]
  );

  // Handle search result selection
  const handleSearchResult = useCallback((item: any) => {
    LoggingService.debug('Search result selected', 'INVENTORY_DASHBOARD', item);

    // Navigate based on item type
    if (item.category && item.baseUnit) {
      // It's an inventory item
      navigation.navigate('InventoryItemDetail', { itemId: item.id });
    } else if (item.location) {
      // It's a warehouse
      navigation.navigate('WarehouseManagement');
    }
  }, []);

  // Calculate dashboard statistics
  const dashboardStats = useMemo(() => {
    const items = inventoryState.items;
    const warehouses = inventoryState.warehouses;
    const transactions = inventoryState.recentTransactions;

    const totalItems = items.length;
    const activeItems = items.filter((item: any) => item.isActive || item.is_active).length;
    const lowStockItems = inventoryState.lowStockItems.length;
    const totalWarehouses = warehouses.length;
    const recentTransactions = transactions.length;

    // Calculate total inventory value (simplified)
    const totalValue = items.reduce((sum, item) => {
      return sum + ((item as any).purchasePrice || (item as any).cost_per_unit || 0) * 10; // Assuming average stock of 10 units
    }, 0);

    return {
      totalItems,
      activeItems,
      lowStockItems,
      totalWarehouses,
      recentTransactions,
      totalValue,
    };
  }, [dataState.products]);

  // Dashboard cards configuration
  const dashboardCards = useMemo(
    (): StatCard[] => [
      {
        key: 'total-items',
        title: 'Total Items',
        value: dashboardStats.totalItems.toString(),
        icon: 'package',
        iconColor: theme.colors.primary,
        onPress: () => {
          LoggingService.info('Navigate to all items', 'INVENTORY_DASHBOARD');
          navigation.navigate('InventoryItems');
        },
      },
      {
        key: 'low-stock',
        title: 'Low Stock Alert',
        value: dashboardStats.lowStockItems.toString(),
        icon: 'warning',
        iconColor: dashboardStats.lowStockItems > 0 ? theme.colors.error : theme.colors.primary,
        onPress: () => {
          LoggingService.info('Navigate to low stock items', 'INVENTORY_DASHBOARD');
          navigation.navigate('InventoryItems', { filter: 'low-stock' });
        },
      },
      {
        key: 'inventory-value',
        title: 'Inventory Value',
        value: formatCurrency(dashboardStats.totalValue, { decimals: 0 }),
        icon: 'chart-line',
        iconColor: theme.colors.tertiary,
        onPress: () => {
          LoggingService.info('Navigate to inventory reports', 'INVENTORY_DASHBOARD');
          navigation.navigate('Reports');
        },
      },
      {
        key: 'recent-activity',
        title: 'Recent Activity',
        value: dashboardStats.recentTransactions.toString(),
        icon: 'clock',
        iconColor: theme.colors.tertiary,
        onPress: () => {
          LoggingService.info('Navigate to transaction history', 'INVENTORY_DASHBOARD');
          // Add a small delay to ensure navigation ref is ready
          setTimeout(() => {
            navigation.navigate('TransactionHistory');
          }, 100);
        },
      },
    ],
    [dashboardStats, theme.colors]
  );

  // Navigation handlers
  const handleAddItem = useCallback(() => {
    LoggingService.info('Navigate to add inventory item', 'INVENTORY_DASHBOARD');
    navigation.navigate('AddEditInventoryItem');
  }, [navigation]);

  const handleStockOperation = useCallback(() => {
    LoggingService.info('Navigate to stock operations', 'INVENTORY_DASHBOARD');
    navigation.navigate('StockOperations');
  }, [navigation]);

  const handleStockTransfer = useCallback(() => {
    LoggingService.info('Navigate to stock transfer', 'INVENTORY_DASHBOARD');
    navigation.navigate('StockTransfer');
  }, [navigation]);

  const handleWarehouseManagement = useCallback(() => {
    LoggingService.info('Navigate to warehouse management', 'INVENTORY_DASHBOARD');
    navigation.navigate('WarehouseManagement');
  }, [navigation]);

  // Prepare search data
  const searchData = useMemo(() => {
    const items = inventoryState.items;

    return [
      ...items.map(item => ({
        ...item,
        type: 'item',
      })),
    ];
  }, [inventoryState.items]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
            <Header
        title='Inventory'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchData={inventoryState.items}
        searchFields={['name', 'category', 'description']}
        searchType='products'
        onSearch={query => {
          // Navigate to inventory items with search
          navigation.navigate('InventoryItems', { searchQuery: query });
        }}
        actions={[
          {
            icon: 'plus',
            onPress: handleAddItem,
          },
        ]}
        
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Dashboard Statistics */}
          <View style={styles.statsSection}>
            <StatCardGroup title='' cards={dashboardCards} columns={2} showTitle={false} />
          </View>

          {/* Quick Actions Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                  Quick Actions
                </Text>
              </View>
            </View>

            <View style={styles.quickActionsGrid}>
              <TouchableOpacity style={styles.quickActionItem} onPress={handleAddItem}>
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary }]}>
                  <PhosphorIcon name='plus' size={20} color={theme.colors.onPrimary} />
                </View>
                <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                  Add Item
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.quickActionItem} onPress={handleStockOperation}>
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.secondary }]}>
                  <PhosphorIcon name='arrow-down' size={20} color={theme.colors.onSecondary} />
                </View>
                <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                  Stock In/Out
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.quickActionItem} onPress={handleStockTransfer}>
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.tertiary }]}>
                  <PhosphorIcon
                    name='arrows-left-right'
                    size={20}
                    color={theme.colors.onTertiary}
                  />
                </View>
                <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                  Transfer
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.quickActionItem} onPress={handleWarehouseManagement}>
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary }]}>
                  <PhosphorIcon name='warehouse' size={20} color={theme.colors.onPrimary} />
                </View>
                <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                  Warehouses
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Tabbed View Section */}
          <View style={styles.sectionContainer}>
            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
              <ChipGroup
                filters={[
                  { id: 'all-products', label: 'All Products' },
                  { id: 'warehouse', label: 'Warehouses' },
                  { id: 'low-stock', label: 'Low Stock' },
                  { id: 'recent-activities', label: 'Recent Activities' },
                ]}
                selectedFilter={activeTab}
                onFilterChange={filter =>
                  setActiveTab(
                    filter as 'all-products' | 'warehouse' | 'low-stock' | 'recent-activities'
                  )
                }
                style={styles.tabContainer}
              />
            </View>

            {/* Tab Content */}
            <View style={[styles.tabContent, { backgroundColor: theme.colors.surface }]}>
              {activeTab === 'all-products' && (
                <View style={styles.tabPanel}>
                  {inventoryState.items.length > 0 ? (
                    <View style={styles.itemList}>
                      {inventoryState.items
                        .slice(0, 5)
                        .map((item: Product) => (
                          <InventoryCard
                            key={item.id}
                            item={item as any}
                            stockLevel={0}
                            onPress={() =>
                              navigation.navigate('InventoryItemDetail', { itemId: item.id })
                            }
                            showStockLevel={true}
                            showActions={false}
                            showPrices={false}
                            compact={true}
                          />
                        ))}
                      {inventoryState.items.length >
                        5 && (
                        <TouchableOpacity
                          style={[
                            styles.viewAllButton,
                            { backgroundColor: theme.colors.surfaceVariant },
                          ]}
                          onPress={() => navigation.navigate('InventoryItems')}
                        >
                          <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                            View All Products
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ) : (
                    <View style={styles.emptyTabState}>
                      <PhosphorIcon
                        name='package'
                        size={48}
                        color={theme.colors.onSurfaceVariant}
                      />
                      <Text style={[styles.emptyTabText, { color: theme.colors.onSurfaceVariant }]}>
                        No products found
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {activeTab === 'warehouse' && (
                <View style={styles.tabPanel}>
                  {inventoryState.warehouses.length > 0 ? (
                    <View style={styles.itemList}>
                      {inventoryState.warehouses
                        .slice(0, 5)
                        .map((warehouse: InventoryItem) => (
                          <InventoryCard
                            key={warehouse.id}
                            item={warehouse}
                            stockLevel={0}
                            onPress={() =>
                              navigation.navigate('WarehouseManagement', {
                                warehouseId: warehouse.id,
                              })
                            }
                            showStockLevel={false}
                            showActions={false}
                            showPrices={false}
                            compact={true}
                          />
                        ))}
                      {inventoryState.warehouses
                        .length > 5 && (
                        <TouchableOpacity
                          style={[
                            styles.viewAllButton,
                            { backgroundColor: theme.colors.surfaceVariant },
                          ]}
                          onPress={() => navigation.navigate('WarehouseManagement')}
                        >
                          <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                            View All Warehouses
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ) : (
                    <View style={styles.emptyTabState}>
                      <PhosphorIcon
                        name='warehouse'
                        size={48}
                        color={theme.colors.onSurfaceVariant}
                      />
                      <Text style={[styles.emptyTabText, { color: theme.colors.onSurfaceVariant }]}>
                        No warehouses found
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {activeTab === 'low-stock' && (
                <View style={styles.tabPanel}>
                  {inventoryState.lowStockItems.length > 0 ? (
                    <View style={styles.itemList}>
                      {inventoryState.lowStockItems.slice(0, 5).map((item: InventoryItem) => (
                        <InventoryCard
                          key={item.id}
                          item={item}
                          stockLevel={0}
                          onPress={() =>
                            navigation.navigate('InventoryItemDetail', { itemId: item.id })
                          }
                          showStockLevel={true}
                          showActions={false}
                          showPrices={false}
                          compact={true}
                        />
                      ))}
                      {inventoryState.lowStockItems.length > 5 && (
                        <TouchableOpacity
                          style={[
                            styles.viewAllButton,
                            { backgroundColor: theme.colors.surfaceVariant },
                          ]}
                          onPress={() =>
                            navigation.navigate('InventoryItems', { filter: 'low-stock' })
                          }
                        >
                          <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                            View All Low Stock Items
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ) : (
                    <View style={styles.emptyTabState}>
                      <PhosphorIcon
                        name='check-circle'
                        size={48}
                        color={theme.colors.onSurfaceVariant}
                      />
                      <Text style={[styles.emptyTabText, { color: theme.colors.onSurfaceVariant }]}>
                        No low stock items
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {activeTab === 'recent-activities' && (
                <View style={styles.tabPanel}>
                  {inventoryState.recentTransactions.length > 0 ? (
                    <View style={styles.itemList}>
                      <TransactionHistoryList
                        transactions={inventoryState.recentTransactions}
                        maxItems={5}
                        compact={true}
                        showItemName={true}
                        showWarehouseName={true}
                        showPerformedBy={false}
                        onTransactionPress={transaction => {
                          LoggingService.info(
                            'Transaction selected',
                            'INVENTORY_DASHBOARD',
                            transaction
                          );
                          navigation.navigate('TransactionDetail', {
                            transactionId: transaction.id,
                          });
                        }}
                      />
                      {inventoryState.recentTransactions.length > 5 && (
                        <TouchableOpacity
                          style={[
                            styles.viewAllButton,
                            { backgroundColor: theme.colors.surfaceVariant },
                          ]}
                          onPress={() => {
                            setTimeout(() => {
                              navigation.navigate('TransactionHistory');
                            }, 100);
                          }}
                        >
                          <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                            View All Transactions
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ) : (
                    <View style={styles.emptyTabState}>
                      <PhosphorIcon name='clock' size={48} color={theme.colors.onSurfaceVariant} />
                      <Text style={[styles.emptyTabText, { color: theme.colors.onSurfaceVariant }]}>
                        No recent activities
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
    gap: 0,
  },
  sectionCard: {
    marginVertical: 8,
  },
  sectionContainer: {
    marginVertical: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionAction: {
    fontSize: 14,
    fontWeight: '500',
  },
  lowStockList: {
    gap: 8,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickActionItem: {
    width: '23%',
    aspectRatio: 1,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  emptyStateCard: {
    marginVertical: 32,
  },
  emptyStateContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  emptyStateButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsSection: {
    marginBottom: 8,
  },
  tabContainer: {
    marginBottom: 16,
  },
  tabContent: {
    borderRadius: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabPanel: {
    // No specific styles for tab panels, they will be managed by flexbox
  },
  itemList: {
    gap: 8,
  },
  viewAllButton: {
    alignSelf: 'center',
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyTabState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTabText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default InventoryDashboardScreen;