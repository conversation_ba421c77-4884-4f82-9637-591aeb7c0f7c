import React, { ReactNode, createContext, useContext, useEffect, useReducer } from 'react';

import AuthService, { LoginCredentials, UserSession } from '../services/AuthService';
import LoggingService from '../services/LoggingService';

/**
 * AuthContext - Authentication state management
 *
 * Provides:
 * - Authentication state management
 * - Login/logout functionality
 * - Session validation
 * - User information access
 * - Authentication status tracking
 *
 * @context AuthContext
 * @version 1.0.0
 */

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserSession | null;
  error: string | null;
  sessionInfo: {
    timeUntilExpiry?: number;
    lastActivity?: number;
  } | null;
}

export interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  forceLogout: (reason?: string) => Promise<void>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
  // Consolidated permission functions
  hasRole: (requiredRole: 'admin' | 'manager' | 'user') => boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  isManagerOrHigher: () => boolean;
  isStaffOrHigher: () => boolean;
}

type AuthAction =
  | { type: 'AUTH_LOADING'; payload: boolean }
  | { type: 'AUTH_SUCCESS'; payload: UserSession }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_SESSION_INFO'; payload: AuthState['sessionInfo'] };

// Role hierarchy for access control
const roleHierarchy = {
  admin: 3,
  manager: 2,
  user: 1,
} as const;

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  error: null,
  sessionInfo: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_LOADING':
      return {
        ...state,
        isLoading: action.payload,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
        sessionInfo: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'UPDATE_SESSION_INFO':
      return {
        ...state,
        sessionInfo: action.payload,
      };

    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  /**
   * Check authentication status on app start
   */
  useEffect(() => {
    checkAuthStatus();
  }, []);

  /**
   * Set up session monitoring
   */
  useEffect(() => {
    if (state.isAuthenticated) {
      const interval = setInterval(() => {
        updateSessionInfo();
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [state.isAuthenticated]);

  /**
   * Check current authentication status
   */
  const checkAuthStatus = async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });

      const session = await AuthService.getCurrentSession();

      if (session) {
        dispatch({ type: 'AUTH_SUCCESS', payload: session });
        await updateSessionInfo();
        LoggingService.info('Authentication status: authenticated', 'AUTH');
      } else {
        dispatch({ type: 'AUTH_LOGOUT' });
        LoggingService.info('Authentication status: not authenticated', 'AUTH');
      }
    } catch (error) {
      LoggingService.error('Failed to check auth status', 'AUTH', error as Error);
      // Don't set error state, just log out to show login screen
      dispatch({ type: 'AUTH_LOGOUT' });
    } finally {
      dispatch({ type: 'AUTH_LOADING', payload: false });
    }
  };

  /**
   * Update session information
   */
  const updateSessionInfo = async (): Promise<void> => {
    try {
      const sessionInfo = await AuthService.getSessionInfo();

      if (sessionInfo.isAuthenticated && sessionInfo.session) {
        dispatch({
          type: 'UPDATE_SESSION_INFO',
          payload: {
            timeUntilExpiry: sessionInfo.timeUntilExpiry,
            lastActivity: sessionInfo.session.lastActivity,
          },
        });
      }
    } catch (error) {
      LoggingService.error('Failed to update session info', 'AUTH', error as Error);
    }
  };

  /**
   * Login user
   */
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      LoggingService.info(`Login attempt for: ${credentials.email}`, 'AUTH');

      const session = await AuthService.login(credentials);

      dispatch({ type: 'AUTH_SUCCESS', payload: session });
      await updateSessionInfo();

      LoggingService.info(`Login successful for: ${credentials.email}`, 'AUTH');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      LoggingService.error('Login failed', 'AUTH', error as Error);
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: 'AUTH_LOADING', payload: false });
    }
  };

  /**
   * Logout user
   */
  const logout = async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });

      const currentUser = state.user?.username || 'unknown';
      LoggingService.info(`Logout initiated for: ${currentUser}`, 'AUTH');

      await AuthService.logout();

      dispatch({ type: 'AUTH_LOGOUT' });

      LoggingService.info(`Logout completed for: ${currentUser}`, 'AUTH');
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH', error as Error);
      // Even if logout fails, clear the local state to ensure UI consistency
      dispatch({ type: 'AUTH_LOGOUT' });
      // Don't throw error to prevent app freeze - logout should always succeed from UI perspective
    } finally {
      dispatch({ type: 'AUTH_LOADING', payload: false });
    }
  };

  /**
   * Force logout (for security purposes)
   */
  const forceLogout = async (reason: string = 'security'): Promise<void> => {
    try {
      const currentUser = state.user?.username || 'unknown';
      LoggingService.warn(`Force logout initiated for: ${currentUser}, reason: ${reason}`, 'AUTH');

      await AuthService.forceLogout(reason);

      dispatch({ type: 'AUTH_LOGOUT' });

      LoggingService.warn(`Force logout completed for: ${currentUser}`, 'AUTH');
    } catch (error) {
      LoggingService.error('Force logout failed', 'AUTH', error as Error);
      // Even if force logout fails, clear the local state
      dispatch({ type: 'AUTH_LOGOUT' });
      throw error;
    }
  };

  /**
   * Refresh current session
   */
  const refreshSession = async (): Promise<void> => {
    try {
      const session = await AuthService.getCurrentSession();

      if (session) {
        dispatch({ type: 'AUTH_SUCCESS', payload: session });
        await updateSessionInfo();
        LoggingService.info('Session refreshed successfully', 'AUTH');
      } else {
        dispatch({ type: 'AUTH_LOGOUT' });
        LoggingService.warn('Session refresh failed - no valid session', 'AUTH');
      }
    } catch (error) {
      LoggingService.error('Session refresh failed', 'AUTH', error as Error);
      dispatch({ type: 'AUTH_ERROR', payload: 'Failed to refresh session' });
    }
  };

  /**
   * Clear authentication error
   */
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Permission checking functions
  const hasRole = (requiredRole: 'admin' | 'manager' | 'user'): boolean => {
    if (!state.user || !state.isAuthenticated) {
      return false;
    }

    const userRoleLevel = roleHierarchy[state.user.role];
    const requiredRoleLevel = roleHierarchy[requiredRole];

    return userRoleLevel >= requiredRoleLevel;
  };

  const hasPermission = (permission: string): boolean => {
    if (!state.user || !state.isAuthenticated) {
      return false;
    }

    return (
      state.user.permissions.includes(permission) ||
      state.user.permissions.includes('*') ||
      state.user.role === 'admin'
    );
  };

  const isAdmin = (): boolean => hasRole('admin');
  const isManagerOrHigher = (): boolean => hasRole('manager');
  const isStaffOrHigher = (): boolean => hasRole('user');

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    forceLogout,
    refreshSession,
    clearError,
    checkAuthStatus,
    hasRole,
    hasPermission,
    isAdmin,
    isManagerOrHigher,
    isStaffOrHigher,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

/**
 * Hook to use authentication context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

/**
 * Hook to get current user
 */
export const useCurrentUser = (): UserSession | null => {
  const { state } = useAuth();
  return state.user;
};

/**
 * Hook to check if user has permission
 */
export const usePermission = (permission: string): boolean => {
  const { state } = useAuth();

  if (!state.user || !state.isAuthenticated) {
    return false;
  }

  return (
    state.user.permissions.includes(permission) ||
    state.user.permissions.includes('*') ||
    state.user.role === 'admin'
  );
};

/**
 * Hook to get session information
 */
export const useSessionInfo = (): AuthState['sessionInfo'] => {
  const { state } = useAuth();
  return state.sessionInfo;
};

export default AuthContext;