import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  memo,
} from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import Button from '../ui/Button';

// --- Types & Constants ---
type Theme = ReturnType<typeof useTheme>;
type ReportPeriod = 'today' | '7days' | '30days' | '90days' | '6months' | '1year' | 'custom';

interface TimePeriodBottomSheetProps {
  selectedPeriod?: ReportPeriod;
  onApply: (data: { period: ReportPeriod; startDate: Date; endDate: Date }) => void;
  onClose?: () => void;
  mode?: 'range' | 'single';
  onDateSelect?: (date: Date) => void;
  selectedDate?: Date;
}

export interface TimePeriodBottomSheetRef {
  open: () => void;
  close: () => void;
}

const { width: screenWidth } = Dimensions.get('window');
const containerPadding = 32;
const dayWidth = (screenWidth - containerPadding) / 7;

const PERIOD_OPTIONS = [ { value: 'today', label: 'Today' }, { value: '7days', label: '7 Days' }, { value: '30days', label: '30 Days' }, { value: '90days', label: '90 Days' }, { value: '6months', label: '6 Months' }, { value: '1year', label: '1 Year' } ] as const;
const WEEK_DAYS = ['S', 'M', 'T', 'W', 'T', 'F', 'S'] as const;

// --- Date Utilities ---
const normalizeDate = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
};

// --- Calendar Sub-components ---
const CalendarDay = memo(({ day, monthDate, getDateState, handleDateSelect, theme, styles, mode }: any) => {
  const currentDate = useMemo(() => new Date(monthDate.getFullYear(), monthDate.getMonth(), day), [monthDate, day]);
  const { isSelected, isToday, isStartDate, isEndDate, isOnlySelectedDate } = getDateState(currentDate);

  const containerStyle = [ styles.dayContainer, mode === 'range' && isSelected && { backgroundColor: `${theme.colors.primary}20` }, mode === 'range' && isSelected && !isStartDate && styles.rangeFill, mode === 'range' && isStartDate && !isEndDate && styles.startRange, mode === 'range' && isEndDate && styles.endRange ];
  const dayStyle = [ styles.calendarDay, (isStartDate || isEndDate || isOnlySelectedDate) && { backgroundColor: theme.colors.primary, borderRadius: 22 } ];
  const textStyle = [ styles.dayText, (isStartDate || isEndDate || isOnlySelectedDate) && { color: theme.colors.onPrimary }];

  return (
    <View style={containerStyle}>
      <TouchableOpacity style={dayStyle} onPress={() => handleDateSelect(currentDate)}>
        <Text style={textStyle}>{day}</Text>
        {isToday && !isSelected && <View style={styles.todayIndicator} />}
      </TouchableOpacity>
    </View>
  );
});

const MonthComponent = memo(({ monthDate, ...rest }: { monthDate: Date } & any) => {
  const { daysInMonth, firstDay, monthName } = useMemo(() => ({
    daysInMonth: new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0).getDate(),
    firstDay: new Date(monthDate.getFullYear(), monthDate.getMonth(), 1).getDay(),
    monthName: monthDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
  }), [monthDate]);

  const calendarDays = useMemo(() => {
    const emptyDays = Array.from({ length: firstDay }, (_, i) => <View key={`empty-${i}`} style={rest.styles.calendarDay} />);
    const monthDays = Array.from({ length: daysInMonth }, (_, i) => <CalendarDay key={i + 1} day={i + 1} monthDate={monthDate} {...rest} />);
    return [...emptyDays, ...monthDays];
  }, [firstDay, daysInMonth, monthDate, rest]);

  return (
    <View style={rest.styles.monthContainer}>
      <Text style={rest.styles.monthTitle}>{monthName}</Text>
      <View style={rest.styles.weekDays}>
        {WEEK_DAYS.map((d, index) => (<Text key={index} style={rest.styles.weekDayText}>{d}</Text>))}
      </View>
      <View style={rest.styles.calendarGrid}>{calendarDays}</View>
    </View>
  );
});

// --- Main Component ---
const TimePeriodBottomSheetComponent = forwardRef<TimePeriodBottomSheetRef, TimePeriodBottomSheetProps>(
  ({ selectedPeriod: initialPeriod = 'today', onApply, onClose, mode = 'range', onDateSelect, selectedDate }, ref) => {
    const theme = useTheme();
    const bottomSheetRef = useRef<BottomSheet>(null);
    const flatListRef = useRef<FlatList<Date>>(null);
    const styles = useMemo(() => createStyles(theme), [theme]);
    const insets = useSafeAreaInsets();

    const [selectedPeriod, setSelectedPeriod] = useState<ReportPeriod>(initialPeriod);
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);

    const snapPoints = useMemo(() => (mode === 'single' ? ['70%'] : ['85%']), [mode]);
    const today = useMemo(() => normalizeDate(new Date()), []);

    const monthsToDisplay = useMemo(() => {
      const months: Date[] = [];
      const currentDate = new Date();
      const count = mode === 'single' ? 12 : 24; // Show more months for single selection
      const startMonthOffset = mode === 'single' ? 0 : -23;
      for (let i = 0; i < count; i++) {
        months.push(new Date(currentDate.getFullYear(), currentDate.getMonth() + startMonthOffset + i, 1));
      }
      return months;
    }, [mode]);

    const getDateState = useCallback((currentDate: Date) => {
      const normalizedCurrent = normalizeDate(currentDate);
      const isToday = normalizedCurrent.getTime() === today.getTime();
      if (mode === 'single') {
        const isSelected = selectedDate ? normalizeDate(selectedDate).getTime() === normalizedCurrent.getTime() : false;
        return { isSelected, isToday, isOnlySelectedDate: isSelected };
      }
      const normalizedStart = startDate ? normalizeDate(startDate) : null;
      const normalizedEnd = endDate ? normalizeDate(endDate) : null;
      const isSelected = !!(normalizedStart && normalizedEnd && normalizedCurrent >= normalizedStart && normalizedCurrent <= normalizedEnd);
      return {
        isSelected, isToday,
        isStartDate: !!(normalizedStart && normalizedCurrent.getTime() === normalizedStart.getTime()),
        isEndDate: !!(normalizedEnd && normalizedCurrent.getTime() === normalizedEnd.getTime()),
        isOnlySelectedDate: !!(normalizedStart && !normalizedEnd && normalizedCurrent.getTime() === normalizedStart.getTime()),
      };
    }, [mode, selectedDate, startDate, endDate, today]);
    
    const scrollToDate = useCallback((date: Date) => {
        if (!flatListRef.current) return;
        const targetMonthIndex = monthsToDisplay.findIndex(
            month => month.getFullYear() === date.getFullYear() && month.getMonth() === date.getMonth()
        );
        if (targetMonthIndex !== -1) {
            // Timeout ensures the list has time to render before scrolling
            setTimeout(() => {
                flatListRef.current?.scrollToIndex({
                    index: targetMonthIndex,
                    animated: true,
                    viewPosition: 0,
                });
            }, 200);
        }
    }, [monthsToDisplay]);

    const handlePeriodSelect = useCallback((period: ReportPeriod) => {
      setSelectedPeriod(period);
      if (period === 'custom') {
        setStartDate(null);
        setEndDate(null);
        return;
      }
      const end = new Date();
      const start = new Date();
      switch (period) {
        case '7days': start.setDate(start.getDate() - 6); break;
        case '30days': start.setDate(start.getDate() - 29); break;
        case '90days': start.setDate(start.getDate() - 89); break;
        case '6months': start.setMonth(start.getMonth() - 6); start.setDate(start.getDate() + 1); break;
        case '1year': start.setFullYear(start.getFullYear() - 1); start.setDate(start.getDate() + 1); break;
      }
      const normalizedStart = normalizeDate(start);
      setStartDate(normalizedStart);
      setEndDate(normalizeDate(end));
      scrollToDate(normalizedStart); // Scroll to the start of the range
    }, [scrollToDate]);

    useEffect(() => {
      if (mode === 'range') handlePeriodSelect(initialPeriod);
    }, [mode, initialPeriod, handlePeriodSelect]);
    
    const handleDateSelect = useCallback((date: Date) => {
      if (mode === 'single') {
        onDateSelect?.(date);
        bottomSheetRef.current?.close();
      } else {
        setSelectedPeriod('custom');
        const normalized = normalizeDate(date);
        if (!startDate || (startDate && endDate)) {
          setStartDate(normalized);
          setEndDate(null);
        } else if (normalized >= startDate) {
          setEndDate(normalized);
        } else {
          setEndDate(startDate);
          setStartDate(normalized);
        }
      }
    }, [mode, onDateSelect, startDate, endDate]);

    const handleApply = useCallback(() => {
      if (startDate && endDate) {
        onApply({ period: selectedPeriod, startDate, endDate });
        bottomSheetRef.current?.close();
      }
    }, [startDate, endDate, selectedPeriod, onApply]);

    useImperativeHandle(ref, () => ({
      open: () => {
        // When opening, ensure the initially selected period is visible
        if (startDate) {
          scrollToDate(startDate);
        }
        bottomSheetRef.current?.expand();
      },
      close: () => bottomSheetRef.current?.close(),
    }), [startDate, scrollToDate]);

    const renderMonth = useCallback(({ item }: { item: Date }) => (
      <MonthComponent monthDate={item} mode={mode} theme={theme} getDateState={getDateState} handleDateSelect={handleDateSelect} styles={styles} />
    ), [mode, theme, getDateState, handleDateSelect, styles]);

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={onClose}
        backdropComponent={(props: BottomSheetBackdropProps) => <BottomSheetBackdrop {...props} pressBehavior="close" />}
        enablePanDownToClose={false}
        enableHandlePanningGesture={false}
        enableContentPanningGesture={false}
        backgroundStyle={{ backgroundColor: theme.colors.background }}
      >
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{mode === 'single' ? 'Select Date' : 'Select Time Period'}</Text>
          <TouchableOpacity style={styles.closeButton} onPress={() => bottomSheetRef.current?.close()}>
            <PhosphorIcon name="x" size={24} color={theme.colors.onSurface} />
          </TouchableOpacity>
        </View>
        {mode === 'range' && (
          <View style={styles.fixedSection}>
            <View style={styles.chipContainer}>
              <FlatList
                data={PERIOD_OPTIONS}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={item => item.value}
                renderItem={({ item }) => (
                  <TouchableOpacity onPress={() => handlePeriodSelect(item.value)} style={[styles.periodChip, selectedPeriod === item.value && styles.periodChipSelected]}>
                    <Text style={[styles.periodChipText, selectedPeriod === item.value && styles.periodChipTextSelected]}>{item.label}</Text>
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        )}
        <FlatList
          ref={flatListRef}
          data={monthsToDisplay}
          keyExtractor={item => item.toISOString()}
          renderItem={renderMonth}
          style={styles.scrollableContent}
          getItemLayout={(_, index) => ({ length: 350, offset: 350 * index, index })}
        />
        <View style={[styles.actionButtonsContainer, { paddingBottom: insets.bottom > 0 ? insets.bottom : 16 }]}>
          <Button variant="ghost" onPress={() => bottomSheetRef.current?.close()} style={styles.button}>Cancel</Button>
          {mode === 'range' && (
            <Button variant="primary" onPress={handleApply} style={styles.button} disabled={!startDate || !endDate}>Apply</Button>
          )}
        </View>
      </BottomSheet>
    );
  }
);

const createStyles = (theme: Theme) => StyleSheet.create({
  // ... (header, fixedSection, chip styles are the same)
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: theme.spacing.lg, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
  headerTitle: { fontSize: theme.typography.fontSize.xl, fontWeight: '600', color: theme.colors.onSurface },
  closeButton: { padding: theme.spacing.xs },
  fixedSection: { paddingVertical: theme.spacing.md, paddingHorizontal: theme.spacing.lg, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
  chipContainer: { height: 40 },
  periodChip: { marginRight: theme.spacing.sm, paddingHorizontal: theme.spacing.md, paddingVertical: theme.spacing.sm, backgroundColor: theme.colors.surfaceVariant, borderRadius: theme.borderRadius.md, justifyContent: 'center' },
  periodChipSelected: { backgroundColor: theme.colors.primary },
  periodChipText: { color: theme.colors.onSurface, fontWeight: '600' },
  periodChipTextSelected: { color: theme.colors.onPrimary, fontWeight: 'bold' },
  scrollableContent: { flex: 1 },
  monthContainer: { paddingHorizontal: 16, marginBottom: theme.spacing.lg },
  monthTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.onSurface, textAlign: 'center', paddingVertical: theme.spacing.md },
  weekDays: { flexDirection: 'row', marginBottom: theme.spacing.sm },
  weekDayText: { width: dayWidth, textAlign: 'center', color: theme.colors.onSurfaceVariant },
  calendarGrid: { flexDirection: 'row', flexWrap: 'wrap' },
  dayContainer: { width: dayWidth, height: 44, justifyContent: 'center', alignItems: 'center' },
  rangeFill: {},
  startRange: { borderTopLeftRadius: 22, borderBottomLeftRadius: 22 },
  endRange: { borderTopRightRadius: 22, borderBottomRightRadius: 22 },
  calendarDay: { width: 44, height: 44, justifyContent: 'center', alignItems: 'center' },
  dayText: { fontSize: 16, color: theme.colors.onSurface },
  todayIndicator: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    backgroundColor: theme.colors.primary,
    position: 'absolute',
    bottom: 5,
  },
  actionButtonsContainer: { flexDirection: 'row', padding: theme.spacing.lg, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, gap: theme.spacing.md, backgroundColor: theme.colors.background },
  button: { flex: 1 },
});

const TimePeriodBottomSheet = memo(TimePeriodBottomSheetComponent);
TimePeriodBottomSheet.displayName = 'TimePeriodBottomSheet';

export default TimePeriodBottomSheet;