import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import React from 'react';

import NavBar from '../components/navigation/NavBar';
import DashboardScreen from '../screens/business/DashboardScreen';
import InventoryDashboardScreen from '../screens/business/InventoryDashboardScreen';
import OrdersScreen from '../screens/business/OrdersScreen';
import ProfileScreen from '../screens/settings/ProfileScreen';
import LoggingService from '../services/LoggingService';

const Tab = createBottomTabNavigator();

interface TabNavigatorProps {
  navigation: any;
  route: any;
}

// Wrapper components to provide required props
const DashboardWrapper: React.FC = () => {
  const navigation = useNavigation();
  return (
    <DashboardScreen
      navigation={navigation}
      navigateToTab={(tabName: string) => {
        // Handle tab navigation
        if (tabName === 'Orders') {
          (navigation as any).navigate('Orders');
        } else if (tabName === 'Inventory') {
          (navigation as any).navigate('Inventory');
        } else if (tabName === 'Profile') {
          (navigation as any).navigate('Profile');
        }
      }}
    />
  );
};

const TabNavigator: React.FC<TabNavigatorProps> = ({ navigation, route }) => {
  const handleQuickAction = (action: string) => {
    LoggingService.debug(`Quick action triggered: ${action}`, 'TAB_NAVIGATOR', {
      hasNavigation: !!navigation,
      navigationType: typeof navigation,
      hasNavigate: navigation && typeof navigation.navigate === 'function',
    });

    switch (action) {
      case 'add-product':
        try {
          if (navigation && typeof navigation.navigate === 'function') {
            navigation.navigate('AddProduct');
            LoggingService.info(
              'Successfully navigated to AddProduct from TabNavigator',
              'TAB_NAVIGATOR'
            );
          } else {
            LoggingService.error('Navigation object not available for AddProduct', 'TAB_NAVIGATOR');
          }
        } catch (error) {
          LoggingService.error('Failed to navigate to AddProduct', 'TAB_NAVIGATOR', error as Error);
        }
        break;
      case 'add-order':
        navigation.navigate('AddOrder');
        break;
      case 'add-customer':
        navigation.navigate('AddCustomer');
        break;
      case 'add-inventory':
        navigation.navigate('AddEditInventoryItem');
        break;
      default:
        LoggingService.warn('Unknown quick action received', 'TAB_NAVIGATOR', { action });
    }
  };

  return (
    <Tab.Navigator
      tabBar={(props: any) => <NavBar {...props} onQuickAction={handleQuickAction} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen
        name='Home'
        component={DashboardWrapper}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name='Orders'
        component={OrdersScreen}
        options={{
          tabBarLabel: 'Orders',
        }}
      />
      <Tab.Screen
        name='Add'
        component={DashboardWrapper} // This will be handled by the add button
        options={{
          tabBarLabel: 'Add',
        }}
        listeners={{
          tabPress: (e: any) => {
            e.preventDefault();
            // This will be handled by the NavBar add button
          },
        }}
      />
      <Tab.Screen
        name='Inventory'
        component={InventoryDashboardScreen}
        options={{
          tabBarLabel: 'Inventory',
        }}
      />
      <Tab.Screen
        name='Profile'
        component={ProfileScreen}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
