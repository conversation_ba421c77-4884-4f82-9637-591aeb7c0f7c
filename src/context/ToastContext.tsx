import React, { createContext, useContext, useState, useCallback, useRef } from 'react';

import Toast from '../components/ui/Toast';

interface ToastProps {
  visible: boolean;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'default';
  position?: 'top' | 'bottom';
  duration?: number;
  onHide: () => void;
}

interface ToastItem {
  id: string;
  message: string;
  type: ToastProps['type'];
  duration: number;
  position: 'top' | 'bottom';
  visible: boolean;
}

interface ToastContextType {
  showToast: (
    message: string,
    type?: ToastProps['type'],
    duration?: number,
    position?: 'top' | 'bottom'
  ) => void;
  showSuccess: (message: string, duration?: number, position?: 'top' | 'bottom') => void;
  showError: (message: string, duration?: number, position?: 'top' | 'bottom') => void;
  showWarning: (message: string, duration?: number, position?: 'top' | 'bottom') => void;
  showInfo: (message: string, duration?: number, position?: 'top' | 'bottom') => void;
  hideToast: () => void;
  hideAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children, maxToasts = 3 }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const toastIdCounter = useRef(0);

  const generateToastId = () => {
    toastIdCounter.current += 1;
    return `toast_${toastIdCounter.current}_${Date.now()}`;
  };

  const addToast = useCallback(
    (toast: Omit<ToastItem, 'id' | 'visible'>) => {
      const newToast: ToastItem = {
        ...toast,
        id: generateToastId(),
        visible: true,
      };

      setToasts(prev => {
        const updated = [...prev, newToast];
        // Keep only the last maxToasts items
        return updated.slice(-maxToasts);
      });
    },
    [maxToasts]
  );

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const showToast = useCallback(
    (
      message: string,
      type: ToastProps['type'] = 'info',
      duration: number = 3000,
      position: 'top' | 'bottom' = 'top'
    ) => {
      addToast({ message, type, duration, position });
    },
    [addToast]
  );

  const showSuccess = useCallback(
    (message: string, duration: number = 3000, position: 'top' | 'bottom' = 'top') => {
      showToast(message, 'success', duration, position);
    },
    [showToast]
  );

  const showError = useCallback(
    (message: string, duration: number = 4000, position: 'top' | 'bottom' = 'top') => {
      showToast(message, 'error', duration, position);
    },
    [showToast]
  );

  const showWarning = useCallback(
    (message: string, duration: number = 3500, position: 'top' | 'bottom' = 'top') => {
      showToast(message, 'warning', duration, position);
    },
    [showToast]
  );

  const showInfo = useCallback(
    (message: string, duration: number = 3000, position: 'top' | 'bottom' = 'top') => {
      showToast(message, 'info', duration, position);
    },
    [showToast]
  );

  const hideToast = useCallback(() => {
    // Hide the first visible toast
    setToasts(prev => {
      const updated = prev.map((toast, index) =>
        index === 0 ? { ...toast, visible: false } : toast
      );
      return updated;
    });
  }, []);

  const hideAllToasts = useCallback(() => {
    setToasts(prev => prev.map(toast => ({ ...toast, visible: false })));
  }, []);

  const handleToastHide = useCallback(
    (id: string) => {
      // Remove the toast after animation completes
      setTimeout(() => {
        removeToast(id);
      }, 300);
    },
    [removeToast]
  );

  const handleToastVisibilityChange = useCallback((id: string, visible: boolean) => {
    setToasts(prev => prev.map(toast => (toast.id === id ? { ...toast, visible } : toast)));
  }, []);

  return (
    <ToastContext.Provider
      value={{
        showToast,
        showSuccess,
        showError,
        showWarning,
        showInfo,
        hideToast,
        hideAllToasts,
      }}
    >
      {children}
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          visible={toast.visible}
          position={toast.position}
          onHide={() => handleToastHide(toast.id)}
        />
      ))}
    </ToastContext.Provider>
  );
};
