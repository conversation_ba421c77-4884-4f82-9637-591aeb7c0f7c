/**
 * Unit Conversion Utility for Inventory Management
 * Handles conversions between different measurement units with meter as base unit
 */

import LoggingService from '../services/LoggingService';

export interface ConversionResult {
  value: number;
  unit: string;
  baseValue: number;
  isValid: boolean;
  error?: string;
}

export class UnitConverter {
  // Conversion rates to meter (base unit)
  private static readonly CONVERSION_RATES: Record<string, number> = {
    meter: 1, // Base unit
    m: 1, // Alias for meter
    cm: 0.01, // 1 cm = 0.01 meter
    centimeter: 0.01,
    inch: 0.0254, // 1 inch = 0.0254 meter
    in: 0.0254, // Alias for inch
    yard: 0.9144, // 1 yard = 0.9144 meter
    yd: 0.9144, // Alias for yard
    feet: 0.3048, // 1 foot = 0.3048 meter
    ft: 0.3048, // Alias for feet
    foot: 0.3048, // Alias for feet
  };

  // Display names for units
  private static readonly UNIT_DISPLAY_NAMES: Record<string, string> = {
    meter: 'Meter',
    m: 'Meter',
    cm: 'Centimeter',
    centimeter: 'Centimeter',
    inch: 'Inch',
    in: 'Inch',
    yard: 'Yard',
    yd: 'Yard',
    feet: 'Feet',
    ft: 'Feet',
    foot: 'Feet',
  };

  // Abbreviations for units
  private static readonly UNIT_ABBREVIATIONS: Record<string, string> = {
    meter: 'm',
    m: 'm',
    cm: 'cm',
    centimeter: 'cm',
    inch: 'in',
    in: 'in',
    yard: 'yd',
    yd: 'yd',
    feet: 'ft',
    ft: 'ft',
    foot: 'ft',
  };

  /**
   * Convert any unit to base unit (meter)
   * @param value - The value to convert
   * @param unit - The source unit
   * @returns The value in meters
   */
  static convertToBase(value: number, unit: string): number {
    try {
      if (typeof value !== 'number' || isNaN(value)) {
        throw new Error('Invalid value: must be a number');
      }

      if (value < 0) {
        throw new Error('Invalid value: cannot be negative');
      }

      if (!unit || typeof unit !== 'string') {
        throw new Error('Invalid unit: unit must be a non-empty string');
      }
      const normalizedUnit = unit.toLowerCase().trim();
      const conversionRate = this.CONVERSION_RATES[normalizedUnit];

      if (conversionRate === undefined) {
        throw new Error(`Unsupported unit: ${unit}`);
      }

      const result = value * conversionRate;

      LoggingService.debug(`Converted ${value} ${unit} to ${result} meters`, 'UNIT_CONVERTER');

      return result;
    } catch (error) {
      LoggingService.error('Failed to convert to base unit', 'UNIT_CONVERTER', error as Error);
      throw error;
    }
  }

  /**
   * Convert from base unit (meter) to target unit
   * @param baseValue - The value in meters
   * @param targetUnit - The target unit
   * @returns The value in target unit
   */
  static convertFromBase(baseValue: number, targetUnit: string): number {
    try {
      if (typeof baseValue !== 'number' || isNaN(baseValue)) {
        throw new Error('Invalid base value: must be a number');
      }

      if (baseValue < 0) {
        throw new Error('Invalid base value: cannot be negative');
      }

      if (!targetUnit || typeof targetUnit !== 'string') {
        throw new Error('Invalid target unit: unit must be a non-empty string');
      }
      const normalizedUnit = targetUnit.toLowerCase().trim();
      const conversionRate = this.CONVERSION_RATES[normalizedUnit];

      if (conversionRate === undefined) {
        throw new Error(`Unsupported unit: ${targetUnit}`);
      }

      const result = baseValue / conversionRate;

      LoggingService.debug(
        `Converted ${baseValue} meters to ${result} ${targetUnit}`,
        'UNIT_CONVERTER'
      );

      return result;
    } catch (error) {
      LoggingService.error('Failed to convert from base unit', 'UNIT_CONVERTER', error as Error);
      throw error;
    }
  }

  /**
   * Convert between any two units
   * @param value - The value to convert
   * @param fromUnit - The source unit
   * @param toUnit - The target unit
   * @returns The converted value
   */
  static convertBetweenUnits(value: number, fromUnit: string, toUnit: string): number {
    try {
      // First convert to base unit (meter)
      const baseValue = this.convertToBase(value, fromUnit);

      // Then convert from base unit to target unit
      const result = this.convertFromBase(baseValue, toUnit);

      LoggingService.debug(
        `Converted ${value} ${fromUnit} to ${result} ${toUnit}`,
        'UNIT_CONVERTER'
      );

      return result;
    } catch (error) {
      LoggingService.error('Failed to convert between units', 'UNIT_CONVERTER', error as Error);
      throw error;
    }
  }

  /**
   * Get all supported units
   * @returns Array of supported unit names
   */
  static getSupportedUnits(): string[] {
    return Object.keys(this.CONVERSION_RATES);
  }

  /**
   * Get primary units (without aliases)
   * @returns Array of primary unit names
   */
  static getPrimaryUnits(): string[] {
    return ['meter', 'cm', 'inch', 'yard', 'feet'];
  }

  /**
   * Check if a unit is supported
   * @param unit - The unit to check
   * @returns True if unit is supported
   */
  static isUnitSupported(unit: string): boolean {
    if (!unit || typeof unit !== 'string') {
      return false;
    }
    const normalizedUnit = unit.toLowerCase().trim();
    return this.CONVERSION_RATES[normalizedUnit] !== undefined;
  }

  /**
   * Get display name for a unit
   * @param unit - The unit
   * @returns Display name for the unit
   */
  static getUnitDisplayName(unit: string): string {
    if (!unit || typeof unit !== 'string') {
      return unit || 'Unknown';
    }
    const normalizedUnit = unit.toLowerCase().trim();
    return this.UNIT_DISPLAY_NAMES[normalizedUnit] || unit;
  }

  /**
   * Get abbreviation for a unit
   * @param unit - The unit
   * @returns Abbreviation for the unit
   */
  static getUnitAbbreviation(unit: string): string {
    if (!unit || typeof unit !== 'string') {
      return unit || 'Unknown';
    }
    const normalizedUnit = unit.toLowerCase().trim();
    return this.UNIT_ABBREVIATIONS[normalizedUnit] || unit;
  }

  /**
   * Format quantity with unit for display
   * @param quantity - The quantity value
   * @param unit - The unit
   * @param precision - Number of decimal places (default: 2)
   * @returns Formatted string
   */
  static formatQuantity(quantity: number, unit: string, precision: number = 2): string {
    try {
      if (typeof quantity !== 'number' || isNaN(quantity)) {
        return `0 ${this.getUnitAbbreviation(unit)}`;
      }

      const formattedQuantity = quantity.toFixed(precision);
      const abbreviation = this.getUnitAbbreviation(unit);

      return `${formattedQuantity} ${abbreviation}`;
    } catch (error) {
      LoggingService.warn('Failed to format quantity', 'UNIT_CONVERTER', error as Error);
      return `${quantity} ${unit}`;
    }
  }

  /**
   * Get conversion with detailed result
   * @param value - The value to convert
   * @param fromUnit - The source unit
   * @param toUnit - The target unit
   * @returns Detailed conversion result
   */
  static getConversionResult(value: number, fromUnit: string, toUnit: string): ConversionResult {
    try {
      const convertedValue = this.convertBetweenUnits(value, fromUnit, toUnit);
      const baseValue = this.convertToBase(value, fromUnit);

      return {
        value: convertedValue,
        unit: toUnit,
        baseValue,
        isValid: true,
      };
    } catch (error) {
      LoggingService.warn('Conversion failed', 'UNIT_CONVERTER', error as Error);

      return {
        value: 0,
        unit: toUnit,
        baseValue: 0,
        isValid: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Get conversion rate between two units
   * @param fromUnit - The source unit
   * @param toUnit - The target unit
   * @returns The conversion rate
   */
  static getConversionRate(fromUnit: string, toUnit: string): number {
    try {
      const fromRate = this.CONVERSION_RATES[fromUnit.toLowerCase().trim()];
      const toRate = this.CONVERSION_RATES[toUnit.toLowerCase().trim()];

      if (fromRate === undefined || toRate === undefined) {
        throw new Error('Unsupported unit in conversion rate calculation');
      }

      return fromRate / toRate;
    } catch (error) {
      LoggingService.error('Failed to get conversion rate', 'UNIT_CONVERTER', error as Error);
      throw error;
    }
  }

  /**
   * Round value to appropriate precision based on unit
   * @param value - The value to round
   * @param unit - The unit (affects precision)
   * @returns Rounded value
   */
  static roundToPrecision(value: number, unit: string): number {
    const normalizedUnit = unit.toLowerCase().trim();

    // Different precision for different units
    switch (normalizedUnit) {
      case 'cm':
      case 'centimeter':
        return Math.round(value * 10) / 10; // 1 decimal place
      case 'inch':
      case 'in':
        return Math.round(value * 100) / 100; // 2 decimal places
      case 'meter':
      case 'm':
        return Math.round(value * 1000) / 1000; // 3 decimal places
      case 'yard':
      case 'yd':
      case 'feet':
      case 'ft':
      case 'foot':
        return Math.round(value * 100) / 100; // 2 decimal places
      default:
        return Math.round(value * 100) / 100; // Default 2 decimal places
    }
  }

  /**
   * Validate unit conversion input
   * @param value - The value to validate
   * @param unit - The unit to validate
   * @returns Validation result
   */
  static validateInput(value: number, unit: string): { isValid: boolean; error?: string } {
    if (typeof value !== 'number' || isNaN(value)) {
      return { isValid: false, error: 'Value must be a valid number' };
    }

    if (value < 0) {
      return { isValid: false, error: 'Value cannot be negative' };
    }

    if (!this.isUnitSupported(unit)) {
      return { isValid: false, error: `Unsupported unit: ${unit}` };
    }

    return { isValid: true };
  }
}

export default UnitConverter;
