import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo, useState, memo, useRef } from 'react';
import { FlatList, ListRenderItem, Pressable, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Checkbox, Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FilterSortBottomSheet from '../../components/bottomsheets/FilterSortBottomSheet';
import { BottomSheetRef } from '../../components/bottomsheets/BottomSheet';
import { DataCard } from '../../components/cards';
import Header from '../../components/navigation/Header';
import { ActionSheet, Button, ChipGroup, EmptyState } from '../../components/ui';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { Customer, Order } from '../../types';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';

interface EnhancedCustomer extends Omit<Customer, 'lastOrderDate'> {
  totalSpent: number;
  totalOrders: number;
  activeOrders: number;
  lastOrderDate: Date | null;
}

const ITEM_HEIGHT = 120; // Approximate height of a DataCard. Measure precisely for optimal performance.

const SelectionHeader: React.FC<{
  count: number;
  areAllSelected: boolean;
  onCancel: () => void;
  onSelectAll: () => void;
  onDelete: () => void;
}> = memo(({ count, areAllSelected, onCancel, onSelectAll, onDelete }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const styles = createSelectionHeaderStyles(theme, insets);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Pressable onPress={onSelectAll} style={styles.selectAllContainer}>
          <TouchableOpacity
            onPress={onSelectAll}
            style={styles.selectionCheckbox}
          >
            <PhosphorIcon
              name={areAllSelected ? 'check-circle' : 'circle'}
              size={24}
              color={theme.colors.primary}
              weight={areAllSelected ? 'fill' : 'regular'}
            />
          </TouchableOpacity>
          <Text style={styles.title}>{count} Selected</Text>
        </Pressable>
        <Button icon="trash" onPress={onDelete} variant="ghost" textColor={theme.colors.error} />
      </View>
    </View>
  );
});

// --------------------------------------------------------------------------------- //
// --- MAIN SCREEN COMPONENT                                                     --- //
// --------------------------------------------------------------------------------- //
const CustomersScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const theme = useTheme();
  const { showSuccess, showError } = useToast();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const { customers: rawCustomers, orders: rawOrders } = state;

  const customerOrdersMap = useMemo(() => {
    const map = new Map<string, Order[]>();
    if (rawOrders) {
      rawOrders.forEach(order => {
        const customerId = String(order.customerId);
        if (!map.has(customerId)) {
          map.set(customerId, []);
        }
        map.get(customerId)?.push(order);
      });
    }
    return map;
  }, [rawOrders]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedCustomers, setSelectedCustomers] = useState<Set<string>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState<{ key: string; direction: 'asc' | 'desc' }>();
  const filterSortBottomSheetRef = useRef<BottomSheetRef>(null);

  const sortOptions = useMemo(() => [
    { key: 'name', label: 'Name' },
    { key: 'totalSpent', label: 'Total Spent' },
    { key: 'totalOrders', label: 'Total Orders' },
    { key: 'lastOrderDate', label: 'Last Order' },
  ], []);

  const filterOptions = useMemo(() => [
    { id: 'All', label: 'All', icon: 'users' as PhosphorIconName },
    { id: 'Recent', label: 'Recent', icon: 'clock' as PhosphorIconName },
    { id: 'Active', label: 'Active', icon: 'check-circle' as PhosphorIconName },
    { id: 'New', label: 'New', icon: 'star' as PhosphorIconName },
    { id: 'VIP', label: 'VIP', icon: 'crown' as PhosphorIconName },
  ], []);

  const enhancedCustomers: EnhancedCustomer[] = useMemo(() => {
    if (!rawCustomers) return [];
    return rawCustomers.map(customer => {
      const customerOrders = customerOrdersMap.get(String(customer.id)) || [];
      const totalSpent = customerOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
      const activeOrders = customerOrders.filter(order => {
        return ['pending', 'confirmed', 'in-progress'].includes(order.status);
      }).length;
      const lastOrderDate = customerOrders.length > 0
          ? new Date(Math.max(...customerOrders.map(o => new Date(o.createdAt).getTime())))
          : null;
      return { ...customer, totalSpent, totalOrders: customerOrders.length, activeOrders, lastOrderDate };
    });
  }, [rawCustomers, customerOrdersMap]);

  const filteredCustomers = useMemo(() => {
    let filtered = [...enhancedCustomers];
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(c =>
        c.name?.toLowerCase().includes(query) ||
        c.phone?.includes(query) ||
        c.email?.toLowerCase().includes(query)
      );
    }

    if (selectedSort) {
      filtered.sort((a, b) => {
        const aValue = a[selectedSort.key as keyof EnhancedCustomer];
        const bValue = b[selectedSort.key as keyof EnhancedCustomer];

        if (aValue === bValue) return 0;

        const sortOrder = selectedSort.direction === 'asc' ? 1 : -1;

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return (aValue - bValue) * sortOrder;
        }
        if (aValue instanceof Date && bValue instanceof Date) {
          return (aValue.getTime() - bValue.getTime()) * sortOrder;
        }
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return aValue.localeCompare(bValue) * sortOrder;
        }
        return 0;
      });
    } else {
      filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    return filtered;
  }, [enhancedCustomers, searchQuery, selectedSort]);
  
  const handleToggleSelection = useCallback((customer: EnhancedCustomer) => {
    console.log('handleToggleSelection called for customer:', customer.id);
    const customerId = customer.id.toString();
    setSelectedCustomers(prevSelected => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(customerId)) {
        newSelected.delete(customerId);
      } else {
        newSelected.add(customerId);
      }
      if (newSelected.size === 0) {
        setIsSelectionMode(false);
      }
      console.log('newSelected customers (functional update):', newSelected);
      return newSelected;
    });
  }, []);

  const handleLongPress = useCallback((customer: EnhancedCustomer) => {
    setIsSelectionMode(true);
    setSelectedCustomers(new Set([customer.id.toString()]));
  }, []);

  const handlePress = useCallback((customer: EnhancedCustomer, normalPressAction: () => void) => {
    if (isSelectionMode) {
      handleToggleSelection(customer);
    } else {
      normalPressAction();
    }
  }, [isSelectionMode, handleToggleSelection]);
  
  // ... other callbacks like handleRefresh, handleSelectAll, etc. ...
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await actions.reloadData();
      showSuccess('Customers refreshed successfully!');
    } catch (error) {
      showError('Failed to refresh customers.');
    } finally {
      setRefreshing(false);
    }
  }, [actions, showSuccess, showError]);
  const handleSelectAll = useCallback(() => {
    setSelectedCustomers(prev => {
      if (prev.size === filteredCustomers.length) {
        setIsSelectionMode(false);
        return new Set();
      } else {
        return new Set(filteredCustomers.map(customer => customer.id.toString()));
      }
    });
  }, [filteredCustomers]);
  const cancelSelection = useCallback(() => {
    setSelectedCustomers(new Set());
    setIsSelectionMode(false);
  }, []);
  const handleBulkDeleteConfirm = useCallback(async () => {
    if (selectedCustomers.size === 0) return;

    try {
      const selectedIds = Array.from(selectedCustomers);
      const deletedCount = selectedCustomers.size;

      await Promise.all(selectedIds.map(id => actions.deleteCustomer(id)));

      setSelectedCustomers(new Set());
      setIsSelectionMode(false);

      // Assuming reloadData exists or similar action to refresh the list
      // If not, you might need to filter the rawCustomers directly
      await actions.reloadData(); 

      showSuccess(
        `Successfully deleted ${deletedCount} customer${deletedCount > 1 ? 's' : ''}.`
      );
    } catch (error) {
      showError('Failed to delete customers. Please try again.');
    }
  }, [selectedCustomers, actions, showSuccess, showError]);
  const isSelected = useCallback((customerId: string | number) => {
    const result = selectedCustomers.has(customerId.toString());
    console.log(`Customer ${customerId} is selected: ${result}`);
    return result;
  }, [selectedCustomers]);
  const keyExtractor = useCallback((item: EnhancedCustomer) => item.id.toString(), []);

  const renderCustomerCard: ListRenderItem<EnhancedCustomer> = useCallback(({ item }) => (
    <View style={styles.cardContainer}>
      <DataCard
        id={item.id}
        name={item.name}
        phone={item.phone} // Pass the phone number
        totalSpent={item.totalSpent}
        totalOrders={item.totalOrders}
        activeOrders={item.activeOrders} // Pass active orders
        lastOrderDate={item.lastOrderDate}
        cardType='customer'
        // ✅ THIS IS THE CRITICAL CHANGE
        onPress={() => handlePress(item, () => navigation.navigate('CustomerDetails', { customerId: item.id }))}
        onLongPress={() => handleLongPress(item)}
        onCheckboxPress={() => handleToggleSelection(item)}
        selected={isSelected(item.id)}
        showSelectionIndicator={isSelectionMode}
      />
    </View>
  ), [isSelectionMode, isSelected, handlePress, handleLongPress, navigation, handleToggleSelection]);

  return (
    <View style={styles.container}>
      {isSelectionMode ? (
        <SelectionHeader
          count={selectedCustomers.size}
          areAllSelected={selectedCustomers.size === filteredCustomers.length && filteredCustomers.length > 0}
          onCancel={cancelSelection}
          onSelectAll={handleSelectAll}
          onDelete={() => setActionSheetVisible(true)}
        />
      ) : (
        <Header
          title='Customers'
          showBack={navigation.canGoBack()}
          onBackPress={navigation.goBack}
          showSearch={true}
          onSearch={setSearchQuery}
          actions={[
            { icon: 'filter', onPress: () => filterSortBottomSheetRef.current?.present() },
            { icon: 'plus', onPress: () => navigation.navigate('AddCustomer') },
          ]}
        />
      )}

      {!isSelectionMode && (
        <View style={styles.chipGroupContainer}>
          <ChipGroup
            filters={filterOptions}
            selectedFilter={selectedFilter}
            onFilterChange={(filter) => setSelectedFilter(String(filter))}
            showCounts={true}
            data={enhancedCustomers}
            borderRadius='full-rounded'
          />
        </View>
      )}
      
      <FlatList<EnhancedCustomer>
        data={filteredCustomers}
        renderItem={renderCustomerCard}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContainer}
        getItemLayout={(data, index) => (
          { length: ITEM_HEIGHT, offset: ITEM_HEIGHT * index, index }
        )}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} tintColor={theme.colors.primary} />
        }
        ListEmptyComponent={
          <EmptyState
            type="custom"
            icon="users"
            title="No Customers Found"
            description="Try adjusting your filters or add a new customer."
          />
        }
      />

      {!isSelectionMode && (
        <Button
          variant='primary'
          onPress={() => navigation.navigate('AddCustomer')}
          style={{ ...styles.fab, bottom: insets.bottom + SPACING.sm }}
          icon='plus'
        >
          Add Customer
        </Button>
      )}
      
      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete ${selectedCustomers.size} customers?`}
        description="This action cannot be undone."
        options={[
          { text: 'Delete', style: 'destructive', onPress: handleBulkDeleteConfirm },
          { text: 'Cancel', style: 'cancel', onPress: () => setActionSheetVisible(false) },
        ]}
      />

      <FilterSortBottomSheet
        ref={filterSortBottomSheetRef}
        filters={filterOptions.map(f => f.label)}
        selectedFilter={selectedFilter}
        onSelectFilter={setSelectedFilter}
        sortOptions={sortOptions}
        selectedSort={selectedSort}
        onSelectSort={setSelectedSort}
        onConfirm={() => filterSortBottomSheetRef.current?.close()}
      />
    </View>
  );
};

// --- STYLES ---
const styles = StyleSheet.create({
    container: { flex: 1 },
    cardContainer: { marginBottom: SPACING.xxs },
    chipGroupContainer: { zIndex: 1, padding: SPACING.sm, paddingHorizontal: SPACING.md },
    listContainer: { paddingHorizontal: SPACING.md, paddingBottom: 64 },
    fab: {
        position: 'absolute',
        left: SPACING.sm,
        right: SPACING.sm,
        borderRadius: BORDER_RADIUS.lg,
        height: 44,
    },
});

const createSelectionHeaderStyles = (theme: any, insets: any) => StyleSheet.create({
    container: { paddingTop: insets.top, backgroundColor: theme.colors.surface, elevation: 4, shadowColor: '#000', shadowOpacity: 0.1, shadowRadius: 4, zIndex: 100 },
    content: { height: 56, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: SPACING.sm },
    selectAllContainer: { flexDirection: 'row', alignItems: 'center' },
    title: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.semibold, marginLeft: SPACING.sm, color: theme.colors.onSurface },
    selectionCheckbox: {
      marginRight: SPACING.sm,
    },
});

export default CustomersScreen;