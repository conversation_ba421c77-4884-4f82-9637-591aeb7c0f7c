/**
 * Master Database Configuration
 * Single configuration file for the entire database system
 */

export interface MasterDatabaseConfig {
  // Database settings
  database: {
    name: string;
    location: string;
    version: number;
    enableWAL: boolean;
    enableForeignKeys: boolean;
    busyTimeout: number;
  };

  // Cache settings
  cache: {
    enabled: boolean;
    maxSize: number;
    defaultTTL: number; // milliseconds
    persistToDisk: boolean;
    cleanupInterval: number; // milliseconds
  };

  // Offline sync settings
  sync: {
    enabled: boolean;
    syncInterval: number; // milliseconds
    maxRetries: number;
    retryDelay: number; // milliseconds
    batchSize: number;
    conflictResolution: 'client' | 'server' | 'manual';
  };

  // Monitoring settings
  monitoring: {
    enabled: boolean;
    healthCheckInterval: number; // milliseconds
    performanceTracking: boolean;
    maxAvgQueryTime: number; // milliseconds
    maxErrorRate: number; // percentage (0-1)
    alertThresholds: {
      slowQuery: number; // milliseconds
      highErrorRate: number; // percentage (0-1)
      lowCacheHitRate: number; // percentage (0-1)
    };
  };

  // Backup settings
  backup: {
    enabled: boolean;
    autoBackupInterval: number; // milliseconds
    maxBackups: number;
    compressionEnabled: boolean;
    includeSettings: boolean;
    excludeTables: string[];
  };

  // Security settings
  security: {
    enableEncryption: boolean;
    encryptionKey?: string;
    enableAuditLog: boolean;
    sensitiveFields: string[];
    dataRetentionDays: number;
  };

  // Performance settings
  performance: {
    enableQueryOptimization: boolean;
    enableIndexHints: boolean;
    connectionPoolSize: number;
    queryTimeout: number; // milliseconds
    transactionTimeout: number; // milliseconds
    enablePreparedStatements: boolean;
  };

  // Development settings
  development: {
    enableDebugLogging: boolean;
    enableQueryLogging: boolean;
    enablePerformanceLogging: boolean;
    seedDataOnInit: boolean;
    resetDatabaseOnInit: boolean;
  };
}

// Default configuration
export const DEFAULT_CONFIG: MasterDatabaseConfig = {
  database: {
    name: 'tailorza_master.db',
    location: 'default',
    version: 1,
    enableWAL: true,
    enableForeignKeys: true,
    busyTimeout: 30000,
  },

  cache: {
    enabled: true,
    maxSize: 1000,
    defaultTTL: 300000, // 5 minutes
    persistToDisk: true,
    cleanupInterval: 600000, // 10 minutes
  },

  sync: {
    enabled: true,
    syncInterval: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 5000, // 5 seconds
    batchSize: 50,
    conflictResolution: 'client',
  },

  monitoring: {
    enabled: true,
    healthCheckInterval: 60000, // 1 minute
    performanceTracking: true,
    maxAvgQueryTime: 1000, // 1 second
    maxErrorRate: 0.05, // 5%
    alertThresholds: {
      slowQuery: 2000, // 2 seconds
      highErrorRate: 0.1, // 10%
      lowCacheHitRate: 0.7, // 70%
    },
  },

  backup: {
    enabled: true,
    autoBackupInterval: 3600000, // 1 hour
    maxBackups: 10,
    compressionEnabled: true,
    includeSettings: true,
    excludeTables: ['audit_logs'], // Exclude large log tables
  },

  security: {
    enableEncryption: false, // Enable in production
    enableAuditLog: true,
    sensitiveFields: ['password', 'ssn', 'credit_card'],
    dataRetentionDays: 365,
  },

  performance: {
    enableQueryOptimization: true,
    enableIndexHints: true,
    connectionPoolSize: 5,
    queryTimeout: 30000, // 30 seconds
    transactionTimeout: 60000, // 1 minute
    enablePreparedStatements: true,
  },

  development: {
    enableDebugLogging: __DEV__ || false,
    enableQueryLogging: __DEV__ || false,
    enablePerformanceLogging: __DEV__ || false,
    seedDataOnInit: __DEV__ || false,
    resetDatabaseOnInit: false,
  },
};

// Environment-specific configurations
export const PRODUCTION_CONFIG: Partial<MasterDatabaseConfig> = {
  cache: {
    enabled: true,
    maxSize: 10000,
    defaultTTL: 3600000,
    persistToDisk: true,
    cleanupInterval: 300000,
  },

  sync: {
    enabled: true,
    syncInterval: 300000,
    maxRetries: 3,
    retryDelay: 5000,
    batchSize: 100,
    conflictResolution: 'server',
  },

  monitoring: {
    enabled: true,
    healthCheckInterval: 60000,
    performanceTracking: true,
    maxAvgQueryTime: 1000,
    maxErrorRate: 0.05,
    alertThresholds: {
      slowQuery: 2000,
      highErrorRate: 0.1,
      lowCacheHitRate: 0.5,
    },
  },

  backup: {
    enabled: true,
    autoBackupInterval: 86400000,
    maxBackups: 7,
    compressionEnabled: true,
    includeSettings: true,
    excludeTables: [],
  },

  security: {
    enableEncryption: true,
    enableAuditLog: true,
    sensitiveFields: ['password', 'token', 'key'],
    dataRetentionDays: 365,
  },

  development: {
    enableDebugLogging: false,
    enableQueryLogging: false,
    enablePerformanceLogging: false,
    seedDataOnInit: false,
    resetDatabaseOnInit: false,
  },
};

// Development configuration
export const DEVELOPMENT_CONFIG: Partial<MasterDatabaseConfig> = {
  database: {
    name: 'tailorza_master_dev.db',
    location: 'default',
    version: 1,
    enableWAL: true,
    enableForeignKeys: true,
    busyTimeout: 30000,
  },

  cache: {
    enabled: false,
    maxSize: 5000,
    defaultTTL: 3600000,
    persistToDisk: false,
    cleanupInterval: 300000,
  },

  sync: {
    enabled: false,
    syncInterval: 300000,
    maxRetries: 3,
    retryDelay: 5000,
    batchSize: 50,
    conflictResolution: 'client',
  },

  monitoring: {
    enabled: true,
    healthCheckInterval: 30000,
    performanceTracking: true,
    maxAvgQueryTime: 2000,
    maxErrorRate: 0.1,
    alertThresholds: {
      slowQuery: 3000,
      highErrorRate: 0.2,
      lowCacheHitRate: 0.3,
    },
  },

  backup: {
    enabled: false,
    autoBackupInterval: 3600000,
    maxBackups: 3,
    compressionEnabled: false,
    includeSettings: true,
    excludeTables: ['logs', 'cache'],
  },

  security: {
    enableEncryption: false,
    enableAuditLog: true,
    sensitiveFields: ['password', 'token'],
    dataRetentionDays: 30,
  },

  development: {
    enableDebugLogging: true,
    enableQueryLogging: true,
    enablePerformanceLogging: true,
    seedDataOnInit: true,
    resetDatabaseOnInit: true,
  },
};

// Test configuration
export const TEST_CONFIG: Partial<MasterDatabaseConfig> = {
  database: {
    name: 'tailorza_master_test.db',
    location: 'default',
    version: 1,
    enableWAL: false,
    enableForeignKeys: true,
    busyTimeout: 5000,
  },

  cache: {
    enabled: false,
    maxSize: 1000,
    defaultTTL: 60000,
    persistToDisk: false,
    cleanupInterval: 30000,
  },

  sync: {
    enabled: false,
    syncInterval: 0,
    maxRetries: 0,
    retryDelay: 1000,
    batchSize: 10,
    conflictResolution: 'client',
  },

  monitoring: {
    enabled: false,
    healthCheckInterval: 0,
    performanceTracking: false,
    maxAvgQueryTime: 5000,
    maxErrorRate: 1,
    alertThresholds: {
      slowQuery: 10000,
      highErrorRate: 1,
      lowCacheHitRate: 0,
    },
  },

  backup: {
    enabled: false,
    autoBackupInterval: 0,
    maxBackups: 1,
    compressionEnabled: false,
    includeSettings: false,
    excludeTables: ['logs', 'cache', 'temp'],
  },

  security: {
    enableEncryption: false,
    enableAuditLog: false,
    sensitiveFields: ['password'],
    dataRetentionDays: 1,
  },

  development: {
    enableDebugLogging: false,
    enableQueryLogging: false,
    enablePerformanceLogging: false,
    seedDataOnInit: true,
    resetDatabaseOnInit: true,
  },
};

/**
 * Get configuration based on environment
 */
export function getConfig(
  environment: 'development' | 'production' | 'test' = 'development'
): MasterDatabaseConfig {
  let envConfig: Partial<MasterDatabaseConfig>;

  switch (environment) {
    case 'production':
      envConfig = PRODUCTION_CONFIG;
      break;
    case 'test':
      envConfig = TEST_CONFIG;
      break;
    case 'development':
    default:
      envConfig = DEVELOPMENT_CONFIG;
      break;
  }

  // Deep merge default config with environment-specific config
  return mergeConfigs(DEFAULT_CONFIG, envConfig);
}

/**
 * Deep merge two configuration objects
 */
function mergeConfigs(
  defaultConfig: MasterDatabaseConfig,
  envConfig: Partial<MasterDatabaseConfig>
): MasterDatabaseConfig {
  const result = { ...defaultConfig };

  for (const [key, value] of Object.entries(envConfig)) {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key as keyof MasterDatabaseConfig] = {
        ...result[key as keyof MasterDatabaseConfig],
        ...value,
      } as any;
    } else {
      result[key as keyof MasterDatabaseConfig] = value as any;
    }
  }

  return result;
}

/**
 * Validate configuration
 */
export function validateConfig(config: MasterDatabaseConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate database settings
  if (!config.database.name) {
    errors.push('Database name is required');
  }

  // Validate cache settings
  if (config.cache.enabled && config.cache.maxSize <= 0) {
    errors.push('Cache max size must be greater than 0');
  }

  // Validate sync settings
  if (config.sync.enabled && config.sync.syncInterval <= 0) {
    errors.push('Sync interval must be greater than 0');
  }

  // Validate monitoring settings
  if (config.monitoring.enabled && config.monitoring.healthCheckInterval <= 0) {
    errors.push('Health check interval must be greater than 0');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
