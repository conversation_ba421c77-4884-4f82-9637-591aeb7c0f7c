import { useNavigation } from '@react-navigation/native';
import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  ListRenderItemInfo,
} from 'react-native';
import { Text, Card } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import ChipGroup from '../../components/ui/ChipGroup';
import EmptyState from '../../components/ui/EmptyState';
import ActionSheet from '../../components/ui/ActionSheet';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import { ActionSheetOption } from '../../types';
import { SPACING, BORDER_RADIUS } from '../../theme/theme'; // Assuming these are in your theme

// --- Placeholders for your data types ---
// It's a good practice to have these defined in a central types file
interface Order {
  id: string;
  totalAmount?: number;
  createdAt?: string;
  updatedAt?: string;
  status?: string;
}

interface Staff {
  id: string;
  name: string;
  createdAt?: string;
}

// --- Constants and Utility Functions (Outside Component for Stability) ---

const FILTER_OPTIONS = [
  { id: 'All', label: 'All' },
  { id: 'order', label: 'Orders' },
  { id: 'product', label: 'Products' },
  { id: 'customer', label: 'Customers' },
  { id: 'inventory', label: 'Inventory' },
  { id: 'financial', label: 'Financial' },
  { id: 'staff', label: 'Staff' },
  { id: 'system', label: 'System' },
];

/**
 * Formats a timestamp into a relative time string (e.g., "5m ago").
 * @param timestamp - The ISO string of the date to format.
 * @returns A formatted relative time string.
 */
const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  return date.toLocaleDateString();
};

// --- Type Definitions ---

interface ActivityLogItem {
  id: string;
  type: 'product' | 'order' | 'customer' | 'inventory' | 'financial' | 'system' | 'staff';
  action: string;
  title: string;
  description: string;
  timestamp: string;
  icon: PhosphorIconName; // Using strict type for icons
  color: string;
  data: any;
}

// --- Memoized List Item Component for Performance ---

const ActivityItem: React.FC<{ item: ActivityLogItem }> = React.memo(({ item }) => {
  const theme = useTheme();
  const styles = useStyles();

  return (
    <Card style={[styles.activityCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content style={styles.activityContent}>
        <View style={styles.activityHeader}>
          <View style={[styles.iconContainer, { backgroundColor: `${item.color}15` }]}>
            <PhosphorIcon name={item.icon} size={20} color={item.color} />
          </View>
          <View style={styles.activityInfo}>
            <Text variant='titleSmall' style={{ color: theme.colors.onSurface }}>
              {item.title}
            </Text>
            <Text variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
              {item.description}
            </Text>
          </View>
          <Text variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
            {formatTimestamp(item.timestamp)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
});

// --- Main Screen Component ---

const ActivityLogScreen: React.FC = () => {
  const theme = useTheme();
  const { state: dataState } = useData();
  const navigation = useNavigation();
  const styles = useStyles();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback((title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
    setConfirmationActionSheetTitle(title);
    setConfirmationActionSheetDescription(message);
    setConfirmationActionSheetOptions([
      { text: options?.confirmText || 'Confirm', onPress: () => { onConfirm(); setConfirmationActionSheetVisible(false); }, style: options?.type === 'danger' ? 'destructive' : 'primary', isAction: true },
      { text: options?.cancelText || 'Cancel', onPress: () => setConfirmationActionSheetVisible(false), style: 'cancel', isAction: false },
    ]);
    setConfirmationActionSheetVisible(true);
  }, []);

  const getStatusColor = useCallback((status: string): string => {
    switch (status) {
      case 'Completed': return theme.colors.tertiary;
      case 'In Progress': return theme.colors.secondary;
      case 'Pending': return theme.colors.primary;
      case 'Cancelled': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  }, [theme.colors]);

  const activityLog = useMemo((): ActivityLogItem[] => {
    const orderActivities = dataState.orders.reduce((acc, order: Order) => {
      acc.push({ id: `order-${order.id}`, type: 'order' as const, action: 'created', title: 'Order Created', description: `Order #${order.id} for ${order.totalAmount?.toFixed(2) || '0.00'}৳`, timestamp: order.createdAt || new Date().toISOString(), icon: 'clipboard-text' as const, color: theme.colors.secondary, data: order });
      if (order.status) {
        acc.push({ id: `order-status-${order.id}`, type: 'order' as const, action: 'status_changed', title: 'Order Status Updated', description: `Order #${order.id} status is now ${order.status}`, timestamp: order.updatedAt || new Date().toISOString(), icon: 'refresh' as const, color: getStatusColor(order.status), data: order }); // **FIXED ICON NAME**
      }
      return acc;
    }, [] as ActivityLogItem[]);

    const activities: ActivityLogItem[] = [
      ...dataState.products.map(p => ({ id: `product-${p.id}`, type: 'product' as const, action: 'created', title: 'Product Created', description: `${p.name} was added`, timestamp: p.createdAt || new Date().toISOString(), icon: 'plus-circle' as const, color: theme.colors.primary, data: p })),
      ...orderActivities,
      ...dataState.customers.map(c => ({ id: `customer-${c.id}`, type: 'customer' as const, action: 'created', title: 'Customer Added', description: `${c.name} was added`, timestamp: c.createdAt || new Date().toISOString(), icon: 'user-plus' as const, color: theme.colors.tertiary, data: c })),
      ...(dataState.staff || []).map((s: Staff) => ({ id: `staff-${s.id}`, type: 'staff' as const, action: 'assigned', title: 'Staff Assignment', description: `${s.name} was assigned`, timestamp: s.createdAt || new Date().toISOString(), icon: 'user-gear' as const, color: theme.colors.secondary, data: s })),
      { id: 'system-login', type: 'system' as const, action: 'user_login', title: 'User Login', description: 'User logged into the system', timestamp: new Date().toISOString(), icon: 'shield-check' as const, color: theme.colors.primary, data: { type: 'login' } },
    ];
    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [dataState.products, dataState.orders, dataState.customers, dataState.staff, theme.colors, getStatusColor]);

  const filteredActivities = useMemo(() => {
    return activityLog.filter(activity => {
      const matchesSearch = searchQuery === '' ||
        activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = selectedFilter === 'All' || activity.type === selectedFilter;
      return matchesSearch && matchesFilter;
    });
  }, [activityLog, searchQuery, selectedFilter]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Activity log data refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.warn('Failed to refresh activity log data', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const renderActivityItem = useCallback(
    ({ item }: ListRenderItemInfo<ActivityLogItem>) => <ActivityItem item={item} />,
    []
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='Activity Log' onBackPress={() => navigation.goBack()} showBack={true}  />
      <View style={styles.content}>
        <ChipGroup
          filters={FILTER_OPTIONS}
          selectedFilter={selectedFilter}
          onFilterChange={(filter) => setSelectedFilter(filter as string)}
          style={styles.chipGroup}
        />
        <FlatList
          data={filteredActivities}
          renderItem={renderActivityItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]} tintColor={theme.colors.primary} />}
          ListEmptyComponent={
            <EmptyState
              type='activities'
              searchQuery={searchQuery}
              description='No activities found matching your filters.'
              actionLabel='Clear Filters'
              onActionPress={() => setSelectedFilter('All')}
              iconColor={theme.colors.onSurfaceVariant}
              style={{}} 
            />
          }
        />
      </View>
      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

// --- Styles ---

const useStyles = () => {
  return StyleSheet.create({
    container: { flex: 1 },
    content: { flex: 1, paddingHorizontal: SPACING.md },
    listContainer: { paddingBottom: SPACING.lg },
    activityCard: { marginBottom: SPACING.sm, borderRadius: BORDER_RADIUS.lg },
    activityContent: { paddingVertical: SPACING.md },
    activityHeader: { flexDirection: 'row', alignItems: 'center' },
    iconContainer: { width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', marginRight: SPACING.md },
    activityInfo: { flex: 1 },
    chipGroup: { marginVertical: SPACING.md },
  });
};

export default ActivityLogScreen;