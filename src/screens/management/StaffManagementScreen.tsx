import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Avatar, Card, Chip, FAB, ProgressBar, Text, Title } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { ActionSheetOption } from '../../types';
import StatCardGroup from '../../components/ui/StatCardGroup';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import LoggingService from '../../services/LoggingService';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Staff, StaffRole, StatCard } from '../../types';
import { StaffManagementScreenProps } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import Button from '@/components/ui/Button';

interface SortOption {
  key: string;
  label: string;
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

const StaffManagementScreen: React.FC<StaffManagementScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  const { state: authState } = useAuth();
  const { user } = authState;
  const staff = dataState.staff; // Explicitly define staff
  const insets = useSafeAreaInsets();
  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // All hooks must be at the top level
  const [searchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedSort] = useState<SelectedSort>({ key: 'name', direction: 'asc' });

  const selectMode = (route?.params as any)?.selectMode || false;
  const onStaffSelect = (route?.params as any)?.onStaffSelect;
  const returnTo = (route?.params as any)?.returnTo;
  const requiredRole = (route?.params as any)?.requiredRole;
  const requiredSkill = (route?.params as any)?.requiredSkill;

  const filters = [
    'All',
    'Tailor',
    'Cutter',
    'Finisher',
    'Quality Checker',
    'Manager',
    'Assistant',
    'Available',
    'Overloaded',
  ];

  const filteredStaff = useMemo(() => {
    let filtered = dataState.staff || [];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        staffMember =>
          staffMember.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (staffMember.email || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
          (staffMember.phone || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
          staffMember.employeeId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply role filter
    switch (selectedFilter) {
      case 'Tailor':
        filtered = filtered.filter(staffMember => staffMember.role === 'tailor');
        break;
      case 'Cutter':
        filtered = filtered.filter(staffMember => staffMember.role === 'cutter');
        break;
      case 'Finisher':
        filtered = filtered.filter(staffMember => staffMember.role === 'finisher');
        break;
      case 'Quality Checker':
        filtered = filtered.filter(staffMember => staffMember.role === 'quality_checker');
        break;
      case 'Manager':
        filtered = filtered.filter(staffMember => staffMember.role === 'manager');
        break;
      case 'Assistant':
        filtered = filtered.filter(staffMember => staffMember.role === 'assistant');
        break;
      case 'Available':
        filtered = filtered.filter(
          staffMember => staffMember.isActive // Simplified - workload data not available
        );
        break;
      case 'Overloaded':
        filtered = filtered.filter(
          staffMember => false // Simplified - workload data not available
        );
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[selectedSort.key as keyof Staff];
      let bValue: any = b[selectedSort.key as keyof Staff];

      // Handle nested properties - simplified for available data
      if (selectedSort.key === 'workload.efficiency') {
        aValue = 0; // Workload data not available
        bValue = 0;
      } else if (selectedSort.key === 'performance.qualityRating') {
        aValue = 0; // Performance data not available
        bValue = 0;
      } else if (selectedSort.key === 'workload.scheduledHours') {
        aValue = 0; // Workload data not available
        bValue = 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (selectedSort.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [staff, user?.selectedOutlet, searchQuery, selectedFilter, selectedSort]);

  const staffStats = useMemo(() => {
    const totalStaff = dataState.staff?.length || 0;
    const activeStaff = dataState.staff?.filter(s => s.isActive).length || 0;
    const availableStaff =
      dataState.staff?.filter(
        s => s.isActive && (s.workload?.scheduledHours || 0) < (s.workload?.availableHours || 0)
      ).length || 0;
    const averageEfficiency = dataState.staff?.length
      ? 0 // Efficiency data not available
      : 0;

    return [
      {
        key: 'total',
        title: 'Total Staff',
        value: totalStaff.toString(),
        icon: 'account-group',
        iconColor: theme.colors.primary,
        iconBackground: theme.colors.primaryContainer,
      },
      {
        key: 'active',
        title: 'Active Staff',
        value: activeStaff.toString(),
        icon: 'account-check',
        iconColor: '#10b981',
        iconBackground: '#d1fae5',
      },
      {
        key: 'available',
        title: 'Available',
        value: availableStaff.toString(),
        icon: 'account-clock',
        iconColor: '#3b82f6',
        iconBackground: '#dbeafe',
      },
      {
        key: 'efficiency',
        title: 'Avg Efficiency',
        value: `${averageEfficiency.toFixed(1)}%`,
        icon: 'trend-up',
        iconColor: '#f59e0b',
        iconBackground: '#fef3c7',
      },
    ];
  }, [staff, theme.colors]);

  React.useEffect(() => {
    dataActions.reloadData();
  }, [dataActions]);

  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      await dataActions.reloadData();
    } catch (error) {
      LoggingService.error('Failed to refresh staff data', 'STAFF_MANAGEMENT', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [dataActions]);

  // Show loading state if data is not loaded yet
  if (!dataState.isDataLoaded) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: theme.colors.background,
            justifyContent: 'center',
            alignItems: 'center',
          },
        ]}
      >
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={{ marginTop: 16, color: theme.colors.onSurfaceVariant }}>
          Loading staff...
        </Text>
      </View>
    );
  }

  const handleStaffSelect = (staffMember: Staff): void => {
    if (selectMode && onStaffSelect) {
      // Check if staff meets requirements
      if (requiredRole && staffMember.role !== requiredRole) {
        showConfirmation(
          'Role Mismatch',
          `This staff member is a ${staffMember.role}, but ${requiredRole} is required.`,
          () => {
            onStaffSelect(staffMember);
            if (returnTo) {
              navigation.navigate(returnTo as any);
            }
          },
          {
            confirmText: 'Select Anyway',
            cancelText: 'Cancel',
            type: 'warning',
          }
        );
      } else if (
        requiredSkill &&
        !staffMember.skills?.some(skill =>
          skill.name.toLowerCase().includes(requiredSkill.toLowerCase())
        )
      ) {
        showConfirmation(
          'Skill Mismatch',
          `This staff member doesn't have the required skill: ${requiredSkill}`,
          () => {
            onStaffSelect(staffMember);
            if (returnTo) {
              navigation.navigate(returnTo as any);
            }
          },
          {
            confirmText: 'Select Anyway',
            cancelText: 'Cancel',
            type: 'warning',
          }
        );
      } else {
        onStaffSelect(staffMember);
        if (returnTo) {
          navigation.navigate(returnTo as any);
        }
      }
    } else {
      // Navigate to staff details
      navigation.navigate('StaffDetails', { staffId: staffMember.id });
    }
  };

  const handleStaffLongPress = (staffMember: Staff): void => {
    // For now, we'll use a simple confirmation for delete
    // The other options can be implemented as separate buttons or in a bottom sheet
    showConfirmation(
      'Staff Options',
      `What would you like to do with ${staffMember.name}?`,
      () => {
        // This will be replaced with actual navigation logic
        navigation.navigate('StaffDetails', { staffId: staffMember.id });
      },
      {
        confirmText: 'View Details',
        cancelText: 'Cancel',
        type: 'info',
      }
    );
  };

  const handleAddStaff = (): void => {
    navigation.navigate('AddStaff');
  };

  const getRoleIcon = (role: StaffRole): string => {
    switch (role) {
      case 'tailor':
        return 'scissors-cutting';
      case 'cutter':
        return 'scissors';
      case 'finisher':
        return 'check-circle';
      case 'quality_checker':
        return 'check-decagram';
      case 'manager':
        return 'account-tie';
      case 'assistant':
        return 'account-plus';
      case 'designer':
        return 'palette';
      default:
        return 'user';
    }
  };

  const getRoleColor = (role: StaffRole): string => {
    switch (role) {
      case 'tailor':
        return '#10b981';
      case 'cutter':
        return '#3b82f6';
      case 'finisher':
        return '#8b5cf6';
      case 'quality_checker':
        return '#f59e0b';
      case 'manager':
        return '#ef4444';
      case 'assistant':
        return '#6b7280';
      case 'designer':
        return '#ec4899';
      default:
        return '#6b7280';
    }
  };

  const getAvailabilityStatus = (
    staffMember: Staff
  ): { status: string; color: string; backgroundColor: string } => {
    // Check if staff member is active first
    if (!staffMember.isActive) {
      return {
        status: 'Unavailable',
        color: '#6b7280',
        backgroundColor: '#f3f4f6',
      };
    }

    const utilization =
      (staffMember.workload?.scheduledHours || 0) / (staffMember.workload?.availableHours || 1);

    if (utilization >= 1) {
      return {
        status: 'Overloaded',
        color: theme.colors.error,
        backgroundColor: theme.colors.errorContainer,
      };
    } else if (utilization >= 0.8) {
      return {
        status: 'Busy',
        color: '#f59e0b',
        backgroundColor: '#fef3c7',
      };
    } else {
      return {
        status: 'Available',
        color: '#10b981',
        backgroundColor: '#d1fae5',
      };
    }
  };

  const getSkillLevelColor = (level: SkillLevel): string => {
    switch (level) {
      case 'beginner':
        return '#6b7280';
      case 'intermediate':
        return '#3b82f6';
      case 'advanced':
        return '#f59e0b';
      case 'expert':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const renderStaffCard = ({ item }: { item: Staff }): React.ReactElement => {
    const availabilityStatus = getAvailabilityStatus(item);
    const roleColor = getRoleColor(item.role);
    const utilization = (item.workload?.scheduledHours || 0) / (item.workload?.availableHours || 1);

    return (
      <Card
        style={[styles.staffCard, { backgroundColor: theme.colors.surface }]}
        onPress={() => handleStaffSelect(item)}
        onLongPress={() => handleStaffLongPress(item)}
        contentStyle={{ padding: 4 }}
      >
        <Card.Content style={{ overflow: 'hidden' }}>
          <View style={styles.cardHeader}>
            <View style={styles.staffInfo}>
              <Avatar.Text
                size={48}
                label={item.name
                  .split(' ')
                  .map(n => n[0])
                  .join('')
                  .substring(0, 2)}
                style={[styles.avatar, { backgroundColor: roleColor }]}
              />
              <View style={styles.staffDetails}>
                <Title
                  style={[styles.staffName, { color: theme.colors.onSurface }]}
                  numberOfLines={1}
                  ellipsizeMode='tail'
                >
                  {item.name}
                </Title>
                <View style={styles.roleContainer}>
                  <PhosphorIcon name={getRoleIcon(item.role) as any} size={16} color={roleColor} />
                  <Text
                    style={[styles.roleText, { color: roleColor }]}
                    numberOfLines={1}
                    ellipsizeMode='tail'
                  >
                    {item.role.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={[
                styles.availabilityChip,
                {
                  backgroundColor: availabilityStatus.backgroundColor,
                  paddingHorizontal: 6,
                  paddingVertical: 2,
                  borderRadius: 8,
                },
              ]}
            >
              <Text style={{ color: availabilityStatus.color, fontSize: 10, fontWeight: '500' }}>
                {availabilityStatus.status}
              </Text>
            </View>
          </View>

          <View style={styles.performanceSection}>
            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Quality Rating
              </Text>
              <View style={styles.ratingContainer}>
                <PhosphorIcon name='star' size={16} color='#f59e0b' />
                <Text style={[styles.ratingText, { color: theme.colors.onSurface }]}>
                  {(item.performance?.qualityRating || 0).toFixed(1)}
                </Text>
              </View>
            </View>

            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Efficiency
              </Text>
              <Text style={[styles.performanceValue, { color: theme.colors.on }]}>
                {item.workload?.efficiency || 0}%
              </Text>
            </View>

            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Workload
              </Text>
              <View style={styles.workloadContainer}>
                <ProgressBar
                  progress={utilization}
                  color={utilization > 0.8 ? theme.colors.error : theme.colors.primary}
                  style={styles.progressBar}
                />
                <Text
                  style={[styles.workloadText, { color: theme.colors.onSurface }]}
                  numberOfLines={1}
                  ellipsizeMode='tail'
                >
                  {item.workload?.scheduledHours || 0}/{item.workload?.availableHours || 0}h
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.skillsSection}>
            <Text style={[styles.skillsLabel, { color: theme.colors.onVariant }]}>Skills</Text>
            <View style={styles.skillsContainer}>
              {item.skills?.slice(0, 3).map((skill, index) => (
                <Chip
                  key={index}
                  style={[
                    styles.skillChip,
                    { backgroundColor: `${getSkillLevelColor(skill.level)}20` },
                  ]}
                  textStyle={{ color: getSkillLevelColor(skill.level) }}
                  compact
                >
                  {skill.name}
                </Chip>
              ))}
              {item.skills && item.skills.length > 3 && (
                <Chip style={styles.moreSkillsChip} compact>
                  +{item.skills.length - 3} more
                </Chip>
              )}
            </View>
          </View>

          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
                {item.performance?.ordersCompleted || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>Orders</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
                {item.workload?.currentOrders || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>Active</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
                {item.performance?.onTimeDeliveryRate || 0}%
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>On Time</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = (): React.ReactElement => (
    <View style={{ padding: 32, alignItems: 'center' }}>
      <Text
        style={{
          fontSize: 16,
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          marginBottom: 16,
        }}
      >
        {searchQuery
          ? 'No staff members match your search criteria'
          : 'Add your first staff member to get started'}
      </Text>
      <Button
        variant='primary'
        onPress={handleAddStaff}
        textColor={theme.colors.onPrimary}
        icon='account-plus'
      >
        Add Staff
      </Button>
    </View>
  );

  const renderFilterChips = (): React.ReactElement => {
    const appBarHeight = insets.top + 56; // safe area + minHeight

    return (
      <View
        style={[
          styles.filterContainer,
          {
            backgroundColor: theme.colors.background,
            top: appBarHeight,
          },
        ]}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterScrollContent}
        >
          {filters.map(filter => (
            <Chip
              key={filter}
              selected={selectedFilter === filter}
              onPress={() => setSelectedFilter(filter)}
              style={[
                styles.filterChip,
                selectedFilter === filter && { backgroundColor: theme.colors.primary },
              ]}
              textStyle={{
                color: selectedFilter === filter ? theme.colors.onPrimary : theme.colors.onSurface,
              }}
            >
              {filter}
            </Chip>
          ))}
        </ScrollView>

        <TouchableOpacity
          onPress={() => {
            /* TODO: Open filter/sort sheet */
          }}
          style={styles.filter}
        >
          <PhosphorIcon name='filter' size={20} color={theme.colors.onSurface} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderStatsCards = (): React.ReactElement => (
    <StatCardGroup
      cards={staffStats as StatCard[]}
      columns={2}
      showTitle={false}
      containerStyle={styles.statsContainer}
    />
  );

  const handleBackPress = (): void => {
    navigation.goBack();
  };

  const handleSearchPress = (): void => {
    // TODO: Implement search functionality
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Staff Management'
        onBackPress={handleBackPress}
        showBack={true}
        actions={[
          {
            icon: 'magnifying-glass',
            onPress: handleSearchPress,
          },
        ]}
      />

      {renderFilterChips()}

      <FlatList
        data={filteredStaff}
        renderItem={renderStaffCard}
        keyExtractor={item => item.id}
        contentContainerStyle={[
          styles.listContainer,
          {
            paddingBottom: insets.bottom + 80,
            paddingTop: 48, // Height of filter chips container
          },
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={renderStatsCards}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon='plus'
        style={[styles.fab, { backgroundColor: theme.colors.secondary }]}
        onPress={handleAddStaff}
      />

      {/* Confirmation Dialog */}
      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsContainer: {
    paddingVertical: 8,
  },
  listContainer: {
    padding: 16,
  },
  staffCard: {
    marginBottom: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  staffInfo: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 8,
    minWidth: 0, // Prevents flex overflow
  },
  avatar: {
    marginRight: 8,
  },
  staffDetails: {
    flex: 1,
    minWidth: 0, // Prevents flex overflow
  },
  staffName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  availabilityChip: {
    alignSelf: 'flex-start',
    flexShrink: 0, // Prevents chip from shrinking
  },
  performanceSection: {
    marginBottom: 8,
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
    minWidth: 0, // Prevents flex overflow
    maxWidth: '100%', // Ensures row doesn't overflow
  },
  performanceLabel: {
    fontSize: 12,
    fontWeight: '500',
    flexShrink: 0, // Prevents label from shrinking
  },
  performanceValue: {
    fontSize: 12,
    fontWeight: '600',
    flexShrink: 0, // Prevents value from shrinking
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 0, // Prevents container from shrinking
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  workloadContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
    minWidth: 0, // Prevents flex overflow
    maxWidth: '100%', // Ensures container doesn't overflow
    overflow: 'hidden', // Prevents content from spilling out
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
    minWidth: 0, // Prevents flex overflow
    maxWidth: '100%', // Ensures progress bar doesn't overflow
  },
  workloadText: {
    fontSize: 9,
    minWidth: 25,
    maxWidth: 35,
    flexShrink: 0, // Prevents text from shrinking
    textAlign: 'right',
    overflow: 'hidden', // Prevents text overflow
  },
  skillsSection: {
    marginBottom: 8,
  },
  skillsLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    alignItems: 'flex-start',
  },
  skillChip: {
    marginBottom: 2,
    flexShrink: 0, // Prevents chips from shrinking
  },
  moreSkillsChip: {
    backgroundColor: '#f3f4f6',
    flexShrink: 0, // Prevents chip from shrinking
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 6,
    marginTop: 6,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
    minWidth: 0, // Prevents flex overflow
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  statLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'transparent',
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  filterScrollContent: {
    paddingRight: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  filter: {
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default StaffManagementScreen;
