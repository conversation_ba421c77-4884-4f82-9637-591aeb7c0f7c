import { BottomSheetBackdrop, BottomSheetBackdropProps, BottomSheetModal } from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Keyboard, ScrollView, StyleSheet, View } from 'react-native';
import { Card, Chip, Divider, IconButton, Portal, Text, TextInput } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { FINANCIAL_CONFIG } from '../../config/constants';
import { useFinancial } from '../../context/FinancialContext';
import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import ActionSheet from '../ui/ActionSheet';
import Button from '../ui/Button';

// --- Constants and Helpers ---
const INITIAL_RECONCILIATION_STATE = {
  date: new Date().toISOString().split('T')[0],
  actualCash: '',
  notes: '',
  performedBy: 'Manager',
};

const validateNumber = (val: string): boolean => !isNaN(Number(val)) && Number(val) >= 0;

const formatCurrency = (amount: number = 0): string => {
  return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(
    FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES
  )}`;
};

export interface CashReconciliationBottomSheetRef {
  present: () => void;
  close: () => void;
}
// --- Component ---
const CashReconciliationBottomSheet = forwardRef<CashReconciliationBottomSheetRef, any>((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { performReconciliation, calculateDailyCashExpected } = useFinancial();
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const [reconciliation, setReconciliation] = useState(INITIAL_RECONCILIATION_STATE);
  const [expectedCashData, setExpectedCashData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [calculatingExpected, setCalculatingExpected] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [actionSheet, setActionSheet] = useState({ visible: false, title: '', description: '' });

  const snapPoints = useMemo(() => ['90%'], []);

  const loadExpectedCash = useCallback(async () => {
    setCalculatingExpected(true);
    setExpectedCashData(null); // Reset on each load
    try {
      const data = await calculateDailyCashExpected(reconciliation.date);
      setExpectedCashData(data);
    } catch (error) {
      console.error('Failed to calculate expected cash:', error);
      setActionSheet({
        visible: true,
        title: 'Calculation Error',
        description: 'Could not calculate the expected cash amount. Please try again later.',
      });
    } finally {
      setCalculatingExpected(false);
    }
  }, [reconciliation.date, calculateDailyCashExpected]);

  useEffect(() => {
    if (bottomSheetRef.current) {
      loadExpectedCash();
    }
  }, [reconciliation.date, loadExpectedCash]);

  useImperativeHandle(
    ref,
    () => ({
      present: () => {
        console.log('present called on CashReconciliationBottomSheet');
        setReconciliation(INITIAL_RECONCILIATION_STATE);
        setErrors({});
        bottomSheetRef.current?.present();
      },
      close: () => bottomSheetRef.current?.close(),
    }),
    []
  );

  const handleInputChange = (field: keyof typeof reconciliation, value: string) => {
    setReconciliation(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const difference = useMemo(() => {
    const actual = parseFloat(reconciliation.actualCash) || 0;
    const expected = expectedCashData?.expectedClosingCash || 0;
    return actual - expected;
  }, [reconciliation.actualCash, expectedCashData]);

  const differenceStatus = useMemo(() => {
    const isBalanced = Math.abs(difference) <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE;
    return isBalanced
      ? { status: 'balanced', color: '#4CAF50', icon: 'check-circle' }
      : { status: 'discrepancy', color: '#F44336', icon: 'alert-circle' };
  }, [difference]);

  const getStatusInfo = useMemo(() => {
    if (!reconciliation.actualCash || !expectedCashData) {
      return 'Awaiting actual cash amount';
    }
    const diffText = formatCurrency(Math.abs(difference));
    return `${
      differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy'
    } • ${diffText}`;
  }, [reconciliation.actualCash, expectedCashData, difference, differenceStatus.status]);

  const handlePerformReconciliation = useCallback(async () => {
    Keyboard.dismiss();
    const newErrors: Record<string, string> = {};
    if (!validateNumber(reconciliation.actualCash)) {
      newErrors.actualCash = 'Please enter a valid cash amount';
    }
    if (!reconciliation.performedBy.trim()) {
      newErrors.performedBy = 'Name is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    try {
      // FIX 1: Add `difference` and `status` to the object being passed
      const reconciliationData = {
        ...reconciliation,
        actualCash: parseFloat(reconciliation.actualCash),
        expectedCash: expectedCashData?.expectedClosingCash || 0,
        difference: difference,
        status: differenceStatus.status,
      };

      const result = await performReconciliation(reconciliationData);
      const diff = Math.abs(result.difference);
      const isBalanced = diff <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE;
      const message = isBalanced
        ? `Reconciliation successful! The difference of ${formatCurrency(diff)} is within tolerance.`
        : `Discrepancy detected! The difference is ${formatCurrency(diff)}. Please review and investigate.`;

      setActionSheet({ visible: true, title: 'Reconciliation Complete', description: message });
    } catch (error: any) {
      setActionSheet({
        visible: true,
        title: 'Error',
        description: error.message || 'Failed to perform reconciliation.',
      });
    } finally {
      setLoading(false);
    }
  }, [reconciliation, expectedCashData, performReconciliation, difference, differenceStatus]);

  // FIX 2: Add `BottomSheetBackdropProps` type to props
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />
    ),
    []
  );

  return (
    <Portal>
      <BottomSheetModal
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        enablePanDownToClose
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outlineVariant }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.tertiary}20` }]}>
                <PhosphorIcon name="cash-register" size={24} color={theme.colors.tertiary} />
              </View>
              <View>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Cash Reconciliation
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {new Date(reconciliation.date).toLocaleDateString()} • {getStatusInfo}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={24}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Expected Cash Card */}
            <Card style={[styles.card, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Card.Content>
                <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                  Expected Cash Calculation
                </Text>
                {calculatingExpected ? (
                  <Text style={styles.infoText}>Calculating...</Text>
                ) : expectedCashData ? (
                  <View style={styles.breakdownContainer}>
                    <View style={styles.breakdownRow}>
                      <Text>Opening Cash</Text>
                      <Text>{formatCurrency(expectedCashData.openingCash)}</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Cash Sales ({expectedCashData.orderCount} orders)</Text>
                      <Text style={{ color: '#4CAF50' }}>+{formatCurrency(expectedCashData.cashSales)}</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Cash Expenses</Text>
                      <Text style={{ color: '#F44336' }}>-{formatCurrency(expectedCashData.cashExpenses)}</Text>
                    </View>
                    <Divider style={styles.divider} />
                    <View style={styles.breakdownRow}>
                      <Text variant="titleMedium" style={{ fontWeight: 'bold' }}>Expected Closing Cash</Text>
                      <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: 'bold' }}>
                        {formatCurrency(expectedCashData.expectedClosingCash)}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <Text style={[styles.infoText, { color: theme.colors.error }]}>
                    Could not calculate expected cash.
                  </Text>
                )}
              </Card.Content>
            </Card>

            {/* Inputs & Difference Card */}
            <View style={styles.formSection}>
              <TextInput
                label="Actual Cash Count *"
                value={reconciliation.actualCash}
                onChangeText={text => handleInputChange('actualCash', text)}
                keyboardType="numeric"
                error={!!errors.actualCash}
                placeholder="0.00"
              />
              {!!errors.actualCash && <Text style={styles.errorText}>{errors.actualCash}</Text>}

              {reconciliation.actualCash && expectedCashData && (
                <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
                  <Card.Content>
                    <View style={styles.differenceHeader}>
                      <PhosphorIcon
                        name={differenceStatus.icon as any}
                        size={24}
                        color={differenceStatus.color}
                      />
                      <Text variant="titleMedium" style={{ marginLeft: 8 }}>Reconciliation Status</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Difference</Text>
                      <Text variant="titleMedium" style={{ color: differenceStatus.color, fontWeight: 'bold' }}>
                        {difference >= 0 ? '+' : ''}{formatCurrency(difference)}
                      </Text>
                    </View>
                    <Chip
                      icon={differenceStatus.icon as any}
                      style={[styles.statusChip, { backgroundColor: `${differenceStatus.color}20` }]}
                      textStyle={{ color: differenceStatus.color }}
                    >
                      {differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy'}
                    </Chip>
                  </Card.Content>
                </Card>
              )}

              <TextInput
                label="Performed By *"
                value={reconciliation.performedBy}
                onChangeText={text => handleInputChange('performedBy', text)}
                error={!!errors.performedBy}
                placeholder="Manager name"
              />
              {!!errors.performedBy && <Text style={styles.errorText}>{errors.performedBy}</Text>}

              <TextInput
                label="Notes (Optional)"
                value={reconciliation.notes}
                onChangeText={text => handleInputChange('notes', text)}
                multiline
                numberOfLines={3}
                placeholder="Note any discrepancies..."
              />
            </View>
            <View style={{ height: insets.bottom + 80 }} />
          </ScrollView>

          {/* Footer */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outlineVariant }]}>
            <Button
              variant="outline"
              onPress={() => bottomSheetRef.current?.close()}
              style={styles.button}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onPress={handlePerformReconciliation}
              style={styles.button}
              loading={loading}
              disabled={loading || !expectedCashData || calculatingExpected}
              icon="check"
            >
              Complete
            </Button>
          </View>
        </View>
      </BottomSheetModal>

      <ActionSheet
        visible={actionSheet.visible}
        onDismiss={() => setActionSheet({ ...actionSheet, visible: false })}
        title={actionSheet.title}
        description={actionSheet.description}
        options={[{ text: 'OK', onPress: () => setActionSheet({ ...actionSheet, visible: false }) }]}
      />
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: { elevation: 8, zIndex: 1000 },
  container: { flex: 1 },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: StyleSheet.hairlineWidth },
  headerLeft: { flex: 1, flexDirection: 'row', alignItems: 'center', gap: 12 },
  iconContainer: { width: 40, height: 40, borderRadius: 12, justifyContent: 'center', alignItems: 'center' },
  title: { fontWeight: '700' },
  subtitle: { marginTop: 2 },
  content: { flex: 1, paddingHorizontal: 16, paddingTop: 16 },
  card: { marginBottom: 16, borderRadius: 12 },
  infoText: { paddingTop: 16 },
  breakdownContainer: { gap: 8, marginTop: 16 },
  breakdownRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 4 },
  divider: { marginVertical: 8 },
  formSection: { paddingBottom: 32, gap: 16 },
  differenceHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  statusChip: { alignSelf: 'flex-start', marginTop: 8 },
  footer: { paddingHorizontal: 16, paddingTop: 12, borderTopWidth: StyleSheet.hairlineWidth, flexDirection: 'row', gap: 12 },
  button: { flex: 1 },
  errorText: { color: 'red', marginTop: -12, fontSize: 12, paddingHorizontal: 4 },
});

export default CashReconciliationBottomSheet;