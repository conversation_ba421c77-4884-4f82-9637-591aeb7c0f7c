import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { ToastProvider } from '../context/ToastContext';
import LoggingService from '../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../theme/theme';
import AppNavigator from './AppNavigator';
import AuthNavigator from './AuthNavigator';

const RootNavigator: React.FC = () => {
  const { state } = useAuth();
  const theme = useTheme();

  // Debug logging
  LoggingService.debug(
    `RootNavigator state: isLoading=${state.isLoading}, isAuthenticated=${state.isAuthenticated}, user=${state.user?.username || 'null'}`,
    'NAV'
  );

  // Show loading screen while checking authentication
  if (state.isLoading) {
    LoggingService.debug('Showing authentication loading screen', 'NAV');

    return (
      <ToastProvider>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: theme.colors.background,
            gap: SPACING.lg,
          }}
        >
          <ActivityIndicator size='large' color={theme.colors.primary} />
          <Text
            style={{
              fontSize: TYPOGRAPHY.fontSize.lg,
              color: theme.colors.onSurface,
              fontWeight: TYPOGRAPHY.fontWeight.medium,
            }}
          >
            Checking authentication...
          </Text>
        </View>
      </ToastProvider>
    );
  }

  // Show appropriate navigator based on authentication state
  if (state.isAuthenticated && state.user) {
    LoggingService.debug(`User authenticated: ${state.user.username} (${state.user.role})`, 'NAV');
    return (
      <ToastProvider>
        <AppNavigator />
      </ToastProvider>
    );
  } else {
    LoggingService.debug('User not authenticated, showing auth flow', 'NAV');
    return (
      <ToastProvider>
        <AuthNavigator />
      </ToastProvider>
    );
  }
};

export default RootNavigator;
