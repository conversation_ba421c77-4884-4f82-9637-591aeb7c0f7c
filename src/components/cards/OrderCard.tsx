import React, { useMemo, useCallback, memo } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '@/context/ThemeContext';
import { Order } from '@/types'; // Assuming your Order type is defined here
import { formatCurrency } from '@/utils/currency';

// --- Constants & Types ---
type Theme = ReturnType<typeof useTheme>;

const statusColors: { [key: string]: { bg: string; text: string } } = {
  PENDING: { bg: '#a16207', text: '#fff' }, // Amber-700
  READY: { bg: '#15803d', text: '#fff' }, // Green-700
  CANCELLED: { bg: '#991b1b', text: '#fff' }, // Red-800
  'IN PROGRESS': { bg: '#1e40af', text: '#fff' }, // Blue-800
  CONFIRMED: { bg: '#1e40af', text: '#fff' }, // Blue-800
  PAID: { bg: '#059669', text: '#fff' }, // Emerald-600
  DELIVERED: { bg: '#059669', text: '#fff' }, // Emerald-600
  COMPLETED: { bg: '#059669', text: '#fff' }, // Emerald-600
};

const PAID_STATUSES = ['PAID', 'DELIVERED', 'READY', 'COMPLETED'];

// --- Custom Hook for Business Logic ---

const useOrderDetails = (order: Order, theme: Theme) => {
  return useMemo(() => {
    const status = (order.status || 'PENDING').toUpperCase().replace('-', ' ');
    const isPaid = PAID_STATUSES.includes(status);
    const totalAmount = order.total || order.totalAmount || 0;
    
    // 👇 FIX: Use `subtotal` instead of `amountPaid` and ensure due is 0 if paid.
    const amountPaid = order.subtotal || 0;
    const dueAmount = isPaid ? 0 : totalAmount - amountPaid;

    return {
      status,
      statusColor: statusColors[status] || { bg: theme.colors.primary, text: theme.colors.onPrimary },
      itemCount: order.items?.length || 0,
      isPaid,
      dueAmount,
      totalAmount,
      formattedDate: order.date ? new Date(order.date).toLocaleDateString() : 'No date',
      customerName: order.customerName || order.customer || 'Walk-in Customer',
    };
  }, [order, theme]);
};

// --- Main Component ---

const OrderCard: React.FC<{ order: Order; onPress: (order: Order) => void; }> = ({ order, onPress }) => {
  const theme = useTheme();
  const styles = useMemo(() => createCardStyles(theme), [theme]);
  const details = useOrderDetails(order, theme);

  const handlePress = useCallback(() => onPress(order), [onPress, order]);

  return (
    <TouchableOpacity style={styles.card} activeOpacity={0.85} onPress={handlePress}>
      <View style={styles.headerRow}>
        <Text style={styles.orderNumber}>#{order.id}</Text>
        <View style={[styles.statusBadge, { backgroundColor: details.statusColor.bg }]}>
          <Text style={[styles.statusText, { color: details.statusColor.text }]}>
            {details.status}
          </Text>
        </View>
      </View>

      <Text style={styles.itemsLink}>{details.itemCount} Item{details.itemCount !== 1 ? 's' : ''}</Text>
      <Text style={styles.customerName}>{details.customerName}</Text>

      <View style={styles.footerRow}>
        <Text style={styles.dateText}>Due {details.formattedDate}</Text>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>{formatCurrency(details.totalAmount)}</Text>
          {details.dueAmount <= 0 ? (
            <Text style={styles.paidText}>Paid</Text>
          ) : (
            <Text style={styles.dueText}>
              Due {formatCurrency(details.dueAmount)}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

// --- Styles ---

const createCardStyles = (theme: Theme) => StyleSheet.create({
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.outlineVariant,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  orderNumber: {
    color: theme.colors.onSurface,
    fontWeight: 'bold',
    fontSize: 16,
  },
  statusBadge: {
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  statusText: {
    fontWeight: 'bold',
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  itemsLink: {
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: 14,
    marginBottom: 2,
  },
  customerName: {
    color: theme.colors.onSurface,
    fontSize: 14,
    marginBottom: 8,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  dateText: {
    color: theme.colors.onSurfaceVariant,
    fontSize: 12,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    color: theme.colors.onSurface,
    fontWeight: 'bold',
    fontSize: 16,
  },
  dueText: {
    color: theme.colors.error, // Theme-aware color
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 2,
  },
  paidText: {
    color: theme.colors.success, // Theme-aware color
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 2,
  },
});

export default memo(OrderCard);