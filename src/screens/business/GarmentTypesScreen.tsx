import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, ViewStyle } from 'react-native';
import { Text } from 'react-native-paper';

import Header from '@/components/navigation/Header';
import UIOrganicCard from '@/components/ui/Card';
import EmptyState from '@/components/ui/EmptyState';
import ActionSheet from '@/components/ui/ActionSheet';
import { ActionSheetOption } from '@/types';
import { useTheme } from '@/context/ThemeContext';
import GarmentTypeService, { GarmentTemplate } from '@/services/GarmentTypeService';
import LoggingService from '@/services/LoggingService';
import { formatCurrency } from '@/utils/currency';

const GarmentTypesScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [items, setItems] = useState<GarmentTemplate[]>([]);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const load = useCallback(async () => {
    await GarmentTypeService.seedIfEmpty();
    const list = await GarmentTypeService.list();
    setItems(list);
  }, []);

  useEffect(() => {
    void load();
  }, [load]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await load();
    } finally {
      setRefreshing(false);
    }
  }, [load]);

  const filtered = useMemo(() => {
    const q = searchQuery.trim().toLowerCase();
    if (!q) return items;
    return items.filter(
      i =>
        i.name.toLowerCase().includes(q) ||
        (Array.isArray(i.measurementFields) &&
          i.measurementFields.join(' ').toLowerCase().includes(q))
    );
  }, [searchQuery, items]);

  const handleSearch = useCallback((q: string) => setSearchQuery(q), []);

  const handleAdd = useCallback(() => {
    navigation.navigate('GarmentTypeDetail');
  }, [navigation]);

  const handlePress = useCallback((item: GarmentTemplate) => {
    navigation.navigate('GarmentTypeDetail', { id: item.id });
  }, [navigation]);

  const renderItem = useCallback(
    ({ item }: { item: GarmentTemplate }) => {
      const rightPrice = formatCurrency(item.price, { decimals: 0 });
      return (
        <UIOrganicCard
          title={item.name}
          subtitle={`Measurements: ${item.measurementFields?.length || 0}`}
          description={undefined}
          price={undefined}
          image={undefined}
          icon={undefined}
          iconColor={undefined}
          iconBackgroundColor={undefined}
          // Show price on the right in the title row
          status={rightPrice}
          statusColor={theme.colors.onSurface}
          statusBackgroundColor='transparent'
          badge={undefined}
          badgeColor={undefined}
          onPress={() => handlePress(item)}
          onLongPress={undefined}
          actions={[]}
          primaryAction={undefined}
          secondaryAction={undefined}
          menuItems={[]}
          menuVisible={false}
          onMenuToggle={undefined}
          onMenuDismiss={undefined}
          style={StyleSheet.flatten([styles.card, { borderWidth: 0, borderColor: 'transparent' }])}
          contentStyle={undefined}
          disabled={false}
          showImage={false}
          showIcon={false}
          showActions={true}
          layout='compact'
        />
      );
    },
    [handlePress, theme.colors.onSurface]
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Garment Types'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchData={items}
        searchFields={['name']}
        searchPlaceholder='Search garment types...'
        searchType='products'
        onSearch={handleSearch}
        actions={[{ icon: 'plus', onPress: handleAdd }]}
        
      />

      <FlatList
        data={filtered}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        style={styles.list}
        contentContainerStyle={filtered.length === 0 ? styles.listEmpty : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={() => (
          <EmptyState
            type='custom'
            icon='tshirt'
            title='No Garment Types Found'
            searchQuery={searchQuery}
            onActionPress={handleAdd}
            description='No garment types found'
            actionLabel='Add Type'
            style={{}}
            iconColor={theme.colors.primary}
          />
        )}
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  list: { flex: 1, paddingHorizontal: 16, paddingTop: 8 },
  listEmpty: { flexGrow: 1, justifyContent: 'center' },
  card: { marginBottom: 12 },
});

export default GarmentTypesScreen;
