/**
 * Inventory Card Component
 * Displays inventory item information in a consistent card format
 */

import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle, Image } from 'react-native';
import { Text, Card, Chip, IconButton } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { InventoryItem } from '../../types/inventory';
import { formatCurrency } from '../../utils/currency';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

import StockLevelIndicator from './StockLevelIndicator';

interface InventoryCardProps {
  item: InventoryItem;
  stockLevel?: number;
  stockUnit?: string;
  onPress?: () => void;
  showStockLevel?: boolean;
  showActions?: boolean;
  showImage?: boolean;
  showPrices?: boolean;
  compact?: boolean;
  style?: ViewStyle;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onStockPress?: () => void;
}

const InventoryCard: React.FC<InventoryCardProps> = ({
  item,
  stockLevel = 0,
  stockUnit,
  onPress,
  showStockLevel = true,
  showActions = false,
  showImage = true,
  showPrices = true,
  compact = false,
  style,
  onEditPress,
  onDeletePress,
  onStockPress,
}) => {
  const theme = useTheme();

  // Early return if item is not provided
  if (!item) {
    return null;
  }

  // Determine stock unit (use item's base unit if not provided)
  const displayStockUnit = stockUnit || item.baseUnit;

  // Calculate stock status
  const stockStatus = useMemo(() => {
    if (stockLevel <= 0) return 'out-of-stock';
    if (stockLevel <= item.minimumStockLevel) return 'low-stock';
    if (stockLevel <= item.minimumStockLevel * 2) return 'adequate';
    return 'good-stock';
  }, [stockLevel, item.minimumStockLevel]);

  // Get stock status color
  const stockStatusColor = useMemo(() => {
    switch (stockStatus) {
      case 'out-of-stock':
        return theme.colors.error;
      case 'low-stock':
        return '#F59E0B'; // Amber
      case 'adequate':
        return theme.colors.primary;
      case 'good-stock':
        return '#10B981'; // Green
      default:
        return theme.colors.onSurfaceVariant;
    }
  }, [stockStatus, theme.colors]);

  // Format prices
  const formattedPurchasePrice = formatCurrency(item.purchasePrice);
  const formattedSellingPrice = formatCurrency(item.sellingPrice);
  const profitMargin =
    item.sellingPrice > 0
      ? (((item.sellingPrice - item.purchasePrice) / item.sellingPrice) * 100).toFixed(1)
      : '0';

  // Format stock level
  const formattedStockLevel = UnitConverter.formatQuantity(stockLevel, displayStockUnit, 2);

  // Get category color
  const getCategoryColor = (category: string): string => {
    const colors = [
      theme.colors.primary,
      theme.colors.secondary,
      theme.colors.tertiary,
      '#F59E0B', // Amber
      '#10B981', // Green
      '#8B5CF6', // Purple
      '#EF4444', // Red
      '#06B6D4', // Cyan
    ];

    let hash = 0;
    const categoryStr = category || 'default';
    for (let i = 0; i < categoryStr.length; i++) {
      hash = categoryStr.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  const handleCardPress = () => {
    if (onPress) {
      onPress();
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactContainer, style]}
        onPress={handleCardPress}
        activeOpacity={0.7}
      >
        <View style={styles.compactContent}>
          <View style={styles.compactInfo}>
            <Text
              style={[styles.compactTitle, { color: theme.colors.onSurface }]}
              numberOfLines={1}
            >
              {item.name}
            </Text>

            <Text
              style={[styles.compactCategory, { color: theme.colors.onSurfaceVariant }]}
              numberOfLines={1}
            >
              {item.category}
            </Text>
          </View>

          {showStockLevel && (
            <View style={styles.compactStock}>
              <Text style={[styles.compactStockText, { color: stockStatusColor }]}>
                {formattedStockLevel}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <Card style={[styles.container, style]} mode='outlined'>
      <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
        <Card.Content style={styles.content}>
          {/* Header Row */}
          <View style={styles.headerRow}>
            <View style={styles.titleContainer}>
              <Text style={[styles.title, { color: theme.colors.onSurface }]} numberOfLines={2}>
                {item.name}
              </Text>

              <View style={styles.categoryContainer}>
                <Chip
                  mode='outlined'
                  compact
                  style={[styles.categoryChip, { borderColor: getCategoryColor(item.category) }]}
                  textStyle={{
                    color: getCategoryColor(item.category),
                    fontSize: 11,
                  }}
                >
                  {item.category}
                </Chip>
              </View>
            </View>

            {/* Item Image */}
            {showImage && (
              <View style={styles.imageContainer}>
                {item.imageUri ? (
                  <Image
                    source={{ uri: item.imageUri }}
                    style={styles.itemImage}
                    resizeMode='cover'
                  />
                ) : (
                  <View
                    style={[
                      styles.placeholderImage,
                      { backgroundColor: theme.colors.surfaceVariant },
                    ]}
                  >
                    <PhosphorIcon name='package' size={24} color={theme.colors.onSurfaceVariant} />
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Stock Level Indicator */}
          {showStockLevel && (
            <View style={styles.stockContainer}>
              <StockLevelIndicator
                currentStock={stockLevel}
                minimumLevel={item.minimumStockLevel}
                unit={displayStockUnit}
                size='small'
                showProgressBar={false}
                showText={true}
              />
            </View>
          )}

          {/* Price Information */}
          {showPrices && (
            <View style={styles.priceContainer}>
              <View style={styles.priceRow}>
                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Purchase
                  </Text>
                  <Text style={[styles.priceValue, { color: theme.colors.onSurface }]}>
                    {formattedPurchasePrice}
                  </Text>
                </View>

                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Selling
                  </Text>
                  <Text style={[styles.priceValue, { color: theme.colors.primary }]}>
                    {formattedSellingPrice}
                  </Text>
                </View>

                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Margin
                  </Text>
                  <Text
                    style={[
                      styles.priceValue,
                      {
                        color:
                          parseFloat(profitMargin) > 0 ? theme.colors.primary : theme.colors.error,
                      },
                    ]}
                  >
                    {profitMargin}%
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Base Unit Information */}
          <View style={styles.unitContainer}>
            <PhosphorIcon name='ruler' size={14} color={theme.colors.onSurfaceVariant} />
            <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
              Base unit: {UnitConverter.getUnitDisplayName(item.baseUnit)}
            </Text>
          </View>

          {/* Available Units */}
          {item.availableUnits && item.availableUnits.length > 1 && (
            <View style={styles.availableUnitsContainer}>
              <Text style={[styles.availableUnitsLabel, { color: theme.colors.onSurfaceVariant }]}>
                Available units:
              </Text>
              <View style={styles.unitsRow}>
                {(item.availableUnits || []).slice(0, 4).map((unit, index) => (
                  <Chip
                    key={unit}
                    mode='outlined'
                    compact
                    style={styles.unitChip}
                    textStyle={styles.unitChipText}
                  >
                    {UnitConverter.getUnitAbbreviation(unit)}
                  </Chip>
                ))}
                {(item.availableUnits || []).length > 4 && (
                  <Text style={[styles.moreUnitsText, { color: theme.colors.onSurfaceVariant }]}>
                    +{(item.availableUnits || []).length - 4} more
                  </Text>
                )}
              </View>
            </View>
          )}

          {/* Actions Row */}
          {showActions && (
            <View style={styles.actionsRow}>
              {onStockPress && (
                <IconButton
                  icon='package-variant'
                  size={20}
                  onPress={onStockPress}
                  iconColor={theme.colors.primary}
                  style={styles.actionButton}
                />
              )}

              {onEditPress && (
                <IconButton
                  icon='pencil'
                  size={20}
                  onPress={onEditPress}
                  iconColor={theme.colors.onSurfaceVariant}
                  style={styles.actionButton}
                />
              )}

              {onDeletePress && (
                <IconButton
                  icon='delete'
                  size={20}
                  onPress={onDeletePress}
                  iconColor={theme.colors.error}
                  style={styles.actionButton}
                />
              )}
            </View>
          )}

          {/* Inactive Indicator */}
          {!item.isActive && (
            <View style={styles.inactiveOverlay}>
              <Text style={[styles.inactiveText, { color: theme.colors.error }]}>Inactive</Text>
            </View>
          )}
        </Card.Content>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    marginHorizontal: 8,
  },
  content: {
    paddingVertical: 12,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  categoryContainer: {
    marginTop: 4,
  },
  categoryChip: {
    alignSelf: 'flex-start',
  },
  imageContainer: {
    width: 60,
    height: 60,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stockContainer: {
    marginBottom: 12,
  },
  priceContainer: {
    marginBottom: 12,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceItem: {
    flex: 1,
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 11,
    marginBottom: 2,
  },
  priceValue: {
    fontSize: 13,
    fontWeight: '600',
  },
  unitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  unitText: {
    fontSize: 12,
  },
  availableUnitsContainer: {
    marginBottom: 12,
  },
  availableUnitsLabel: {
    fontSize: 11,
    marginBottom: 4,
  },
  unitsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: 4,
  },
  unitChip: {
    height: 24,
  },
  unitChipText: {
    fontSize: 10,
  },
  moreUnitsText: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    gap: 4,
  },
  actionButton: {
    margin: 0,
  },
  inactiveOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  inactiveText: {
    fontSize: 10,
    fontWeight: '600',
  },
  // Compact styles
  compactContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactInfo: {
    flex: 1,
  },
  compactTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  compactCategory: {
    fontSize: 12,
  },
  compactStock: {
    alignItems: 'flex-end',
  },
  compactStockText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default InventoryCard;
