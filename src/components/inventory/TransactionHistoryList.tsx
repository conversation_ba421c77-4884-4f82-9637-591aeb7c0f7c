/**
 * Transaction History List Component
 * Displays a list of inventory transactions with filtering and details
 */

import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Card, Chip, Divider } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { InventoryTransaction } from '../../types/inventory';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

interface TransactionHistoryListProps {
  transactions: InventoryTransaction[];
  showItemName?: boolean;
  showWarehouseName?: boolean;
  showPerformedBy?: boolean;
  compact?: boolean;
  maxItems?: number;
  style?: ViewStyle;
  emptyMessage?: string;
  onTransactionPress?: (transaction: InventoryTransaction) => void;
}

const TransactionHistoryList: React.FC<TransactionHistoryListProps> = ({
  transactions,
  showItemName = true,
  showWarehouseName = true,
  showPerformedBy = true,
  compact = false,
  maxItems,
  style,
  emptyMessage = 'No transactions found',
  onTransactionPress,
}) => {
  const theme = useTheme();

  // Limit transactions if maxItems is specified
  const displayTransactions = useMemo(() => {
    return maxItems ? transactions.slice(0, maxItems) : transactions;
  }, [transactions, maxItems]);

  // Get transaction type icon and color
  const getTransactionTypeInfo = (type: InventoryTransaction['type']) => {
    switch (type) {
      case 'IN':
        return {
          icon: 'arrow-down',
          color: theme.colors.primary, // Use primary color for stock in
          label: 'Stock In',
          backgroundColor: `${theme.colors.primary}15`,
        };
      case 'OUT':
        return {
          icon: 'arrow-up',
          color: theme.colors.error, // Use error color for stock out
          label: 'Stock Out',
          backgroundColor: `${theme.colors.error}15`,
        };
      case 'TRANSFER':
        return {
          icon: 'arrows-left-right',
          color: theme.colors.tertiary, // Use tertiary color for transfer
          label: 'Transfer',
          backgroundColor: `${theme.colors.tertiary}15`,
        };
      case 'ADJUSTMENT':
        return {
          icon: 'wrench',
          color: theme.colors.primary,
          label: 'Adjustment',
          backgroundColor: `${theme.colors.primary}15`,
        };
      default:
        return {
          icon: 'question',
          color: theme.colors.onSurfaceVariant,
          label: 'Unknown',
          backgroundColor: `${theme.colors.onSurfaceVariant}15`,
        };
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string): { date: string; time: string } => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let dateLabel: string;
    if (date.toDateString() === today.toDateString()) {
      dateLabel = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateLabel = 'Yesterday';
    } else {
      dateLabel = date.toLocaleDateString();
    }

    const timeLabel = date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });

    return { date: dateLabel, time: timeLabel };
  };

  // Render transaction item
  const renderTransactionItem = ({ item: transaction }: { item: InventoryTransaction }) => {
    const typeInfo = getTransactionTypeInfo(transaction.type);
    const { date, time } = formatDateTime(transaction.createdAt);
    const formattedQuantity = UnitConverter.formatQuantity(
      Math.abs(transaction.quantity),
      transaction.unit,
      2
    );

    if (compact) {
      return (
        <View style={[styles.compactItem, { borderLeftColor: typeInfo.color }]}>
          <View style={styles.compactHeader}>
            <View style={styles.compactTypeContainer}>
              <PhosphorIcon name={typeInfo.icon as any} size={16} color={typeInfo.color} />
              <Text style={[styles.compactTypeText, { color: typeInfo.color }]}>
                {typeInfo.label}
              </Text>
            </View>

            <Text
              style={[
                styles.compactQuantity,
                {
                  color: transaction.quantity >= 0 ? theme.colors.primary : theme.colors.error,
                },
              ]}
            >
              {transaction.quantity >= 0 ? '+' : ''}
              {formattedQuantity}
            </Text>
          </View>

          <View style={styles.compactDetails}>
            <Text style={[styles.compactTime, { color: theme.colors.onSurfaceVariant }]}>
              {date} at {time}
            </Text>

            {transaction.note && (
              <Text
                style={[styles.compactNote, { color: theme.colors.onSurfaceVariant }]}
                numberOfLines={1}
              >
                {transaction.note}
              </Text>
            )}
          </View>
        </View>
      );
    }

    return (
      <Card
        style={styles.transactionCard}
        mode='outlined'
        onPress={() => onTransactionPress?.(transaction)}
      >
        <Card.Content style={styles.cardContent}>
          {/* Header Row */}
          <View style={styles.headerRow}>
            <View style={[styles.typeIndicator, { backgroundColor: typeInfo.backgroundColor }]}>
              <PhosphorIcon name={typeInfo.icon as any} size={20} color={typeInfo.color} />
              <Text style={[styles.typeLabel, { color: typeInfo.color }]}>{typeInfo.label}</Text>
            </View>

            <View style={styles.quantityContainer}>
              <Text
                style={[
                  styles.quantityText,
                  {
                    color: transaction.quantity >= 0 ? theme.colors.primary : theme.colors.error,
                  },
                ]}
              >
                {transaction.quantity >= 0 ? '+' : ''}
                {formattedQuantity}
              </Text>
            </View>
          </View>

          {/* Details Row */}
          <View style={styles.detailsRow}>
            <View style={styles.detailsLeft}>
              {showItemName && (transaction as any).item_name && (
                <View style={styles.detailItem}>
                  <PhosphorIcon name='package' size={14} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.detailText, { color: theme.colors.onSurface }]}>
                    {(transaction as any).item_name}
                  </Text>
                </View>
              )}

              {showWarehouseName && (transaction as any).warehouse_name && (
                <View style={styles.detailItem}>
                  <PhosphorIcon name='warehouse' size={14} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.detailText, { color: theme.colors.onSurface }]}>
                    {(transaction as any).warehouse_name}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.detailsRight}>
              <Text style={[styles.dateText, { color: theme.colors.onSurfaceVariant }]}>
                {date}
              </Text>
              <Text style={[styles.timeText, { color: theme.colors.onSurfaceVariant }]}>
                {time}
              </Text>
            </View>
          </View>

          {/* Reference and Note */}
          {(transaction.reference || transaction.note) && (
            <View style={styles.additionalInfo}>
              <Divider style={{ marginVertical: 8 }} />

              {transaction.reference && (
                <View style={styles.infoRow}>
                  <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Reference:
                  </Text>
                  <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                    {transaction.reference}
                  </Text>
                </View>
              )}

              {transaction.note && (
                <View style={styles.infoRow}>
                  <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Note:
                  </Text>
                  <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                    {transaction.note}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Performed By */}
          {showPerformedBy && (
            <View style={styles.performedByContainer}>
              <PhosphorIcon name='user' size={12} color={theme.colors.onSurfaceVariant} />
              <Text style={[styles.performedByText, { color: theme.colors.onSurfaceVariant }]}>
                by {transaction.performedBy}
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  // Empty state
  if (displayTransactions.length === 0) {
    return (
      <View style={[styles.emptyContainer, style]}>
        <PhosphorIcon name='clock' size={48} color={theme.colors.onSurfaceVariant} />
        <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
          {emptyMessage}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {displayTransactions.map((transaction, index) => (
        <View key={transaction.id}>
          {renderTransactionItem({ item: transaction })}
          {!compact && index < displayTransactions.length - 1 && <View style={{ height: 8 }} />}
        </View>
      ))}

      {maxItems && transactions.length > maxItems && (
        <View style={styles.moreIndicator}>
          <Text style={[styles.moreText, { color: theme.colors.primary }]}>
            +{transactions.length - maxItems} more transactions
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Removed flex: 1 to prevent layout issues when used in ScrollView
  },
  transactionCard: {
    marginVertical: 2,
  },
  cardContent: {
    paddingVertical: 12,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  typeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  typeLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  quantityContainer: {
    alignItems: 'flex-end',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  detailsLeft: {
    flex: 1,
    gap: 4,
  },
  detailsRight: {
    alignItems: 'flex-end',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 13,
  },
  dateText: {
    fontSize: 12,
    fontWeight: '500',
  },
  timeText: {
    fontSize: 11,
    marginTop: 2,
  },
  additionalInfo: {
    marginTop: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginVertical: 2,
    gap: 8,
  },
  infoLabel: {
    fontSize: 11,
    fontWeight: '500',
    minWidth: 60,
  },
  infoValue: {
    fontSize: 11,
    flex: 1,
  },
  performedByContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 8,
    gap: 4,
  },
  performedByText: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  // Compact styles
  compactItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderLeftWidth: 3,
    marginVertical: 2,
  },
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  compactTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  compactTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  compactQuantity: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  compactDetails: {
    gap: 2,
  },
  compactTime: {
    fontSize: 10,
  },
  compactNote: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  // Empty state
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 14,
    marginTop: 12,
    textAlign: 'center',
  },
  // More indicator
  moreIndicator: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  moreText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default TransactionHistoryList;
