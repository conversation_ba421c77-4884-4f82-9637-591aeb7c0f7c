/**
 * Warehouse Selector Component
 * Dropdown selector for choosing warehouses
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Menu, Button, HelperText } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { Warehouse } from '../../types/inventory';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface WarehouseSelectorProps {
  warehouses: Warehouse[];
  selectedWarehouseId: string | null;
  onWarehouseSelect: (warehouse: Warehouse) => void;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  showDefault?: boolean;
  style?: ViewStyle;
  compact?: boolean;
}

const WarehouseSelector: React.FC<WarehouseSelectorProps> = ({
  warehouses,
  selectedWarehouseId,
  onWarehouseSelect,
  label = 'Warehouse',
  placeholder = 'Select warehouse',
  error,
  disabled = false,
  required = false,
  showDefault = true,
  style,
  compact = false,
}) => {
  const theme = useTheme();
  const [showMenu, setShowMenu] = useState<boolean>(false);

  // Get selected warehouse
  const selectedWarehouse = warehouses.find(w => w.id === selectedWarehouseId);

  // Filter active warehouses and sort by default first
  const activeWarehouses = warehouses
    .filter(w => w.isActive)
    .sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return a.name.localeCompare(b.name);
    });

  // Handle warehouse selection
  const handleWarehouseSelect = useCallback(
    (warehouse: Warehouse) => {
      setShowMenu(false);
      onWarehouseSelect(warehouse);
    },
    [onWarehouseSelect]
  );

  // Get display text for selected warehouse
  const getDisplayText = (): string => {
    if (selectedWarehouse) {
      return showDefault && selectedWarehouse.isDefault
        ? `${selectedWarehouse.name} (Default)`
        : selectedWarehouse.name;
    }
    return placeholder;
  };

  // Get warehouse icon
  const getWarehouseIcon = (warehouse: Warehouse): string => {
    return warehouse.isDefault ? 'house' : 'warehouse';
  };

  if (compact) {
    return (
      <View style={[styles.compactContainer, style]}>
        <Menu
          visible={showMenu}
          onDismiss={() => setShowMenu(false)}
          anchor={
            <Button
              mode='outlined'
              onPress={() => setShowMenu(true)}
              disabled={disabled || activeWarehouses.length === 0}
              style={[
                styles.compactButton,
                {
                  borderColor: error ? theme.colors.error : theme.colors.outline,
                },
              ]}
              contentStyle={styles.compactButtonContent}
              labelStyle={{
                color: selectedWarehouse ? theme.colors.onSurface : theme.colors.onSurfaceVariant,
                fontSize: 14,
              }}
              icon={() => (
                <PhosphorIcon name='caret-down' size={16} color={theme.colors.onSurface} />
              )}
            >
              {getDisplayText()}
            </Button>
          }
          contentStyle={{
            backgroundColor: theme.colors.surface,
          }}
        >
          {activeWarehouses.map(warehouse => (
            <Menu.Item
              key={warehouse.id}
              onPress={() => handleWarehouseSelect(warehouse)}
              title={warehouse.name}
              titleStyle={{
                color:
                  warehouse.id === selectedWarehouseId
                    ? theme.colors.primary
                    : theme.colors.onSurface,
                fontWeight: warehouse.id === selectedWarehouseId ? 'bold' : 'normal',
              }}
              leadingIcon={() => (
                <PhosphorIcon
                  name={getWarehouseIcon(warehouse) as any}
                  size={20}
                  color={warehouse.isDefault ? theme.colors.primary : theme.colors.onSurfaceVariant}
                />
              )}
              trailingIcon={
                warehouse.isDefault
                  ? () => (
                      <Text
                        style={{
                          color: theme.colors.primary,
                          fontSize: 10,
                          fontWeight: '600',
                        }}
                      >
                        DEFAULT
                      </Text>
                    )
                  : undefined
              }
            />
          ))}
        </Menu>

        {error && (
          <HelperText type='error' visible={!!error}>
            {error}
          </HelperText>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Label */}
      {label && (
        <Text style={[styles.label, { color: theme.colors.onSurface }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}

      {/* Selector Button */}
      <Menu
        visible={showMenu}
        onDismiss={() => setShowMenu(false)}
        anchor={
          <Button
            mode='outlined'
            onPress={() => setShowMenu(true)}
            disabled={disabled || activeWarehouses.length === 0}
            style={[
              styles.selectorButton,
              {
                borderColor: error ? theme.colors.error : theme.colors.outline,
                backgroundColor: disabled ? theme.colors.surfaceVariant : 'transparent',
              },
            ]}
            contentStyle={styles.buttonContent}
            labelStyle={{
              color: selectedWarehouse ? theme.colors.onSurface : theme.colors.onSurfaceVariant,
              fontSize: 16,
              textAlign: 'left',
            }}
            icon={() => <PhosphorIcon name='caret-down' size={20} color={theme.colors.onSurface} />}
          >
            {getDisplayText()}
          </Button>
        }
        contentStyle={{
          backgroundColor: theme.colors.surface,
          minWidth: 250,
        }}
      >
        {activeWarehouses.map(warehouse => (
          <Menu.Item
            key={warehouse.id}
            onPress={() => handleWarehouseSelect(warehouse)}
            title={warehouse.name}
            titleStyle={{
              color:
                warehouse.id === selectedWarehouseId
                  ? theme.colors.primary
                  : theme.colors.onSurface,
              fontWeight: warehouse.id === selectedWarehouseId ? 'bold' : 'normal',
            }}
            leadingIcon={() => (
              <PhosphorIcon
                name={getWarehouseIcon(warehouse) as any}
                size={20}
                color={warehouse.isDefault ? theme.colors.primary : theme.colors.onSurfaceVariant}
              />
            )}
            trailingIcon={
              warehouse.isDefault
                ? () => (
                    <View style={styles.defaultBadge}>
                      <Text style={[styles.defaultBadgeText, { color: theme.colors.primary }]}>
                        DEFAULT
                      </Text>
                    </View>
                  )
                : undefined
            }
          />
        ))}

        {/* No warehouses message */}
        {activeWarehouses.length === 0 && (
          <Menu.Item
            title='No warehouses available'
            disabled
            titleStyle={{
              color: theme.colors.onSurfaceVariant,
              fontStyle: 'italic',
            }}
            leadingIcon={() => (
              <PhosphorIcon name='warning' size={20} color={theme.colors.onSurfaceVariant} />
            )}
          />
        )}
      </Menu>

      {/* Selected Warehouse Details */}
      {selectedWarehouse && !compact && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <PhosphorIcon name='map-pin' size={14} color={theme.colors.onSurfaceVariant} />
            <Text style={[styles.detailText, { color: theme.colors.onSurfaceVariant }]}>
              {selectedWarehouse.location}
            </Text>
          </View>

          {selectedWarehouse.isDefault && (
            <View style={styles.detailRow}>
              <PhosphorIcon name='star' size={14} color={theme.colors.primary} />
              <Text style={[styles.detailText, { color: theme.colors.primary }]}>
                Default warehouse
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Error Message */}
      {error && (
        <HelperText type='error' visible={!!error}>
          {error}
        </HelperText>
      )}

      {/* Helper Text */}
      {!error && activeWarehouses.length === 0 && (
        <HelperText type='info' visible>
          No active warehouses available. Please add a warehouse first.
        </HelperText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  selectorButton: {
    justifyContent: 'flex-start',
    minHeight: 56,
  },
  buttonContent: {
    height: 56,
    justifyContent: 'space-between',
    flexDirection: 'row-reverse', // Icon on right
    paddingHorizontal: 16,
  },
  detailsContainer: {
    marginTop: 8,
    paddingHorizontal: 12,
    gap: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 12,
  },
  defaultBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  defaultBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  // Compact styles
  compactContainer: {
    marginVertical: 4,
  },
  compactButton: {
    minHeight: 40,
    justifyContent: 'center',
  },
  compactButtonContent: {
    height: 40,
    flexDirection: 'row-reverse', // Icon on right
  },
});

export default WarehouseSelector;
