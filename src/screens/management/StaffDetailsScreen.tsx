import React, { useEffect, useState, useCallback } from 'react';
import { ScrollView, View } from 'react-native';
import { Avatar, Card, Chip, Divider, Text as PaperText } from 'react-native-paper';
import { ActionSheetOption } from '../../types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import type { ActionSheetProps } from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { commonStyles } from '../../theme/commonStyles';
import { Staff } from '../../types';
import { StaffDetailsScreenProps } from '../../types/navigation';

// Removed unused common styles

const StaffDetailsScreen: React.FC<StaffDetailsScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state: dataState } = useData();
  const staffId = route?.params?.staffId;
  const [staffMember, setStaffMember] = useState<Staff | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteActionSheet, setShowDeleteActionSheet] = useState(false);
  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState(false);
  const [showErrorActionSheet, setShowErrorActionSheet] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  useEffect(() => {
    if (staffId) {
      const found = dataState.staff.find((s: Staff) => s.id === staffId);
      setStaffMember(found || null);
    }
    setLoading(false);
  }, [staffId, dataState.staff]);

  const handleEdit = (): void => {
    if (staffMember) {
      navigation.navigate('AddStaff', {
        editMode: true,
        staffId: staffMember.id,
      } as any);
    }
  };

  const handleDelete = (): void => {
    if (!staffMember) return;
    setShowDeleteActionSheet(true);
  };

  const confirmDelete = async (): Promise<void> => {
    if (!staffMember) return;

    try {
      // await staffActions.deleteStaff(staffMember.id);
      LoggingService.info('Staff deleted successfully', 'STAFF_DETAILS', {
        staffId: staffMember.id,
      });
      setSuccessMessage('Staff member deleted successfully!');
      setShowSuccessActionSheet(true);
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      LoggingService.error('Failed to delete staff', 'STAFF_DETAILS', error as Error);
      setErrorMessage('Failed to delete staff member. Please try again.');
      setShowErrorActionSheet(true);
    }
  };

  

  const getRoleDisplayName = (role: string): string => {
    const roleMap: Record<string, string> = {
      manager: 'Manager',
      tailor: 'Tailor',
      assistant: 'Assistant',
      designer: 'Designer',
      cutter: 'Cutter',
      finisher: 'Finisher',
      quality_checker: 'Quality Checker',
      customer_service: 'Customer Service',
    };
    return roleMap[role] || role;
  };

  if (loading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <PaperText>Loading...</PaperText>
      </View>
    );
  }

  if (!staffMember) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <PaperText>Staff member not found</PaperText>
        <Button onPress={() => navigation.goBack()}>Go Back</Button>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <Header
        title='Staff Details'
        showBack={true}
        onBackPress={() => navigation.goBack()}
        actions={[
          {
            text: 'Edit',
            onPress: handleEdit,
            color: theme.colors.primary,
          },
        ]}
        
      />

      <ScrollView
        style={commonStyles.flex1}
        contentContainerStyle={{ padding: 16, paddingBottom: insets.bottom + 16 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Section */}
        <Card style={{ borderRadius: 12, padding: 16, marginBottom: 16 }}>
          <View style={{ alignItems: 'center', marginBottom: 16 }}>
            <Avatar.Image
              size={80}
              source={
                staffMember.profileImage
                  ? { uri: staffMember.profileImage }
                  : require('../../../assets/icon.png')
              }
            />
            <PaperText
              variant='headlineSmall'
              style={{ marginTop: 12, color: theme.colors.onSurface }}
            >
              {staffMember.name}
            </PaperText>
            <Chip
              mode='outlined'
              style={commonStyles.marginTop8}
              textStyle={{ color: theme.colors.primary }}
            >
              {getRoleDisplayName(staffMember.role)}
            </Chip>
          </View>

          <Divider style={{ marginVertical: 16 }} />

          <View style={{ gap: 12 }}>
            <View>
              <PaperText variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                Employee ID
              </PaperText>
              <PaperText variant='bodyLarge' style={{ color: theme.colors.onSurface }}>
                {staffMember.employeeId}
              </PaperText>
            </View>

            {staffMember.email && (
              <View>
                <PaperText variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                  Email
                </PaperText>
                <PaperText variant='bodyLarge' style={{ color: theme.colors.onSurface }}>
                  {staffMember.email}
                </PaperText>
              </View>
            )}

            {staffMember.phone && (
              <View>
                <PaperText variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                  Phone
                </PaperText>
                <PaperText variant='bodyLarge' style={{ color: theme.colors.onSurface }}>
                  {staffMember.phone}
                </PaperText>
              </View>
            )}

            

            <View>
              <PaperText variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                Status
              </PaperText>
              <Chip
                mode='flat'
                style={{
                  alignSelf: 'flex-start',
                  marginTop: 4,
                  backgroundColor: staffMember.isActive ? theme.colors.primary : theme.colors.error,
                }}
                textStyle={{ color: theme.colors.onPrimary }}
              >
                {staffMember.isActive ? 'Active' : 'Inactive'}
              </Chip>
            </View>
          </View>
        </Card>

        {/* Performance Section */}
        <Card style={{ borderRadius: 12, padding: 16, marginBottom: 16 }}>
          <PaperText
            variant='titleMedium'
            style={{ marginBottom: 16, color: theme.colors.onSurface }}
          >
            Performance Overview
          </PaperText>

          <View style={{ gap: 12 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                Orders Completed
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.performance?.ordersCompleted || 0}
              </PaperText>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                Quality Rating
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.performance?.qualityRating || 0}/5
              </PaperText>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                On-Time Delivery
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.performance?.onTimeDeliveryRate || 0}%
              </PaperText>
            </View>
          </View>
        </Card>

        {/* Workload Section */}
        <Card style={{ borderRadius: 12, padding: 16, marginBottom: 16 }}>
          <PaperText
            variant='titleMedium'
            style={{ marginBottom: 16, color: theme.colors.onSurface }}
          >
            Current Workload
          </PaperText>

          <View style={{ gap: 12 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                Current Orders
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.workload?.currentOrders || 0}
              </PaperText>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                Available Hours
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.workload?.availableHours || 0}h
              </PaperText>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <PaperText variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                Efficiency
              </PaperText>
              <PaperText
                variant='bodyLarge'
                style={{ color: theme.colors.onSurface, fontWeight: '600' }}
              >
                {staffMember.workload?.efficiency || 0}%
              </PaperText>
            </View>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={{ gap: 12 }}>
          <Button variant='primary' onPress={handleEdit} style={commonStyles.borderRadius8}>
            Edit Staff Member
          </Button>

          <Button
            variant='outline'
            onPress={handleDelete}
            textColor={theme.colors.error}
            style={{ borderRadius: 8, borderColor: theme.colors.error }}
          >
            Delete Staff Member
          </Button>
        </View>
      </ScrollView>

      {/* Action Sheets */}
      <ActionSheet
        visible={showDeleteActionSheet}
        onDismiss={() => setShowDeleteActionSheet(false)}
        title='Delete Staff Member'
        description={`Are you sure you want to delete ${staffMember?.name}? This action cannot be undone.`}
        options={[
          {
            text: 'Delete',
            onPress: confirmDelete,
            icon: 'trash',
            style: 'destructive',
            isAction: true,
          },
        ]}
        showCancel={true}
        cancelText='Cancel'
        
      />

      <ActionSheet
        visible={showSuccessActionSheet}
        onDismiss={() => setShowSuccessActionSheet(false)}
        title='Success'
        description={successMessage}
        options={[
          {
            text: 'OK',
            onPress: () => setShowSuccessActionSheet(false),
            icon: 'check-circle',
            style: 'primary',
            isAction: true,
          },
        ]}
        showCancel={false}
      />

      <ActionSheet
        visible={showErrorActionSheet}
        onDismiss={() => setShowErrorActionSheet(false)}
        title='Error'
        description={errorMessage}
        options={[
          {
            text: 'OK',
            onPress: () => setShowErrorActionSheet(false),
            icon: 'warning',
            style: 'destructive',
            isAction: true,
          },
        ]}
        showCancel={false}
      />
    </View>
  );
};

export default StaffDetailsScreen;
