import React, { useCallback, useMemo, useState } from 'react';
import { FlatList, ListRenderItem, RefreshControl, StyleSheet, View } from 'react-native';
import { OrderCard } from '@/components/cards';
import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import ChipGroup from '@/components/ui/ChipGroup';
import EmptyState from '@/components/ui/EmptyState';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { ActionSheetOption } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { useNotifications } from '@/services/notificationService';
import { Order, StatCard } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIconName } from '@/utils/phosphorIconRegistry';

const commonStyles = StyleSheet.create({
  flex1: { flex: 1 },
  justifyCenter: { justifyContent: 'center' },
  alignCenter: { alignItems: 'center' },
  marginTop16: { marginTop: 16 },
  marginBottom16: { marginBottom: 16 },
  fontWeight600: { fontWeight: '600' },
  fontWeight700: { fontWeight: '700' },
  borderRadius8: { borderRadius: 8 },
  borderRadius12: { borderRadius: 12 },
  padding16: { padding: 16 },
  marginLeft8: { marginLeft: 8 },
  marginTop8: { marginTop: 8 },
  marginTop4: { marginTop: 4 },
  marginTop2: { marginTop: 2 },
  fontSize14: { fontSize: 14 },
  textCenter: { textAlign: 'center' },
});

interface OrdersScreenProps {
  navigation: any;
}

interface OrderStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  revenue: number;
}

const OrdersScreen: React.FC<OrdersScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { state: authState } = useAuth();
  const { unreadCount } = useNotifications();
  const { showSuccess, showError } = useToast();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('All');
  const invoiceBottomSheetRef = React.useRef<any>(null);
  const [invoiceOrder, setInvoiceOrder] = useState<Order | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [actionSheetVisible, setActionSheetVisible] = useState<boolean>(false);
  const [actionSheetOrder, setActionSheetOrder] = useState<Order | null>(null);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Filter options with icons
  const filterOptions = [
    { id: 'All', label: 'All', icon: 'clipboard-text' as PhosphorIconName },
    { id: 'Pending', label: 'Pending', icon: 'clock' as PhosphorIconName },
    { id: 'In Progress', label: 'In Progress', icon: 'gear' as PhosphorIconName },
    { id: 'Completed', label: 'Completed', icon: 'check-circle' as PhosphorIconName },
    { id: 'Cancelled', label: 'Cancelled', icon: 'x-circle' as PhosphorIconName },
    { id: 'Recent', label: 'Recent', icon: 'calendar' as PhosphorIconName },
    { id: 'High Value', label: 'High Value', icon: 'star' as PhosphorIconName },
  ];

  // Icon mapping for ChipGroup
  const filterIcons = {
    All: 'clipboard-text',
    Pending: 'clock',
    'In Progress': 'gear',
    Completed: 'check-circle',
    Cancelled: 'x-circle',
    Recent: 'calendar',
    'High Value': 'star',
  };

  // Pull to refresh functionality
  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Orders data refreshed', 'SCREEN');
      showSuccess('Orders refreshed successfully');
    } catch (error) {
      LoggingService.warn('Failed to refresh orders data', 'SCREEN', error as Error);
      showError('Failed to refresh orders');
    } finally {
      setRefreshing(false);
    }
  }, [showSuccess, showError]);

  const filteredOrders = useMemo(() => {
    // Ensure orders is always an array
    const orders = state.orders || [];
    if (!Array.isArray(orders)) {
      return [];
    }

    return orders.filter((order: any) => {
      // Safely handle customer name with multiple fallbacks
      const customerName = order?.customerName || order?.customer || '';
      const orderId = order?.id ? order.id.toString() : '';
      const orderStatus = order?.status || 'pending';

      // Safely perform search matching
      const matchesSearch =
        (customerName && customerName.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (orderId && orderId.includes(searchQuery));

      // Apply filter based on selectedStatus
      let matchesFilter = true;
      if (selectedStatus && selectedStatus !== 'All') {
        switch (selectedStatus) {
          case 'Pending':
          case 'In Progress':
          case 'Completed':
          case 'Cancelled':
            matchesFilter = orderStatus === selectedStatus;
            break;
          case 'Recent':
            // Orders from last 7 days
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const orderDate = order?.createdAt ? new Date(order.createdAt) : new Date();
            matchesFilter = orderDate > sevenDaysAgo;
            break;
          case 'High Value':
            // Orders with total > ৳5000
            const orderTotal = order?.total || order?.totalAmount || 0;
            matchesFilter = orderTotal > 5000;
            break;
          default:
            matchesFilter = orderStatus === selectedStatus;
        }
      }

      return matchesSearch && matchesFilter;
    });
  }, [state.orders, searchQuery, selectedStatus]);

  const handleAddOrder = (): void => {
    LoggingService.info('Navigating to Add Order page', 'NAVIGATION');
    try {
      navigation.navigate('AddOrder');
    } catch (error) {
      LoggingService.error('Failed to navigate to AddOrder', 'NAVIGATION', error as Error);
    }
  };

  const handleOrderPress = useCallback((order: Order): void => {
    LoggingService.info('Order card pressed', 'NAVIGATION', { orderId: order.id });
    // Navigate directly to order details or edit page
    try {
      navigation.navigate('AddOrder', { order });
    } catch (error) {
      LoggingService.error('Failed to navigate to order details', 'NAVIGATION', error as Error);
    }
  }, [navigation]);

  const handleEditOrder = useCallback((order: Order): void => {
    LoggingService.info('Navigating to Edit Order page', 'NAVIGATION', { orderId: order.id });
    try {
      // Navigate to AddOrder page with order data for editing
      navigation.navigate('AddOrder', { order });
    } catch (error) {
      LoggingService.error('Failed to navigate to edit order', 'NAVIGATION', error as Error);
    }
  }, [navigation]);

  const handleDeleteOrder = useCallback((order: any): void => {
    setActionSheetOrder(order);
    setActionSheetVisible(true);
  }, []);

  const handleDeleteOrderConfirm = useCallback(
    (order: any): void => {
      actions.deleteOrder(order.id.toString());
      setActionSheetVisible(false);
      setActionSheetOrder(null);
    },
    [actions]
  );

  const handleUpdateOrderStatus = useCallback(
    (orderId: string | number, newStatus: string): void => {
      actions.updateOrderStatus(orderId.toString(), newStatus);
    },
    [actions]
  );

  const handleViewInvoice = useCallback((order: Order): void => {
    LoggingService.info('Opening invoice for order', 'SCREEN', { orderId: order.id });
    setInvoiceOrder(order);
    invoiceBottomSheetRef.current?.open();
  }, []);

  // const handleGeneratePDF = async (order: Order): Promise<void> => {
  //     // PDF generation functionality - not currently used
  // };

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    LoggingService.info('Search query updated', 'ORDERS_SCREEN', { query });
  }, []);

  const renderOrderCard: ListRenderItem<any> = useCallback(
    ({ item: order }) => {
      return <OrderCard order={order} onPress={handleOrderPress} />;
    },
    [handleOrderPress]
  );

  const orderStats = useMemo((): OrderStats => {
    // Ensure filteredOrders is always an array
    const orders = Array.isArray(filteredOrders) ? filteredOrders : [];

    return {
      total: orders.length,
      pending: orders.filter((o: any) => o.status === 'Pending').length,
      inProgress: orders.filter((o: any) => o.status === 'In Progress').length,
      completed: orders.filter((o: any) => o.status === 'Completed').length,
      revenue: orders.reduce((sum: number, order: any) => sum + (order.total || 0), 0),
    };
  }, [filteredOrders]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Orders'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchData={state.orders || []}
        searchFields={['id', 'customerName', 'customer', 'status']}
        searchPlaceholder='Search orders...'
        searchType='orders'
        onSearch={handleSearch}
        actions={[
          {
            icon: 'plus',
            onPress: handleAddOrder,
          },
        ]}
        
      />

      {/* Sticky ChipGroup */}
      <View style={{ backgroundColor: theme.colors.background, paddingHorizontal: 16, zIndex: 1 }}>
        <ChipGroup
          filters={filterOptions}
          selectedFilter={selectedStatus}
          onFilterChange={filter => setSelectedStatus(String(filter))}
          showIcons={false}
          showCounts={true}
          data={state.orders || []}
          countField='status'
          borderRadius='full-rounded'
          style={{ marginTop: 12, marginBottom: 4 }}
          chipStyle={{}}
        />
      </View>

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={item => item.id.toString()}
        style={[styles.content, { paddingTop: 4 }]}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {/* Stats Row */}
            <StatCardGroup
              title='Order Stats'
              cards={
                [
                  {
                    key: 'total',
                    title: 'Total Orders',
                    value: orderStats.total.toString(),
                    icon: 'clipboard-text',
                    iconColor: theme.colors.primary,
                  },
                  {
                    key: 'revenue',
                    title: 'Revenue',
                    value: formatCurrency(orderStats.revenue, { decimals: 0 }),
                    icon: 'money',
                    iconColor: theme.colors.secondary,
                  },
                ] as StatCard[]
              }
              columns={2}
              showTitle={false}
              containerStyle={{ marginBottom: 16 }}
            />
          </View>
        )}
        ListEmptyComponent={() => (
          <EmptyState
            type='orders'
            searchQuery={searchQuery}
            onActionPress={handleAddOrder}
            description='No orders found'
            actionLabel='Add Order'
            style={{}}
            iconColor={theme.colors.primary}
          />
        )}
      />

      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete Order #${actionSheetOrder?.id}`}
        description={`Are you sure you want to delete order #${actionSheetOrder?.id}? This action cannot be undone.`}
        options={[
          {
            text: 'Delete',
            onPress: () => handleDeleteOrderConfirm(actionSheetOrder as Order),
            style: 'destructive',
            isAction: true
          },
          {
            text: 'Cancel',
            onPress: () => setActionSheetVisible(false),
            style: 'cancel',
            isAction: false
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
});

export default OrdersScreen;
