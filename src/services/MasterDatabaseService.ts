/**
 * Master Database Service - Single Unified Database System for TailorZa
 * 
 * This service consolidates ALL database operations, storage, caching, and sync
 * into a single, comprehensive system. No more fragmented services!
 * 
 * Features:
 * - SQLite database with AsyncStorage fallback
 * - Built-in caching layer
 * - Offline sync capabilities
 * - Health monitoring and recovery
 * - Automatic backups
 * - Performance optimization
 * - Error handling and logging
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SQLite from 'expo-sqlite';


import { 
  Customer, 
  Order, 
  OrderItem, 
  Product, 
  Staff, 
  Outlet,
  DatabaseRecord,
  DatabaseHealth,
  DatabaseMetrics,
  Measurement,
  Appointment,
  Payment,
  Notification,
  AuditLog,
  AppSetting
} from '../types';
import {
  InventoryItem,
  InventoryStock,
  InventoryTransaction,
  StockTransfer,
  Warehouse
} from '../types/inventory';// ============================================================================

import LoggingService from './LoggingService';
// INTERFACES AND TYPES
// ============================================================================

interface DatabaseConfig {
  databaseName: string;
  enableCache: boolean;
  enableOfflineSync: boolean;
  enableMonitoring: boolean;
  enableAutoBackup: boolean;
  cacheSize: number;
  cacheTTL: number;
  syncInterval: number;
  backupInterval: number;
  maxRetries: number;
}

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

interface SyncQueueItem {
  id: string;
  table: string;
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

interface DatabaseHealthStatus {
  isConnected: boolean;
  isHealthy: boolean;
  lastCheck: number;
  errors: string[];
  performance: {
    avgQueryTime: number;
    totalQueries: number;
    errorRate: number;
  };
}

interface BackupInfo {
  id: string;
  timestamp: number;
  size: number;
  tables: string[];
  recordCount: number;
}

// ============================================================================
// MASTER DATABASE SERVICE CLASS
// ============================================================================

export class MasterDatabaseService {
  private static instance: MasterDatabaseService;
  private database: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;
  private isOnline = true;
  private syncTimer: NodeJS.Timeout | null = null;
  private backupTimer: NodeJS.Timeout | null = null;
  private startTime: number = Date.now();
  
  // Configuration
  private config: DatabaseConfig = {
    databaseName: 'tailorza_master.db',
    enableCache: true,
    enableOfflineSync: true,
    enableMonitoring: true,
    enableAutoBackup: true,
    cacheSize: 1000,
    cacheTTL: 300000, // 5 minutes
    syncInterval: 30000, // 30 seconds
    backupInterval: 3600000, // 1 hour
    maxRetries: 3,
  };

  // Cache system
  private cache = new Map<string, CacheEntry>();
  private cacheStats = { hits: 0, misses: 0, sets: 0 };
  
  // Sync system
  private syncQueue: SyncQueueItem[] = [];
  private syncInProgress = false;
  
  // Health monitoring
  private health: DatabaseHealth = {
    isConnected: false,
    connectionCount: 0,
    uptime: 0,
    performanceMetrics: {
      totalQueries: 0,
      averageQueryTime: 0,
      errorRate: 0,
      databaseSize: 0,
    }
  };
  
  // Backup system
  private lastBackup: BackupInfo | null = null;

  // ============================================================================
  // SINGLETON PATTERN
  // ============================================================================

  public static getInstance(): MasterDatabaseService {
    if (!MasterDatabaseService.instance) {
      MasterDatabaseService.instance = new MasterDatabaseService();
    }
    return MasterDatabaseService.instance;
  }

  private constructor() {
    // Private constructor for singleton
  }

  // ============================================================================
  // INITIALIZATION AND SETUP
  // ============================================================================

  public async initialize(customConfig?: Partial<DatabaseConfig>): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      LoggingService.info('Initializing Master Database Service', 'MASTER_DB');

      // Apply custom configuration
      if (customConfig) {
        this.config = { ...this.config, ...customConfig };
      }

      // Initialize SQLite database
      await this.initializeDatabase();
      
      // Initialize cache system
      if (this.config.enableCache) {
        await this.initializeCache();
      }
      
      // Initialize sync system
      if (this.config.enableOfflineSync) {
        await this.initializeSync();
      }
      
      // Initialize monitoring
      if (this.config.enableMonitoring) {
        await this.initializeMonitoring();
      }
      
      // Initialize auto backup
      if (this.config.enableAutoBackup) {
        await this.initializeAutoBackup();
      }

      this.isInitialized = true;
      LoggingService.info('Master Database Service initialized successfully', 'MASTER_DB');
      
    } catch (error) {
      LoggingService.error('Failed to initialize Master Database Service', 'MASTER_DB', error as Error);
      throw error;
    }
  }

  private async initializeDatabase(): Promise<void> {
    try {
      // Try SQLite first
      this.database = await SQLite.openDatabaseAsync(this.config.databaseName);
      await this.createTables();
      await this.testConnection();
      
      this.health.isConnected = true;
      LoggingService.info('SQLite database initialized', 'MASTER_DB');
      
    } catch (error) {
      LoggingService.warn('SQLite initialization failed, using AsyncStorage fallback', 'MASTER_DB');
      this.database = null;
      this.health.isConnected = false;
      // AsyncStorage fallback will be used automatically
    }
  }

  private async initializeCache(): Promise<void> {
    // Load cache from AsyncStorage if available
    try {
      const cacheData = await AsyncStorage.getItem('master_db_cache');
      if (cacheData) {
        const parsedCache = JSON.parse(cacheData);
        for (const [key, entry] of Object.entries(parsedCache)) {
          const cacheEntry = entry as CacheEntry;
          if (Date.now() < cacheEntry.timestamp + cacheEntry.ttl) {
            this.cache.set(key, cacheEntry);
          }
        }
      }
      LoggingService.info('Cache system initialized', 'MASTER_DB');
    } catch (error) {
      LoggingService.warn('Failed to load cache from storage', 'MASTER_DB');
    }
  }

  private async initializeSync(): Promise<void> {
    // Load sync queue from AsyncStorage
    try {
      const queueData = await AsyncStorage.getItem('master_db_sync_queue');
      if (queueData) {
        this.syncQueue = JSON.parse(queueData);
      }
      
      // Start sync timer
      this.syncTimer = setInterval(() => {
        this.processSyncQueue();
      }, this.config.syncInterval);
      
      LoggingService.info('Sync system initialized', 'MASTER_DB');
    } catch (error) {
      LoggingService.warn('Failed to initialize sync system', 'MASTER_DB');
    }
  }

  private async initializeMonitoring(): Promise<void> {
    // Start health check timer
    setInterval(() => {
      this.performHealthCheck();
    }, 60000); // Check every minute
    
    LoggingService.info('Monitoring system initialized', 'MASTER_DB');
  }

  private async initializeAutoBackup(): Promise<void> {
    // Start backup timer
    this.backupTimer = setInterval(() => {
      this.createAutoBackup();
    }, this.config.backupInterval);
    
    LoggingService.info('Auto backup system initialized', 'MASTER_DB');
  }

  // ============================================================================
  // DATABASE SCHEMA CREATION
  // ============================================================================

  private async createTables(): Promise<void> {
    if (!this.database) return;

    // Get all table creation SQL statements
    const tableCreationSQLs = [
      this.getStaffTableSQL(),
      this.getCustomersTableSQL(),
      this.getProductsTableSQL(),
      this.getInventoryTableSQL(),
      this.getInventoryStockTableSQL(),
      this.getWarehousesTableSQL(),
      this.getInventoryTransactionsTableSQL(),
      this.getOrdersTableSQL(),
      this.getOrderItemsTableSQL(),
      this.getMeasurementsTableSQL(),
      this.getAppointmentsTableSQL(),
      this.getPaymentsTableSQL(),
      this.getNotificationsTableSQL(),
      this.getAuditLogsTableSQL(),
      this.getSettingsTableSQL(),
    ];

    for (const sql of tableCreationSQLs) {
      await this.database.execAsync(sql);
    }

    // Run migrations for existing databases
    await this.runMigrations();

    // Create indexes for better performance
    await this.createIndexes();
  }

  

  private getStaffTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS staff (
        id TEXT PRIMARY KEY,
        outlet_id TEXT NOT NULL,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        role TEXT NOT NULL,
        hire_date TEXT,
        salary REAL,
        is_active BOOLEAN DEFAULT 1,
        permissions TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,

      )
    `;
  }

  private getCustomersTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        outlet_id TEXT NOT NULL,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        date_of_birth TEXT,
        gender TEXT,
        preferences TEXT,
        notes TEXT,
        total_orders INTEGER DEFAULT 0,
        total_spent REAL DEFAULT 0,
        last_order_date TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,

      )
    `;
  }

  private getProductsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        sku TEXT,
        base_price REAL NOT NULL,
        cost_price REAL,
        is_service BOOLEAN DEFAULT 0,
        requires_measurements BOOLEAN DEFAULT 0,
        estimated_time_minutes INTEGER,
        is_active BOOLEAN DEFAULT 1,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
  }

  private getInventoryTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS inventory (
        id TEXT PRIMARY KEY,
        outlet_id TEXT NOT NULL,
        product_id TEXT,
        name TEXT NOT NULL,
        category TEXT,
        quantity INTEGER DEFAULT 0,
        unit TEXT,
        cost_price REAL,
        selling_price REAL,
        reorder_level INTEGER DEFAULT 0,
        supplier TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
,
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    `;
  }

  private getInventoryStockTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS inventory_stock (
        id TEXT PRIMARY KEY,
        item_id TEXT NOT NULL,
        warehouse_id TEXT NOT NULL,
        quantity INTEGER DEFAULT 0,
        unit TEXT NOT NULL,
        reserved_quantity INTEGER DEFAULT 0,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (item_id) REFERENCES inventory (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `;
  }

  private getOrdersTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        outlet_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        staff_id TEXT,
        order_number TEXT UNIQUE NOT NULL,
        status TEXT DEFAULT 'pending',
        type TEXT DEFAULT 'custom',
        total_amount REAL DEFAULT 0,
        paid_amount REAL DEFAULT 0,
        due_date TEXT,
        completion_date TEXT,
        notes TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (staff_id) REFERENCES staff (id)
      )
    `;
  }

  private getOrderItemsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS order_items (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        product_name TEXT,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    `;
  }

  private getMeasurementsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS measurements (
        id TEXT PRIMARY KEY,
        customer_id TEXT NOT NULL,
        order_id TEXT,
        garment_type TEXT NOT NULL,
        measurements TEXT NOT NULL,
        notes TEXT,
        taken_by TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `;
  }

  private getAppointmentsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        outlet_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        staff_id TEXT,
        appointment_date TEXT NOT NULL,
        appointment_time TEXT NOT NULL,
        duration INTEGER DEFAULT 60,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'scheduled',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (staff_id) REFERENCES staff (id)
      )
    `;
  }

  private getPaymentsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS payments (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        amount REAL NOT NULL,
        method TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        transaction_id TEXT,
        payment_date TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `;
  }

  private getNotificationsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        outlet_id TEXT,
        customer_id TEXT,
        staff_id TEXT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        status TEXT DEFAULT 'unread',
        priority TEXT DEFAULT 'normal',
        scheduled_for TEXT,
        sent_at TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (staff_id) REFERENCES staff (id)
      )
    `;
  }

  private getAuditLogsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        outlet_id TEXT,
        user_id TEXT,
        action TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT,
        old_values TEXT,
        new_values TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at TEXT NOT NULL
      )
    `;
  }

  private getSettingsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        outlet_id TEXT,
        category TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT,
        isJson BOOLEAN DEFAULT 0,
        type TEXT DEFAULT 'string',
        description TEXT,
        is_system BOOLEAN DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(outlet_id, category, key)
      )
    `;
  }

  private getWarehousesTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS warehouses (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        location TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
  }

  private getInventoryTransactionsTableSQL(): string {
    return `
      CREATE TABLE IF NOT EXISTS inventory_transactions (
        id TEXT PRIMARY KEY,
        item_id TEXT NOT NULL,
        warehouse_id TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unit TEXT NOT NULL,
        type TEXT NOT NULL, -- e.g., stock_in, stock_out, transfer_in, transfer_out
        performed_by TEXT,
        reference TEXT,
        note TEXT,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (item_id) REFERENCES inventory (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `;
  }

  private async createIndexes(): Promise<void> {
    if (!this.database) return;

    const indexes = [
      
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)',
      'CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customer_id)',
      
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items(product_id)',
      
      
      'CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory(product_id)',
      'CREATE INDEX IF NOT EXISTS idx_appointments_customer ON appointments(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date)',
      'CREATE INDEX IF NOT EXISTS idx_payments_order ON payments(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_customer ON notifications(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_table ON audit_logs(table_name)',
      'CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category, key)',
    ];

    for (const indexSQL of indexes) {
      await this.database.execAsync(indexSQL);
    }
  }

  private async runMigrations(): Promise<void> {
    if (!this.database) return;

    try {
      // Check if isJson column exists in settings table
      const tableInfo = await this.database.getAllAsync(`PRAGMA table_info(settings)`);
      const hasIsJsonColumn = tableInfo.some((column: any) => column.name === 'isJson');

      if (!hasIsJsonColumn) {
        // Migration 1: Add isJson column to settings table
        await this.database.execAsync(`
          ALTER TABLE settings ADD COLUMN isJson BOOLEAN DEFAULT 0
        `);
        LoggingService.info('Migration: Added isJson column to settings table', 'MASTER_DB');
      } else {
        LoggingService.debug('Migration: isJson column already exists in settings table', 'MASTER_DB');
      }
    } catch (error) {
      LoggingService.error('Migration failed for settings.isJson column', 'MASTER_DB', error as Error);
    }

    // Add more migrations here as needed
  }

  private async testConnection(): Promise<void> {
    if (!this.database) return;

    try {
      await this.database.getFirstAsync('SELECT 1 as test');
      this.health.isConnected = true;
      this.health.connectionCount++;
    } catch (error) {
      this.health.isConnected = false;
      this.health.lastError = (error as Error).message;
      throw error;
    }
  }

  // ============================================================================
  // CORE DATABASE OPERATIONS
  // ============================================================================

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  private async executeQuery<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    const startTime = Date.now();

    try {
      if (!this.database) {
        // Fallback to AsyncStorage
        return await this.executeAsyncStorageQuery<T>(sql, params);
      }

      const result = await this.database.getAllAsync(sql, params);

      // Update performance metrics
      const queryTime = Date.now() - startTime;
      this.updatePerformanceMetrics(queryTime, true);

      return result as T[];

    } catch (error) {
      this.updatePerformanceMetrics(Date.now() - startTime, false);
      LoggingService.error(`Query failed: ${sql}`, 'MASTER_DB', error as Error);

      // Try AsyncStorage fallback
      if (this.database) {
        LoggingService.warn('Falling back to AsyncStorage', 'MASTER_DB');
        return await this.executeAsyncStorageQuery<T>(sql, params);
      }

      throw error;
    }
  }

  private async executeNonQuery(sql: string, params: any[] = []): Promise<void> {
    const startTime = Date.now();

    try {
      if (!this.database) {
        // Fallback to AsyncStorage
        await this.executeAsyncStorageNonQuery(sql, params);
        return;
      }

      await this.database.runAsync(sql, params);

      // Update performance metrics
      const queryTime = Date.now() - startTime;
      this.updatePerformanceMetrics(queryTime, true);

    } catch (error) {
      this.updatePerformanceMetrics(Date.now() - startTime, false);
      LoggingService.error(`Non-query failed: ${sql}`, 'MASTER_DB', error as Error);

      // Try AsyncStorage fallback
      if (this.database) {
        LoggingService.warn('Falling back to AsyncStorage for non-query', 'MASTER_DB');
        await this.executeAsyncStorageNonQuery(sql, params);
        return;
      }

      throw error;
    }
  }

  private updatePerformanceMetrics(queryTime: number, success: boolean): void {
    this.health.performanceMetrics.totalQueries++;

    if (success) {
      const totalTime = this.health.performanceMetrics.averageQueryTime * (this.health.performanceMetrics.totalQueries - 1) + queryTime;
      this.health.performanceMetrics.averageQueryTime = totalTime / this.health.performanceMetrics.totalQueries;
    } else {
      const errorRate = (this.health.performanceMetrics.errorRate * (this.health.performanceMetrics.totalQueries - 1) + 1) / this.health.performanceMetrics.totalQueries;
      this.health.performanceMetrics.errorRate = errorRate;
    }
  }

  // ============================================================================
  // ASYNCSTORAGE FALLBACK OPERATIONS
  // ============================================================================

  private async executeAsyncStorageQuery<T>(sql: string, params: any[]): Promise<T[]> {
    // Parse SQL to determine table and operation
    const tableName = this.extractTableFromSQL(sql);
    const operation = this.extractOperationFromSQL(sql);

    try {
      const data = await AsyncStorage.getItem(`table_${tableName}`);
      const records: T[] = data ? JSON.parse(data) : [];

      // Apply basic filtering based on SQL (simplified)
      return this.applyBasicFiltering(records, sql, params);

    } catch (error) {
      LoggingService.error(`AsyncStorage query failed for table ${tableName}`, 'MASTER_DB', error as Error);
      return [];
    }
  }

  private async executeAsyncStorageNonQuery(sql: string, params: any[]): Promise<void> {
    const tableName = this.extractTableFromSQL(sql);
    const operation = this.extractOperationFromSQL(sql);

    try {
      const data = await AsyncStorage.getItem(`table_${tableName}`);
      let records: any[] = data ? JSON.parse(data) : [];

      if (operation === 'INSERT') {
        const newRecord = this.buildRecordFromInsertSQL(sql, params);
        records.push(newRecord);
      } else if (operation === 'UPDATE') {
        records = this.applyUpdateToRecords(records, sql, params);
      } else if (operation === 'DELETE') {
        records = this.applyDeleteToRecords(records, sql, params);
      }

      await AsyncStorage.setItem(`table_${tableName}`, JSON.stringify(records));

    } catch (error) {
      LoggingService.error(`AsyncStorage non-query failed for table ${tableName}`, 'MASTER_DB', error as Error);
      throw error;
    }
  }

  // Helper methods for AsyncStorage fallback (simplified implementations)
  private extractTableFromSQL(sql: string): string {
    const match = sql.match(/(?:FROM|INTO|UPDATE)\s+(\w+)/i);
    return match ? match[1] : 'unknown';
  }

  private extractOperationFromSQL(sql: string): string {
    const operation = sql.trim().split(' ')[0].toUpperCase();
    return operation;
  }

  private applyBasicFiltering<T>(records: T[], sql: string, params: any[]): T[] {
    // Basic filtering implementation for WHERE id = ?
    const idMatch = sql.match(/WHERE\s+id\s*=\s*\?/i);
    if (idMatch && params.length > 0) {
      const idToFilter = params[0];
      return records.filter((record: any) => record.id === idToFilter);
    }
    // For other filters or no filters, return all records
    return records;
  }

  private buildRecordFromInsertSQL(sql: string, params: any[]): any {
    // Simplified record building - in a real implementation, you'd parse the INSERT statement
    const id = this.generateId();
    const now = this.getCurrentTimestamp();
    return { id, created_at: now, updated_at: now, ...params };
  }

  private applyUpdateToRecords(records: any[], sql: string, params: any[]): any[] {
    // Simplified update - in a real implementation, you'd parse the UPDATE statement
    return records;
  }

  private applyDeleteToRecords(records: any[], sql: string, params: any[]): any[] {
    // Basic delete implementation: assumes DELETE FROM table WHERE id = ?
    const idToDelete = params[0];
    return records.filter(record => record.id !== idToDelete);
  }

  // ============================================================================
  // CACHE SYSTEM
  // ============================================================================

  private getCacheKey(table: string, id?: string, filters?: any): string {
    if (id) {
      return `${table}:${id}`;
    }
    if (filters) {
      return `${table}:${JSON.stringify(filters)}`;
    }
    return `${table}:all`;
  }

  private async getFromCache<T>(key: string): Promise<T | null> {
    if (!this.config.enableCache) return null;

    const entry = this.cache.get(key);
    if (!entry) {
      this.cacheStats.misses++;
      return null;
    }

    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.cacheStats.misses++;
      return null;
    }

    this.cacheStats.hits++;
    return entry.data as T;
  }

  private async setInCache<T>(key: string, data: T, ttl?: number): Promise<void> {
    if (!this.config.enableCache) return;

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.cacheTTL,
      key,
    };

    this.cache.set(key, entry);
    this.cacheStats.sets++;

    // Enforce cache size limit
    if (this.cache.size > this.config.cacheSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    // Persist cache to AsyncStorage periodically
    if (this.cacheStats.sets % 10 === 0) {
      await this.persistCache();
    }
  }

  private async persistCache(): Promise<void> {
    try {
      const cacheData: { [key: string]: CacheEntry } = {};
      for (const [key, entry] of Array.from(this.cache.entries())) {
        cacheData[key] = entry;
      }
      await AsyncStorage.setItem('master_db_cache', JSON.stringify(cacheData));
    } catch (error) {
      LoggingService.warn('Failed to persist cache', 'MASTER_DB');
    }
  }

  public clearCache(): void {
    this.cache.clear();
    this.cacheStats = { hits: 0, misses: 0, sets: 0 };
    AsyncStorage.removeItem('master_db_cache');
    LoggingService.info('Cache cleared', 'MASTER_DB');
  }

  // ============================================================================
  // HIGH-LEVEL CRUD OPERATIONS
  // ============================================================================

  public async create<T>(table: string, data: Partial<T>): Promise<T> {
    const id = this.generateId();
    const now = this.getCurrentTimestamp();
    const record = { id, ...data, created_at: now, updated_at: now };

    const columns = Object.keys(record);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(record);

    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;

    await this.executeNonQuery(sql, values);

    // Add to sync queue if offline
    if (!this.isOnline && this.config.enableOfflineSync) {
      await this.addToSyncQueue(table, 'create', record);
    }

    // Clear related cache entries
    this.invalidateTableCache(table);

    return record as T;
  }

  public async findById<T>(table: string, id: string): Promise<T | null> {
    const cacheKey = this.getCacheKey(table, id);

    // Try cache first
    const cached = await this.getFromCache<T>(cacheKey);
    if (cached) return cached;

    const sql = `SELECT * FROM ${table} WHERE id = ? LIMIT 1`;
    const results = await this.executeQuery<T>(sql, [id]);

    const record = results.length > 0 ? results[0] : null;

    // Cache the result
    if (record) {
      await this.setInCache(cacheKey, record);
    }

    return record;
  }

  public async findAll<T>(table: string, filters?: any): Promise<T[]> {
    const cacheKey = this.getCacheKey(table, undefined, filters);

    // Try cache first
    const cached = await this.getFromCache<T[]>(cacheKey);
    if (cached) return cached;

    let sql = `SELECT * FROM ${table}`;
    const params: any[] = [];

    if (filters) {
      const conditions: string[] = [];
      for (const [key, value] of Object.entries(filters)) {
        conditions.push(`${key} = ?`);
        params.push(value);
      }
      if (conditions.length > 0) {
        sql += ` WHERE ${conditions.join(' AND ')}`;
      }
    }

    const results = await this.executeQuery<T>(sql, params);

    // Cache the results
    await this.setInCache(cacheKey, results);

    return results;
  }

  public async update<T>(table: string, id: string, data: Partial<T>): Promise<T | null> {
    const now = this.getCurrentTimestamp();
    const updateData = { ...data, updated_at: now };

    const columns = Object.keys(updateData);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const values = [...Object.values(updateData), id];

    const sql = `UPDATE ${table} SET ${setClause} WHERE id = ?`;

    await this.executeNonQuery(sql, values);

    // Add to sync queue if offline
    if (!this.isOnline && this.config.enableOfflineSync) {
      await this.addToSyncQueue(table, 'update', { id, ...updateData });
    }

    // Clear related cache entries
    this.invalidateTableCache(table);

    // Return updated record
    return await this.findById<T>(table, id);
  }

  public async delete(table: string, id: string): Promise<boolean> {
    const sql = `DELETE FROM ${table} WHERE id = ?`;

    try {
      await this.executeNonQuery(sql, [id]);

      // Add to sync queue if offline
      if (!this.isOnline && this.config.enableOfflineSync) {
        await this.addToSyncQueue(table, 'delete', { id });
      }

      // Clear related cache entries
      this.invalidateTableCache(table);

      return true;
    } catch (error) {
      LoggingService.error(`Failed to delete record from ${table}`, 'MASTER_DB', error as Error);
      return false;
    }
  }

  public async search<T>(table: string, query: string, fields: string[]): Promise<T[]> {
    const conditions = fields.map(field => `${field} LIKE ?`).join(' OR ');
    const params = fields.map(() => `%${query}%`);

    const sql = `SELECT * FROM ${table} WHERE ${conditions}`;
    return await this.executeQuery<T>(sql, params);
  }

  private invalidateTableCache(table: string): void {
    const keysToDelete: string[] = [];
    for (const key of Array.from(this.cache.keys())) {
      if (key.startsWith(`${table}:`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // ============================================================================
  // SYNC SYSTEM
  // ============================================================================

  private async addToSyncQueue(table: string, action: 'create' | 'update' | 'delete', data: any): Promise<void> {
    const syncItem: SyncQueueItem = {
      id: this.generateId(),
      table,
      action,
      data,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.syncQueue.push(syncItem);
    await this.persistSyncQueue();
  }

  private async persistSyncQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem('master_db_sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      LoggingService.warn('Failed to persist sync queue', 'MASTER_DB');
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    LoggingService.info(`Processing ${this.syncQueue.length} sync items`, 'MASTER_DB');

    const processedItems: string[] = [];

    for (const item of this.syncQueue) {
      try {
        await this.processSyncItem(item);
        processedItems.push(item.id);
      } catch (error) {
        item.retryCount++;
        if (item.retryCount >= this.config.maxRetries) {
          LoggingService.error(`Sync item failed after ${this.config.maxRetries} retries`, 'MASTER_DB', error as Error);
          processedItems.push(item.id); // Remove failed items after max retries
        }
      }
    }

    // Remove processed items from queue
    this.syncQueue = this.syncQueue.filter(item => !processedItems.includes(item.id));
    await this.persistSyncQueue();

    this.syncInProgress = false;
    LoggingService.info(`Sync completed. ${processedItems.length} items processed`, 'MASTER_DB');
  }

  private async processSyncItem(item: SyncQueueItem): Promise<void> {
    // In a real implementation, this would sync with a remote server
    // For now, we'll just log the sync operation
    LoggingService.info(`Syncing ${item.action} on ${item.table}`, 'MASTER_DB');

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  public setOnlineStatus(isOnline: boolean): void {
    this.isOnline = isOnline;
    if (isOnline && this.syncQueue.length > 0) {
      // Process sync queue when coming back online
      setTimeout(() => this.processSyncQueue(), 1000);
    }
  }

  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================

  private async performHealthCheck(): Promise<void> {
    try {
      // Check database connection
      if (this.database) {
        await this.testConnection();
        this.health.connectionCount++;
      }

      // Check cache health
      const cacheHitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0;
      this.health.performanceMetrics.cacheHitRate = cacheHitRate;

      // Update uptime
      this.health.uptime = Date.now() - this.startTime;

      // Check sync queue health
      const pendingSyncItems = this.syncQueue.length;

      LoggingService.debug(`Health check: Connected=${this.health.isConnected}, Cache hit rate=${cacheHitRate.toFixed(2)}, Pending sync=${pendingSyncItems}`, 'MASTER_DB');

    } catch (error) {
      this.health.lastError = (error as Error).message;
      LoggingService.error('Health check failed', 'MASTER_DB', error as Error);
    }
  }

  public getHealth(): DatabaseHealth {
    return { ...this.health };
  }

  public getCacheStats() {
    const hitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0;
    return {
      ...this.cacheStats,
      hitRate,
      size: this.cache.size,
      maxSize: this.config.cacheSize,
    };
  }

  // ============================================================================
  // BACKUP SYSTEM
  // ============================================================================

  private async createAutoBackup(): Promise<void> {
    try {
      const backupId = await this.createBackup();
      LoggingService.info(`Auto backup created: ${backupId}`, 'MASTER_DB');
    } catch (error) {
      LoggingService.error('Auto backup failed', 'MASTER_DB', error as Error);
    }
  }

  public async createBackup(): Promise<string> {
    const backupId = `backup_${Date.now()}`;
    const tables = ['staff', 'customers', 'products', 'inventory', 'orders', 'order_items', 'measurements', 'appointments', 'payments', 'notifications', 'audit_logs', 'settings'];

    const backupData: { [table: string]: any[] } = {};
    let totalRecords = 0;

    for (const table of tables) {
      try {
        const data = await this.findAll(table);
        backupData[table] = data;
        totalRecords += data.length;
      } catch (error) {
        LoggingService.warn(`Failed to backup table ${table}`, 'MASTER_DB');
        backupData[table] = [];
      }
    }

    const backup = {
      id: backupId,
      timestamp: Date.now(),
      data: backupData,
      metadata: {
        version: '1.0',
        totalRecords,
        tables: tables.length,
      }
    };

    await AsyncStorage.setItem(`backup_${backupId}`, JSON.stringify(backup));

    this.lastBackup = {
      id: backupId,
      timestamp: Date.now(),
      size: JSON.stringify(backup).length,
      tables,
      recordCount: totalRecords,
    };

    return backupId;
  }

  public async restoreBackup(backupId: string): Promise<void> {
    try {
      const backupData = await AsyncStorage.getItem(`backup_${backupId}`);
      if (!backupData) {
        throw new Error(`Backup ${backupId} not found`);
      }

      const backup = JSON.parse(backupData);

      // Clear existing data
      for (const table of Object.keys(backup.data)) {
        await this.executeNonQuery(`DELETE FROM ${table}`, []);
      }

      // Restore data
      for (const [table, records] of Object.entries(backup.data)) {
        for (const record of records as any[]) {
          await this.create(table, record);
        }
      }

      // Clear cache after restore
      this.clearCache();

      LoggingService.info(`Backup ${backupId} restored successfully`, 'MASTER_DB');

    } catch (error) {
      LoggingService.error(`Failed to restore backup ${backupId}`, 'MASTER_DB', error as Error);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  public async getTableStats(): Promise<{ [table: string]: number }> {
    const tables = ['staff', 'customers', 'products', 'inventory', 'orders', 'order_items', 'measurements', 'appointments', 'payments', 'notifications', 'audit_logs', 'settings'];
    const stats: { [table: string]: number } = {};

    for (const table of tables) {
      try {
        const result = await this.executeQuery<{ count: number }>(`SELECT COUNT(*) as count FROM ${table}`, []);
        stats[table] = result[0]?.count || 0;
      } catch (error) {
        stats[table] = 0;
      }
    }

    return stats;
  }

  public async clearAllData(): Promise<void> {
    const tables = ['order_items', 'orders', 'payments', 'appointments', 'measurements', 'inventory', 'products', 'customers', 'staff', 'notifications', 'audit_logs', 'settings'];

    for (const table of tables) {
      await this.executeNonQuery(`DELETE FROM ${table}`, []);
    }

    this.clearCache();
    this.syncQueue = [];
    await this.persistSyncQueue();

    LoggingService.info('All data cleared', 'MASTER_DB');
  }

  public async resetDatabase(): Promise<void> {
    if (!this.database) return;

    try {
      // Drop all tables
      const tables = ['order_items', 'orders', 'payments', 'appointments', 'measurements', 'inventory', 'products', 'customers', 'staff', 'notifications', 'audit_logs', 'settings'];

      for (const table of tables) {
        await this.database.execAsync(`DROP TABLE IF EXISTS ${table}`);
      }

      // Recreate all tables
      await this.createTables();

      LoggingService.info('Database reset successfully', 'MASTER_DB');
    } catch (error) {
      LoggingService.error('Failed to reset database', 'MASTER_DB', error as Error);
      throw error;
    }
  }

  public async destroy(): Promise<void> {
    // Clear timers
    if (this.syncTimer) {
      clearInterval(this.syncTimer as any);
      this.syncTimer = null;
    }
    if (this.backupTimer) {
      clearInterval(this.backupTimer as any);
      this.backupTimer = null;
    }

    // Clear cache
    this.clearCache();

    // Close database
    if (this.database) {
      // Note: expo-sqlite doesn't have a close method in the new API
      this.database = null;
    }

    this.isInitialized = false;
    LoggingService.info('Master Database Service destroyed', 'MASTER_DB');
  }

  // ============================================================================
  // ENTITY-SPECIFIC METHODS
  // ============================================================================

  // Customer methods
  public async createCustomer(data: Partial<Customer>): Promise<Customer> {
    return await this.create<Customer>('customers', data);
  }

  public async getCustomers(filters?: any): Promise<Customer[]> {
    return await this.findAll<Customer>('customers', filters);
  }

  public async getCustomerById(id: string): Promise<Customer | null> {
    return await this.findById<Customer>('customers', id);
  }

  public async updateCustomer(id: string, data: Partial<Customer>): Promise<Customer | null> {
    return await this.update<Customer>('customers', id, data);
  }

  public async searchCustomers(query: string): Promise<Customer[]> {
    return await this.search<Customer>('customers', query, ['name', 'email', 'phone']);
  }

  // Product methods
  public async createProduct(data: Partial<Product>): Promise<Product> {
    return await this.create<Product>('products', data);
  }

  public async getProducts(filters?: any): Promise<Product[]> {
    return await this.findAll<Product>('products', filters);
  }

  public async getProductById(id: string): Promise<Product | null> {
    return await this.findById<Product>('products', id);
  }

  public async updateProduct(id: string, data: Partial<Product>): Promise<Product | null> {
    return await this.update<Product>('products', id, data);
  }

  public async searchProducts(query: string): Promise<Product[]> {
    return await this.search<Product>('products', query, ['name', 'description', 'category', 'sku']);
  }

  // Order methods
  public async createOrder(data: Partial<Order>): Promise<Order> {
    return await this.create<Order>('orders', data);
  }

  public async getOrders(filters?: any): Promise<Order[]> {
    return await this.findAll<Order>('orders', filters);
  }

  public async getOrderById(id: string): Promise<Order | null> {
    return await this.findById<Order>('orders', id);
  }

  public async updateOrder(id: string, data: Partial<Order>): Promise<Order | null> {
    return await this.update<Order>('orders', id, data);
  }

  public async getOrderWithItems(orderId: string): Promise<{ order: Order | null; items: OrderItem[] }> {
    const order = await this.getOrderById(orderId);
    const items = await this.findAll<OrderItem>('order_items', { order_id: orderId });
    return { order, items };
  }

  // Order Item methods
  public async createOrderItem(data: Partial<OrderItem>): Promise<OrderItem> {
    return await this.create<OrderItem>('order_items', data);
  }

  public async getOrderItems(orderId: string): Promise<OrderItem[]> {
    return await this.findAll<OrderItem>('order_items', { order_id: orderId });
  }

  // Staff methods
  public async createStaff(data: Partial<Staff>): Promise<Staff> {
    return await this.create<Staff>('staff', data);
  }

  public async getStaff(filters?: any): Promise<Staff[]> {
    return await this.findAll<Staff>('staff', filters);
  }

  public async getStaffById(id: string): Promise<Staff | null> {
    return await this.findById<Staff>('staff', id);
  }

  public async updateStaff(id: string, data: Partial<Staff>): Promise<Staff | null> {
    return await this.update<Staff>('staff', id, data);
  }

  public async getStaffPerformance(staffId: string): Promise<any | null> {
    // Placeholder: In a real app, fetch from a dedicated performance table or calculate
    // For now, return dummy data or null
    return {
      qualityRating: Math.random() * 5, // 0-5
      customerSatisfactionRating: Math.random() * 5, // 0-5
      averageCompletionTime: Math.floor(Math.random() * 24) + 1, // 1-24 hours
      totalOrdersCompleted: Math.floor(Math.random() * 100),
      ordersCompleted: Math.floor(Math.random() * 50),
      onTimeDeliveryRate: Math.random() * 100, // 0-100%
      reworkRate: Math.random() * 10, // 0-10%
      goals: [],
      lastUpdated: this.getCurrentTimestamp(),
    };
  }

  public async getStaffWorkload(staffId: string): Promise<any | null> {
    // Placeholder: In a real app, fetch from a dedicated workload table or calculate
    // For now, return dummy data or null
    return {
      currentOrders: Math.floor(Math.random() * 10),
      maxCapacity: 10,
      efficiency: Math.random() * 100, // 0-100%
      availableHours: 40,
      scheduledHours: Math.floor(Math.random() * 40),
      overtimeHours: Math.floor(Math.random() * 10),
      capacity: Math.random(), // 0-1
      lastUpdated: this.getCurrentTimestamp(),
    };
  }

  

  // Inventory methods
  public async createInventoryItem(data: Partial<InventoryItem>): Promise<InventoryItem> {
    return await this.create<InventoryItem>('inventory', data);
  }

  public async getInventoryItems(filters?: any): Promise<InventoryItem[]> {
    return await this.findAll<InventoryItem>('inventory', filters);
  }

  public async getInventoryItemById(id: string): Promise<InventoryItem | null> {
    return await this.findById<InventoryItem>('inventory', id);
  }

  public async updateInventoryItem(id: string, data: Partial<InventoryItem>): Promise<InventoryItem | null> {
    return await this.update<InventoryItem>('inventory', id, data);
  }

  public async getInventoryStockByItem(itemId: string): Promise<InventoryStock[]> {
    return await this.findAll<InventoryStock>('inventory_stock', { item_id: itemId });
  }

  // Warehouse methods
  public async createWarehouse(data: Partial<Warehouse>): Promise<Warehouse> {
    return await this.create<Warehouse>('warehouses', data);
  }

  public async getWarehouses(filters?: any): Promise<Warehouse[]> {
    return await this.findAll<Warehouse>('warehouses', filters);
  }

  public async getWarehouseById(id: string): Promise<Warehouse | null> {
    return await this.findById<Warehouse>('warehouses', id);
  }

  public async updateWarehouse(id: string, data: Partial<Warehouse>): Promise<Warehouse | null> {
    return await this.update<Warehouse>('warehouses', id, data);
  }

  public async deleteWarehouse(id: string): Promise<void> {
    await this.delete('warehouses', id);
  }

  public async deleteInventoryItem(id: string): Promise<void> {
    await this.delete('inventory_items', id);
  }

  // Inventory Transaction methods
  public async createInventoryTransaction(data: Partial<InventoryTransaction>): Promise<InventoryTransaction> {
    return await this.create<InventoryTransaction>('inventory_transactions', data);
  }

  public async getInventoryTransactions(filters?: any): Promise<InventoryTransaction[]> {
    return await this.findAll<InventoryTransaction>('inventory_transactions', filters);
  }

  public async getInventoryStockByWarehouse(warehouseId: string): Promise<InventoryStock[]> {
    return await this.findAll<InventoryStock>('inventory_stock', { warehouse_id: warehouseId });
  }

  // Measurement methods
  public async createMeasurement(data: Partial<Measurement>): Promise<Measurement> {
    return await this.create<Measurement>('measurements', data);
  }

  public async getMeasurements(filters?: any): Promise<Measurement[]> {
    return await this.findAll<Measurement>('measurements', filters);
  }

  public async getCustomerMeasurements(customerId: string): Promise<Measurement[]> {
    return await this.findAll<Measurement>('measurements', { customer_id: customerId });
  }

  // Appointment methods
  public async createAppointment(data: Partial<Appointment>): Promise<Appointment> {
    return await this.create<Appointment>('appointments', data);
  }

  public async getAppointments(filters?: any): Promise<Appointment[]> {
    return await this.findAll<Appointment>('appointments', filters);
  }

  public async getAppointmentById(id: string): Promise<Appointment | null> {
    return await this.findById<Appointment>('appointments', id);
  }

  public async updateAppointment(id: string, data: Partial<Appointment>): Promise<Appointment | null> {
    return await this.update<Appointment>('appointments', id, data);
  }

  // Payment methods
  public async createPayment(data: Partial<Payment>): Promise<Payment> {
    return await this.create<Payment>('payments', data);
  }

  public async getPayments(filters?: any): Promise<Payment[]> {
    return await this.findAll<Payment>('payments', filters);
  }

  public async getOrderPayments(orderId: string): Promise<Payment[]> {
    return await this.findAll<Payment>('payments', { order_id: orderId });
  }

  // Notification methods
  public async createNotification(data: Partial<Notification>): Promise<Notification> {
    return await this.create<Notification>('notifications', data);
  }

  public async getNotifications(filters?: any): Promise<Notification[]> {
    return await this.findAll<Notification>('notifications', filters);
  }

  public async markNotificationAsRead(id: string): Promise<Notification | null> {
    return await this.update<Notification>('notifications', id, { isRead: true });
  }

  // Settings methods
  public async setSetting(category: string, key: string, value: any): Promise<void> {
    const existing = await this.findAll<AppSetting>('settings', { category, key });

    const isJson = typeof value === 'object' && value !== null;
    const storedValue = isJson ? JSON.stringify(value) : String(value);

    if (existing.length > 0) {
      await this.update<AppSetting>('settings', existing[0].id, {
        value: storedValue,
        isJson,
      });
    } else {
      await this.create<AppSetting>('settings', {
        category,
        key,
        value: storedValue,
        isJson,
      });
    }
  }

  public async getSetting(category: string, key: string): Promise<any> {
    const settings = await this.findAll<AppSetting>('settings', { category, key });
    if (settings.length > 0) {
      if (settings[0].isJson) {
        try {
          return JSON.parse(settings[0].value);
        } catch {
          return null;
        }
      }
      return settings[0].value;
    }
    return null;
  }

  public async getSettingsByCategory(category: string): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    const settings = await this.findAll<AppSetting>('settings', { category });

    for (const setting of settings) {
      try {
        result[setting.key] = setting.isJson ? JSON.parse(setting.value) : setting.value;
      } catch {
        result[setting.key] = setting.value;
      }
    }

    return result;
  }
}

// Create and export singleton instance
const instance = MasterDatabaseService.getInstance();
export default instance;
