import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useEffect,
  ReactNode,
} from 'react';

import { FinancialService } from '../services/financialService';
import LoggingService from '../services/LoggingService';
import { Order } from '../types/business';

// Financial data interfaces
interface Expense {
  id: string;
  category: string;
  amount: number;
  description: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

interface CashReconciliation {
  id: string;
  date: string;
  expectedCash: number;
  actualCash: number;
  difference: number;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProfitLossData {
  revenue: number;
  expenses: number;
  grossProfit: number;
  netProfit: number;
  profitMargin: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

interface PaymentAnalytics {
  totalTransactions: number;
  totalAmount: number;
  paymentMethods: Record<
    string,
    {
      count: number;
      amount: number;
      percentage: number;
    }
  >;
  trends: Array<{
    date: string;
    amount: number;
    method: string;
  }>;
}

interface TaxSummary {
  totalTaxableAmount: number;
  totalTaxCollected: number;
  taxRate: number;
  period: {
    startDate: string;
    endDate: string;
  };
  breakdown: Record<
    string,
    {
      amount: number;
      tax: number;
    }
  >;
}

interface DerivedData {
  totalExpenses: number;
  expensesByCategory: Record<string, number>;
  recentReconciliations: CashReconciliation[];
  reconciliationTrend: Array<{
    date: string;
    difference: number;
    status: string;
  }>;
}

// State interface
interface FinancialState {
  expenses: Expense[];
  reconciliations: CashReconciliation[];
  profitLossData: ProfitLossData | null;
  paymentAnalytics: PaymentAnalytics | null;
  taxSummary: TaxSummary | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Action types
enum ActionTypes {
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  SET_EXPENSES = 'SET_EXPENSES',
  ADD_EXPENSE = 'ADD_EXPENSE',
  UPDATE_EXPENSE = 'UPDATE_EXPENSE',
  DELETE_EXPENSE = 'DELETE_EXPENSE',
  SET_RECONCILIATIONS = 'SET_RECONCILIATIONS',
  ADD_RECONCILIATION = 'ADD_RECONCILIATION',
  SET_PROFIT_LOSS = 'SET_PROFIT_LOSS',
  SET_PAYMENT_ANALYTICS = 'SET_PAYMENT_ANALYTICS',
  SET_TAX_SUMMARY = 'SET_TAX_SUMMARY',
  CLEAR_DATA = 'CLEAR_DATA',
}

// Action interfaces
interface SetLoadingAction {
  type: ActionTypes.SET_LOADING;
  payload: boolean;
}

interface SetErrorAction {
  type: ActionTypes.SET_ERROR;
  payload: string | null;
}

interface SetExpensesAction {
  type: ActionTypes.SET_EXPENSES;
  payload: Expense[];
}

interface AddExpenseAction {
  type: ActionTypes.ADD_EXPENSE;
  payload: Expense;
}

interface UpdateExpenseAction {
  type: ActionTypes.UPDATE_EXPENSE;
  payload: Expense;
}

interface DeleteExpenseAction {
  type: ActionTypes.DELETE_EXPENSE;
  payload: string;
}

interface SetReconciliationsAction {
  type: ActionTypes.SET_RECONCILIATIONS;
  payload: CashReconciliation[];
}

interface AddReconciliationAction {
  type: ActionTypes.ADD_RECONCILIATION;
  payload: CashReconciliation;
}

interface SetProfitLossAction {
  type: ActionTypes.SET_PROFIT_LOSS;
  payload: ProfitLossData;
}

interface SetPaymentAnalyticsAction {
  type: ActionTypes.SET_PAYMENT_ANALYTICS;
  payload: PaymentAnalytics;
}

interface SetTaxSummaryAction {
  type: ActionTypes.SET_TAX_SUMMARY;
  payload: TaxSummary;
}

interface ClearDataAction {
  type: ActionTypes.CLEAR_DATA;
}

type FinancialAction =
  | SetLoadingAction
  | SetErrorAction
  | SetExpensesAction
  | AddExpenseAction
  | UpdateExpenseAction
  | DeleteExpenseAction
  | SetReconciliationsAction
  | AddReconciliationAction
  | SetProfitLossAction
  | SetPaymentAnalyticsAction
  | SetTaxSummaryAction
  | ClearDataAction;

// Initial state
const initialState: FinancialState = {
  expenses: [],
  reconciliations: [],
  profitLossData: null,
  paymentAnalytics: null,
  taxSummary: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

// Reducer
const financialReducer = (state: FinancialState, action: FinancialAction): FinancialState => {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };

    case ActionTypes.SET_EXPENSES:
      return {
        ...state,
        expenses: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.ADD_EXPENSE:
      return {
        ...state,
        expenses: [action.payload, ...(state.expenses || [])],
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.UPDATE_EXPENSE:
      return {
        ...state,
        expenses: (state.expenses || []).map(expense =>
          expense.id === action.payload.id ? action.payload : expense
        ),
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.DELETE_EXPENSE:
      return {
        ...state,
        expenses: (state.expenses || []).filter(expense => expense.id !== action.payload),
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.SET_RECONCILIATIONS:
      return {
        ...state,
        reconciliations: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.ADD_RECONCILIATION:
      return {
        ...state,
        reconciliations: [action.payload, ...(state.reconciliations || [])],
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.SET_PROFIT_LOSS:
      return {
        ...state,
        profitLossData: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.SET_PAYMENT_ANALYTICS:
      return {
        ...state,
        paymentAnalytics: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.SET_TAX_SUMMARY:
      return {
        ...state,
        taxSummary: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };

    case ActionTypes.CLEAR_DATA:
      return { ...initialState };

    default:
      return state;
  }
};

// Context interface
interface FinancialContextType extends FinancialState {
  derivedData: DerivedData;

  // Expense management
  loadExpenses: (filters?: Record<string, any>) => Promise<void>;
  addExpense: (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Expense>;
  updateExpense: (id: string, updates: Partial<Expense>) => Promise<Expense>;
  deleteExpense: (id: string) => Promise<void>;

  // Cash reconciliation
  loadReconciliations: (filters?: Record<string, any>) => Promise<void>;
  performReconciliation: (
    reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<CashReconciliation>;
  calculateDailyCashExpected: (date: string) => Promise<number>;

  // Financial reports
  generateProfitLossStatement: (startDate: string, endDate: string) => Promise<ProfitLossData>;
  getPaymentMethodAnalytics: (orders: Order[], startDate: string, endDate: string) => Promise<PaymentAnalytics>;
  getTaxSummary: (orders: Order[], startDate: string, endDate: string) => Promise<TaxSummary>;

  // Utilities
  clearError: () => void;
  clearData: () => void;
}

// Context
const FinancialContext = createContext<FinancialContextType | undefined>(undefined);

// Provider component
interface FinancialProviderProps {
  children: ReactNode;
  orders: Order[];
}

export const FinancialProvider: React.FC<FinancialProviderProps> = ({ children, orders }) => {
  const [state, dispatch] = useReducer(financialReducer, initialState);

  // Expense management
  const loadExpenses = useCallback(async (filters: Record<string, any> = {}): Promise<void> => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const expenses = await FinancialService.getExpenses(filters);

      // Validate expenses array
      if (Array.isArray(expenses)) {
        dispatch({ type: ActionTypes.SET_EXPENSES, payload: expenses });
      } else {
        LoggingService.warn('Invalid expenses data received, using empty array', 'FINANCIAL');
        dispatch({ type: ActionTypes.SET_EXPENSES, payload: [] });
      }
    } catch (error) {
      LoggingService.error('Failed to load expenses', 'FINANCIAL', error as Error);
      dispatch({
        type: ActionTypes.SET_ERROR,
        payload: (error as Error).message || 'Failed to load expenses',
      });
    }
  }, []);

  const addExpense = useCallback(
    async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const newExpense = await FinancialService.addExpense(expenseData);
        dispatch({ type: ActionTypes.ADD_EXPENSE, payload: newExpense });
        return newExpense;
      } catch (error) {
        LoggingService.error('Failed to add expense', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    []
  );

  const updateExpense = useCallback(
    async (id: string, updates: Partial<Expense>): Promise<Expense> => {
      try {
        const updatedExpense = await FinancialService.updateExpense(id, updates);
        dispatch({ type: ActionTypes.UPDATE_EXPENSE, payload: updatedExpense });
        return updatedExpense;
      } catch (error) {
        LoggingService.error('Failed to update expense', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    []
  );

  const deleteExpense = useCallback(async (id: string): Promise<void> => {
    try {
      await FinancialService.deleteExpense(id);
      dispatch({ type: ActionTypes.DELETE_EXPENSE, payload: id });
    } catch (error) {
      LoggingService.error('Failed to delete expense', 'FINANCIAL', error as Error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
      throw error;
    }
  }, []);

  // Cash reconciliation
  const loadReconciliations = useCallback(
    async (filters: Record<string, any> = {}): Promise<void> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const reconciliations = await FinancialService.getCashReconciliations(filters);
        dispatch({ type: ActionTypes.SET_RECONCILIATIONS, payload: reconciliations });
      } catch (error) {
        LoggingService.error('Failed to load reconciliations', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
      }
    },
    []
  );

  const performReconciliation = useCallback(
    async (
      reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>
    ): Promise<CashReconciliation> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const newReconciliation =
          await FinancialService.performCashReconciliation(reconciliationData);
        dispatch({ type: ActionTypes.ADD_RECONCILIATION, payload: newReconciliation });
        return newReconciliation;
      } catch (error) {
        LoggingService.error('Failed to perform reconciliation', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    []
  );

  const calculateDailyCashExpected = useCallback(async (date: string): Promise<number> => {
    try {
      return await FinancialService.calculateDailyCashExpected(date);
    } catch (error) {
      LoggingService.error('Failed to calculate daily cash expected', 'FINANCIAL', error as Error);
      throw error;
    }
  }, []);

  // Financial reports
  const generateProfitLossStatement = useCallback(
    async (startDate: string, endDate: string): Promise<ProfitLossData> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const profitLoss = await FinancialService.generateProfitLossStatement(orders, startDate, endDate);
        dispatch({ type: ActionTypes.SET_PROFIT_LOSS, payload: profitLoss });
        return profitLoss;
      } catch (error) {
        LoggingService.error('Failed to generate P&L statement', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    [orders]
  );

  const getPaymentMethodAnalytics = useCallback(
    async (startDate: string, endDate: string): Promise<PaymentAnalytics> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const analytics = await FinancialService.getPaymentMethodAnalytics(orders, startDate, endDate);
        dispatch({ type: ActionTypes.SET_PAYMENT_ANALYTICS, payload: analytics });
        return analytics;
      } catch (error) {
        LoggingService.error('Failed to get payment analytics', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    [orders]
  );

  const getTaxSummary = useCallback(
    async (startDate: string, endDate: string): Promise<TaxSummary> => {
      try {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const taxSummary = await FinancialService.getTaxSummary(orders, startDate, endDate);
        dispatch({ type: ActionTypes.SET_TAX_SUMMARY, payload: taxSummary });
        return taxSummary;
      } catch (error) {
        LoggingService.error('Failed to get tax summary', 'FINANCIAL', error as Error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: (error as Error).message });
        throw error;
      }
    },
    [orders]
  );

  // Utility functions
  const clearError = useCallback((): void => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: null });
  }, []);

  const clearData = useCallback((): void => {
    dispatch({ type: ActionTypes.CLEAR_DATA });
  }, []);

  // Calculate derived data
  const getDerivedData = useCallback((): DerivedData => {
    const totalExpenses = state.expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expensesByCategory = state.expenses.reduce(
      (acc, expense) => {
        acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
        return acc;
      },
      {} as Record<string, number>
    );

    const recentReconciliations = state.reconciliations.slice(0, 5);
    const reconciliationTrend = state.reconciliations.map(r => ({
      date: r.date,
      difference: r.difference,
      status: r.status,
    }));

    return {
      totalExpenses,
      expensesByCategory,
      recentReconciliations,
      reconciliationTrend,
    };
  }, [state.expenses, state.reconciliations]);

  // Load initial data on mount
  useEffect(() => {
    loadExpenses();
    loadReconciliations();
  }, [loadExpenses, loadReconciliations]);

  const value: FinancialContextType = {
    // State
    ...state,
    derivedData: getDerivedData(),

    // Expense management
    loadExpenses,
    addExpense,
    updateExpense,
    deleteExpense,

    // Cash reconciliation
    loadReconciliations,
    performReconciliation,
    calculateDailyCashExpected,

    // Financial reports
    generateProfitLossStatement,
    getPaymentMethodAnalytics: (orders: Order[], startDate: string, endDate: string) => getPaymentMethodAnalytics(startDate, endDate),
    getTaxSummary: (orders: Order[], startDate: string, endDate: string) => getTaxSummary(startDate, endDate),

    // Utilities
    clearError,
    clearData,
  };

  return <FinancialContext.Provider value={value}>{children}</FinancialContext.Provider>;
};

// Hook to use financial context
export const useFinancial = (): FinancialContextType => {
  const context = useContext(FinancialContext);
  if (!context) {
    throw new Error('useFinancial must be used within a FinancialProvider');
  }
  return context;
};

export default FinancialContext;
