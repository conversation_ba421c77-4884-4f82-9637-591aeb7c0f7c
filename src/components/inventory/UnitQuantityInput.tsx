/**
 * Unit Quantity Input Component
 * Provides quantity input with unit selection and real-time conversion
 */

import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { TextInput, Text, Menu, Button, HelperText } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

interface UnitQuantityInputProps {
  value: number;
  unit: string;
  availableUnits: string[];
  onQuantityChange: (quantity: number) => void;
  onUnitChange: (unit: string) => void;
  showConversion?: boolean;
  baseUnit?: string;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  style?: ViewStyle;
  precision?: number;
  minValue?: number;
  maxValue?: number;
}

const UnitQuantityInput: React.FC<UnitQuantityInputProps> = ({
  value,
  unit,
  availableUnits,
  onQuantityChange,
  onUnitChange,
  showConversion = true,
  baseUnit = 'meter',
  label = 'Quantity',
  placeholder = 'Enter quantity',
  error,
  disabled = false,
  style,
  precision = 2,
  minValue = 0,
  maxValue,
}) => {
  const theme = useTheme();
  const [inputValue, setInputValue] = useState<string>(value.toString());
  const [showUnitMenu, setShowUnitMenu] = useState<boolean>(false);
  const [conversionText, setConversionText] = useState<string>('');
  const [validationError, setValidationError] = useState<string>('');

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  // Update conversion text when value or unit changes
  useEffect(() => {
    if (showConversion && value > 0 && unit !== baseUnit) {
      try {
        const convertedValue = UnitConverter.convertBetweenUnits(value, unit, baseUnit);
        const formattedConversion = UnitConverter.formatQuantity(
          convertedValue,
          baseUnit,
          precision
        );
        setConversionText(`≈ ${formattedConversion}`);
      } catch (error) {
        setConversionText('');
        LoggingService.warn('Conversion failed', 'UNIT_QUANTITY_INPUT', error as Error);
      }
    } else {
      setConversionText('');
    }
  }, [value, unit, baseUnit, showConversion, precision]);

  // Handle quantity input change
  const handleQuantityChange = useCallback(
    (text: string) => {
      setInputValue(text);
      setValidationError('');

      // Parse and validate the input
      const numericValue = parseFloat(text);

      if (text === '' || text === '.') {
        // Allow empty input or just decimal point
        return;
      }

      if (isNaN(numericValue)) {
        setValidationError('Please enter a valid number');
        return;
      }

      if (numericValue < minValue) {
        setValidationError(`Value must be at least ${minValue}`);
        return;
      }

      if (maxValue !== undefined && numericValue > maxValue) {
        setValidationError(`Value must not exceed ${maxValue}`);
        return;
      }

      // Valid input - call the callback
      onQuantityChange(numericValue);
    },
    [onQuantityChange, minValue, maxValue]
  );

  // Handle unit selection
  const handleUnitSelect = useCallback(
    (selectedUnit: string) => {
      setShowUnitMenu(false);

      if (selectedUnit !== unit) {
        try {
          // Convert current value to new unit
          const convertedValue = UnitConverter.convertBetweenUnits(value, unit, selectedUnit);
          const roundedValue = UnitConverter.roundToPrecision(convertedValue, selectedUnit);

          onUnitChange(selectedUnit);
          onQuantityChange(roundedValue);

          LoggingService.debug(
            `Unit changed from ${unit} to ${selectedUnit}`,
            'UNIT_QUANTITY_INPUT'
          );
        } catch (error) {
          LoggingService.error('Unit conversion failed', 'UNIT_QUANTITY_INPUT', error as Error);
          // Still change the unit even if conversion fails
          onUnitChange(selectedUnit);
        }
      }
    },
    [unit, value, onUnitChange, onQuantityChange]
  );

  // Get display name for unit
  const getUnitDisplayName = useCallback((unitName: string): string => {
    return UnitConverter.getUnitDisplayName(unitName);
  }, []);

  // Get abbreviation for unit
  const getUnitAbbreviation = useCallback((unitName: string): string => {
    return UnitConverter.getUnitAbbreviation(unitName);
  }, []);

  const displayError = error || validationError;

  return (
    <View style={[styles.container, style]}>
      <View style={styles.inputRow}>
        {/* Quantity Input */}
        <View style={styles.quantityContainer}>
          <TextInput
            label={label}
            value={inputValue}
            onChangeText={handleQuantityChange}
            placeholder={placeholder}
            keyboardType='numeric'
            disabled={disabled}
            error={!!displayError}
            mode='outlined'
            style={styles.quantityInput}
            contentStyle={{
              color: theme.colors.onSurface,
            }}
            outlineStyle={{
              borderColor: displayError ? theme.colors.error : theme.colors.outline,
            }}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />
        </View>

        {/* Unit Selector */}
        <View style={styles.unitContainer}>
          <Menu
            visible={showUnitMenu}
            onDismiss={() => setShowUnitMenu(false)}
            anchor={
              <Button
                mode='outlined'
                onPress={() => setShowUnitMenu(true)}
                disabled={disabled || availableUnits.length <= 1}
                style={[
                  styles.unitButton,
                  {
                    borderColor: displayError ? theme.colors.error : theme.colors.outline,
                  },
                ]}
                contentStyle={styles.unitButtonContent}
                labelStyle={{
                  color: theme.colors.onSurface,
                  fontSize: 14,
                }}
                icon={() => (
                  <PhosphorIcon name='caret-down' size={16} color={theme.colors.onSurface} />
                )}
              >
                {getUnitAbbreviation(unit)}
              </Button>
            }
            contentStyle={{
              backgroundColor: theme.colors.surface,
            }}
          >
            {availableUnits.map(availableUnit => (
              <Menu.Item
                key={availableUnit}
                onPress={() => handleUnitSelect(availableUnit)}
                title={getUnitDisplayName(availableUnit)}
                titleStyle={{
                  color: availableUnit === unit ? theme.colors.primary : theme.colors.onSurface,
                  fontWeight: availableUnit === unit ? 'bold' : 'normal',
                }}
                leadingIcon={() => (
                  <Text
                    style={{
                      color: theme.colors.onSurfaceVariant,
                      fontSize: 12,
                      minWidth: 30,
                    }}
                  >
                    {getUnitAbbreviation(availableUnit)}
                  </Text>
                )}
              />
            ))}
          </Menu>
        </View>
      </View>

      {/* Conversion Display */}
      {showConversion && conversionText && (
        <View style={styles.conversionContainer}>
          <Text style={[styles.conversionText, { color: theme.colors.onSurfaceVariant }]}>
            {conversionText}
          </Text>
        </View>
      )}

      {/* Error Display */}
      {displayError && (
        <HelperText type='error' visible={!!displayError}>
          {displayError}
        </HelperText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  quantityContainer: {
    flex: 1,
  },
  quantityInput: {
    backgroundColor: 'transparent',
  },
  unitContainer: {
    minWidth: 80,
    marginTop: 8, // Align with TextInput
  },
  unitButton: {
    minWidth: 80,
    height: 56, // Match TextInput height
    justifyContent: 'center',
  },
  unitButtonContent: {
    height: 56,
    flexDirection: 'row-reverse', // Icon on right
  },
  conversionContainer: {
    marginTop: 4,
    paddingHorizontal: 12,
  },
  conversionText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default UnitQuantityInput;
