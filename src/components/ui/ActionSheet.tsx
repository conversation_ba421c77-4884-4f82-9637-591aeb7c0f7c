import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import {
  Animated,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  AccessibilityInfo,
  BackHandler,
  Alert,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  InteractionManager,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { BORDER_RADIUS, SPACING } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DEFINITIONS ---
export interface ActionSheetOption {
  text: string;
  onPress: () => void | Promise<void>;
  icon?: PhosphorIconName;
  style?: 'cancel' | 'primary' | 'destructive' | 'default';
  disabled?: boolean;
  isAction?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
}

export interface ActionSheetProps {
  visible: boolean;
  onDismiss: () => void;
  title: string;
  description?: string;
  options: ActionSheetOption[];
  showCancel?: boolean;
  cancelText?: string;
  closeOnBackdropPress?: boolean;
  closeOnBackButton?: boolean;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  onShow?: () => void;
  onHide?: () => void;
  preventBackdropPress?: boolean;
  customHeader?: React.ReactNode;
  customFooter?: React.ReactNode;
}

// --- HELPER HOOK ---
const useLatest = <T,>(value: T) => {
  const ref = useRef(value);
  useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref;
};

// --- SUB-COMPONENT FOR LIST ITEMS ---
interface OptionItemProps {
  option: ActionSheetOption;
  index: number;
  isLoading: boolean;
  onPress: (option: ActionSheetOption, index: number) => void;
}

const OptionItem: React.FC<OptionItemProps> = React.memo(
  ({ option, index, isLoading, onPress }) => {
    const theme = useTheme();

    const optionStyle = useMemo<StyleProp<ViewStyle>>(() => [
        styles.optionItem,
        {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.outlineVariant,
        },
        option.style === 'destructive' && { backgroundColor: theme.colors.errorContainer },
        option.style === 'primary' && { backgroundColor: theme.colors.primaryContainer },
        option.disabled && styles.optionItemDisabled,
    ], [theme, option.style, option.disabled]);

    const textStyle = useMemo<StyleProp<TextStyle>>(() => [
        styles.optionText,
        { color: theme.colors.onSurface },
        option.style === 'destructive' && { color: theme.colors.onErrorContainer },
        option.style === 'primary' && { color: theme.colors.onPrimaryContainer },
    ], [theme, option.style]);

    const iconColor = useMemo(() => {
        if (option.style === 'destructive') return theme.colors.onErrorContainer;
        if (option.style === 'primary') return theme.colors.onPrimaryContainer;
        return theme.colors.onSurface;
    }, [theme, option.style]);

    return (
      <TouchableOpacity
        style={optionStyle}
        onPress={() => onPress(option, index)}
        disabled={option.disabled || isLoading}
        accessibilityLabel={option.accessibilityLabel || option.text}
        accessibilityHint={option.accessibilityHint}
        testID={option.testID || `option-${index}`}
        accessibilityRole="button"
      >
        {option.icon && (
          <PhosphorIcon
            name={option.icon}
            color={iconColor}
            size={20}
            weight="bold"
            style={styles.optionIcon}
          />
        )}
        <Text style={textStyle} numberOfLines={1}>
          {option.text}
        </Text>
        {isLoading && (
          <ActivityIndicator
            size="small"
            color={theme.colors.primary}
            style={styles.optionLoadingIndicator}
          />
        )}
      </TouchableOpacity>
    );
  }
);

// --- MAIN ACTIONSHEET COMPONENT ---
const ActionSheet: React.FC<ActionSheetProps> = ({
  visible,
  onDismiss,
  title,
  description,
  options = [],
  showCancel = true,
  cancelText = 'Cancel',
  closeOnBackdropPress = true,
  closeOnBackButton = true,
  testID = 'action-sheet',
  accessibilityLabel,
  accessibilityHint,
  onShow,
  onHide,
  preventBackdropPress = false,
  customHeader,
  customFooter,
}) => {
  const theme = useTheme();
  const animation = useRef(new Animated.Value(0)).current;
  const [isLoading, setIsLoading] = useState<{ [key: number]: boolean }>({});
  const latestOnDismiss = useLatest(onDismiss);

  // Animate the sheet in and out
  useEffect(() => {
    if (visible) {
      Animated.spring(animation, {
        toValue: 1,
        useNativeDriver: true,
        bounciness: 4,
        speed: 12,
      }).start(() => {
        onShow?.();
        AccessibilityInfo.announceForAccessibility?.(`${title} action sheet opened.`);
      });
    } else {
      Animated.timing(animation, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start(onHide);
    }
  }, [visible, animation, title, onShow, onHide]);
  
  // Handle hardware back button press
  useEffect(() => {
    if (!visible || !closeOnBackButton) return;
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      latestOnDismiss.current();
      return true; // Prevents default behavior
    });
    return () => backHandler.remove();
  }, [visible, closeOnBackButton, latestOnDismiss]);

  const validatedOptions = useMemo(() => {
    if (!Array.isArray(options)) return [];
    return options.filter(opt => opt && typeof opt.text === 'string' && typeof opt.onPress === 'function');
  }, [options]);

  const handleOptionPress = useCallback(async (option: ActionSheetOption, index: number) => {
    if (isLoading[index]) return;
    setIsLoading(prev => ({ ...prev, [index]: true }));
    try {
      await Promise.resolve(option.onPress());
    } catch (error) {
      LoggingService.error(`ActionSheet option "${option.text}" failed.`, 'UI', error as Error);
      Alert.alert('Action Failed', 'The requested action could not be completed.');
    } finally {
      setIsLoading(prev => ({ ...prev, [index]: false }));
      if (option.isAction !== false) {
        InteractionManager.runAfterInteractions(() => latestOnDismiss.current());
      }
    }
  }, [isLoading, latestOnDismiss]);

  if (!visible) return null;

  const screenHeight = Dimensions.get('window').height;

  return (
    <Modal transparent visible onRequestClose={onDismiss} animationType="none">
      <View style={styles.backdrop}>
        <TouchableOpacity
          style={styles.flex1}
          onPress={closeOnBackdropPress && !preventBackdropPress ? onDismiss : undefined}
          disabled={preventBackdropPress}
        />
        <Animated.View
          style={[
            styles.actionSheetContainer,
            {
              backgroundColor: theme.colors.surface,
              transform: [{ translateY: animation.interpolate({ inputRange: [0, 1], outputRange: [screenHeight, 0] }) }],
              maxHeight: screenHeight * 0.8,
            },
          ]}
          testID={testID}
          accessibilityLabel={accessibilityLabel}
          accessibilityHint={accessibilityHint}
          accessibilityViewIsModal
        >
          {customHeader || (
            <View style={[styles.header, { borderBottomColor: theme.colors.outlineVariant }]}>
              <Text style={[styles.title, { color: theme.colors.onSurface }]}>{title}</Text>
              {description && <Text style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>{description}</Text>}
            </View>
          )}

          <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
            {validatedOptions.map((option, index) => (
              <OptionItem
                key={`${option.text}-${index}`}
                option={option}
                index={index}
                isLoading={!!isLoading[index]}
                onPress={handleOptionPress}
              />
            ))}
          </ScrollView>

          {showCancel && (
            <TouchableOpacity
              style={[styles.cancelButton, { borderTopColor: theme.colors.outlineVariant }]}
              onPress={onDismiss}
            >
              <Text style={[styles.cancelButtonText, { color: theme.colors.primary }]}>{cancelText}</Text>
            </TouchableOpacity>
          )}
          {customFooter}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: { flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.5)', justifyContent: 'flex-end' },
  flex1: { flex: 1 },
  actionSheetContainer: { borderTopLeftRadius: BORDER_RADIUS.xl, borderTopRightRadius: BORDER_RADIUS.xl, overflow: 'hidden' },
  header: { paddingHorizontal: SPACING.lg, paddingTop: SPACING.lg, paddingBottom: SPACING.md, borderBottomWidth: StyleSheet.hairlineWidth },
  title: { fontSize: 20, fontWeight: 'bold', textAlign: 'center' },
  description: { fontSize: 14, textAlign: 'center', marginTop: SPACING.xs },
  optionsContainer: { flexShrink: 1 },
  cancelButton: { paddingVertical: SPACING.md, alignItems: 'center', borderTopWidth: 6, marginTop: SPACING.sm },
  cancelButtonText: { fontSize: 16, fontWeight: 'bold' },
  // Styles for OptionItem
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  optionItemDisabled: {
    opacity: 0.6,
  },
  optionIcon: {
    marginRight: SPACING.md,
  },
  optionText: {
    fontSize: 16,
    flex: 1,
  },
  optionLoadingIndicator: {
    marginLeft: SPACING.md,
  },
});

export default ActionSheet;