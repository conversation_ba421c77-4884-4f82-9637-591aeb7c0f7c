import { Product, Order, Customer, Staff, OrderItem, CustomerMeasurement, Outlet } from './index';
import { Warehouse } from './inventory';

// Settings interfaces
interface StoreHours {
  open: string;
  close: string;
  closed: boolean;
}

interface PaymentMethod {
  enabled: boolean;
  processingFee: number;
}

export interface Settings {
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  taxRate: number;
  currency: string;
  notifications: boolean;
  darkMode: boolean;
  autoBackup: boolean;
  storeHours: {
    monday: StoreHours;
    tuesday: StoreHours;
    wednesday: StoreHours;
    thursday: StoreHours;
    friday: StoreHours;
    saturday: StoreHours;
    sunday: StoreHours;
  };
  paymentMethods: {
    cash: PaymentMethod;
    card: PaymentMethod;
    digitalWallet: PaymentMethod;
    bankTransfer: PaymentMethod;
    giftCard: PaymentMethod;
  };
  profileImage?: string;
}

// State interface
export interface DataState {
  // Core entities
  products: Product[];
  customers: Customer[];
  orders: Order[];
  orderItems: OrderItem[];
  measurements: CustomerMeasurement[];

  // Outlets and staff
  staff: Staff[];
  outlets: Outlet[];

  // Inventory
  warehouses: Warehouse[];

  // App state
  loading: boolean;
  error: string | null;
  settings: Settings;
  searchQuery: string;
  activeFilters: Record<string, any>;
  isDataLoaded: boolean;

  // Current selections
  activeOrderId: string | null;

  // ID counters
  nextProductId: number;
  nextOrderId: number;
  nextCustomerId: number;
}
