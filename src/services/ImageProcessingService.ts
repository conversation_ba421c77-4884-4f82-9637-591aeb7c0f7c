import * as ImageManipulator from 'expo-image-manipulator';

import LoggingService from './LoggingService';

/**
 * --- DEVELOPER ACTION REQUIRED ---
 * You must implement this function to upload files to your server.
 * @param optimizedUri The local URI of the optimized image file.
 * @returns The permanent, remote URL of the uploaded image.
 */
const uploadFileToServer = async (optimizedUri: string): Promise<string> => {
  LoggingService.info('Uploading file...', 'ImageProcessingService', { uri: optimizedUri });

  // In a real app, you would use fetch, axios, etc. to upload the file
  // to your server and return the permanent URL.
  // This is a placeholder that simulates a 2-second upload.
  await new Promise(resolve => setTimeout(resolve, 2000));

  const permanentUrl = `https://cdn.example.com/images/${Date.now()}.jpg`;
  LoggingService.info('Upload complete', 'ImageProcessingService', { url: permanentUrl });
  return permanentUrl;
};

/**
 * Optimizes and uploads a single image file.
 * @param localUri The local URI of the image to process.
 * @returns The permanent, remote URL of the uploaded image.
 */
const optimizeAndUpload = async (localUri: string): Promise<string> => {
  try {
    const optimized = await ImageManipulator.manipulateAsync(
      localUri,
      [{ resize: { width: 1200, height: 1200 } }],
      { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
    );
    const remoteUrl = await uploadFileToServer(optimized.uri);
    return remoteUrl;
  } catch (error) {
    LoggingService.error('Image optimization and upload failed', 'ImageProcessingService', error as Error);
    throw error; // Re-throw to be caught by the calling screen
  }
};

interface ProcessImageAndUpdateParams<T> {
  localUri: string;
  entityId: T;
  updateAction: (id: T, remoteUrl: string) => Promise<void>;
}

/**
 * A generic function to process an image in the background.
 * It optimizes the image, uploads it, and then runs a callback to update the original record.
 * This function is designed to be "fire-and-forget" and not block the UI.
 */
const processImageAndUpdate = async <T>({
  localUri,
  entityId,
  updateAction,
}: ProcessImageAndUpdateParams<T>) => {
  try {
    // 1. Optimize Image
    LoggingService.info('Optimizing image in background...', 'ImageProcessingService', { entityId });
    const optimized = await ImageManipulator.manipulateAsync(
      localUri,
      [{ resize: { width: 1200, height: 1200 } }], // Sensible default for quality
      { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
    );

    // 2. Upload Optimized Image
    const remoteUrl = await uploadFileToServer(optimized.uri);

    // 3. Update the record with the permanent URL
    await updateAction(entityId, remoteUrl);

    LoggingService.info('Background image processing successful', 'ImageProcessingService', {
      entityId,
      remoteUrl,
    });
  } catch (error) {
    LoggingService.error('Background image processing failed', 'ImageProcessingService', error as Error);
    // Here you might want to add logic to retry or notify the user of the background failure.
  }
};

export const ImageProcessingService = {
  processImageAndUpdate,
  optimizeAndUpload,
};