import { useAuth } from '../context/AuthContext';

/**
 * usePermissions - Consolidated hook for role and permission checking
 *
 * Features:
 * - Role-based access control with hierarchy
 * - Permission-based access control
 * - Efficient permission checking without component re-renders
 * - Centralized access control logic
 */
export const usePermissions = () => {
  const { state } = useAuth();

  const roleHierarchy = {
    admin: 3,
    manager: 2,
    user: 1,
  } as const;

  /**
   * Check if user has required role or higher
   */
  const hasRole = (requiredRole: keyof typeof roleHierarchy): boolean => {
    if (!state.isAuthenticated || !state.user) {
      return false;
    }

    const userRoleLevel = roleHierarchy[state.user.role];
    const requiredRoleLevel = roleHierarchy[requiredRole];

    return userRoleLevel >= requiredRoleLevel;
  };

  /**
   * Check if user has specific permission
   */
  const hasPermission = (permission: string): boolean => {
    if (!state.isAuthenticated || !state.user) {
      return false;
    }

    // For now, using the existing permission system
    // This can be enhanced with more granular permission checking
    return state.user.permissions?.includes(permission) || false;
  };

  /**
   * Get user's current role level
   */
  const getRoleLevel = (): number => {
    if (!state.user) return 0;
    return roleHierarchy[state.user.role] || 0;
  };

  /**
   * Check if user is admin
   */
  const isAdmin = (): boolean => hasRole('admin');

  /**
   * Check if user is manager or higher
   */
  const isManagerOrHigher = (): boolean => hasRole('manager');

  /**
   * Check if user is staff or higher
   */
  const isStaffOrHigher = (): boolean => hasRole('user');

  return {
    hasRole,
    hasPermission,
    getRoleLevel,
    isAdmin,
    isManagerOrHigher,
    isStaffOrHigher,
    roleHierarchy,
  };
};
