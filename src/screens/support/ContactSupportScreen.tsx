import React, { useState, useCallback } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import Button from '../../components/ui/Button';
import TextInput from '../../components/ui/TextInput';
import ActionSheet from '../../components/ui/ActionSheet';
import { ActionSheetOption } from '../../types';
import { useTheme } from '../../context/ThemeContext';
import { commonStyles } from '../../theme/commonStyles';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// Data array for the contact methods to avoid repetition
const contactMethods: { icon: PhosphorIconName; title: string; lines: string[] }[] = [
  { icon: 'phone', title: 'Phone Support', lines: ['+****************', 'Mon-Fri, 9AM-6PM EST'] },
  { icon: 'envelope', title: 'Email Support', lines: ['<EMAIL>', 'Response within 24 hours'] },
  { icon: 'chat-circle', title: 'Live Chat', lines: ['Available 24/7', 'Instant response'] },
];

interface ContactSupportScreenProps {
  navigation: { goBack: () => void; navigate: (screen: string) => void };
}

interface ConfirmationState {
  visible: boolean;
  title: string;
  description: string;
  options: ActionSheetOption[];
}

const ContactSupportScreen: React.FC<ContactSupportScreenProps> = ({ navigation }) => {
  const theme = useTheme();

  // Consolidated state for the action sheet
  const [actionSheetState, setActionSheetState] = useState<ConfirmationState>({
    visible: false,
    title: '',
    description: '',
    options: [],
  });

  const hideActionSheet = useCallback(() => setActionSheetState(prev => ({ ...prev, visible: false })), []);

  const showActionSheet = useCallback(
    (title: string, message: string, onConfirm?: () => void) => {
      setActionSheetState({
        visible: true,
        title,
        description: message,
        options: [
          {
            text: 'OK',
            onPress: () => {
              onConfirm?.();
              hideActionSheet();
            },
            style: 'primary',
            isAction: true,
          },
        ],
      });
    },
    [hideActionSheet]
  );

  // Optimized form state management
  const [formData, setFormData] = useState({ subject: '', message: '' });

  const handleInputChange = useCallback((name: 'subject' | 'message', value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleSubmit = useCallback(() => {
    // Corrected validation logic
    if (!formData.subject || !formData.message) {
      showActionSheet('Incomplete Form', 'Please fill in all fields to send a message.');
      return;
    }

    // Simulate sending message
    setTimeout(() => {
      showActionSheet('Message Sent!', "We'll get back to you within 24 hours.", () => {
        setFormData({ subject: '', message: '' }); // Reset form
      });
    }, 1000);
  }, [formData, showActionSheet]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='Contact Support' onBackPress={navigation.goBack} showBack />
      <ScrollView
        style={commonStyles.flex1}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.headerContent}>
            <PhosphorIcon name='headset' size={48} color={theme.colors.primary} />
            <Text variant='headlineSmall' style={[styles.title, { color: theme.colors.onSurface }]}>
              Contact Support
            </Text>
            <Text variant='bodyMedium' style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
              We&apos;re here to help! Send us a message and we&apos;ll respond within 24 hours.
            </Text>
          </View>
        </Surface>

        {/* Render contact methods using .map() */}
        <View style={styles.contactMethods}>
          {contactMethods.map(method => (
            <Surface key={method.title} style={[styles.contactCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
              <View style={styles.contactItem}>
                <PhosphorIcon name={method.icon} size={24} color={theme.colors.primary} />
                <View style={styles.contactInfo}>
                  <Text variant='titleSmall' style={[styles.contactInfoText, { color: theme.colors.onSurface }]}>
                    {method.title}
                  </Text>
                  {method.lines.map(line => (
                    <Text key={line} variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                      {line}
                    </Text>
                  ))}
                </View>
              </View>
            </Surface>
          ))}
        </View>

        <Surface style={[styles.formCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant='titleMedium' style={[styles.formTitle, { color: theme.colors.onSurface }]}>
            Send us a Message
          </Text>
          <TextInput
            label='Subject'
            value={formData.subject}
            onChangeText={text => handleInputChange('subject', text)}
            style={styles.input}
          />
          <TextInput
            label='Message'
            value={formData.message}
            onChangeText={text => handleInputChange('message', text)}
            multiline
            type='textarea'
            style={styles.textArea}
          />
          <Button variant='primary' onPress={handleSubmit} style={styles.submitButton} textColor={theme.colors.onPrimary}>
            Send Message
          </Button>
        </Surface>

        <Surface style={[styles.faqCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.faqContent}>
            <PhosphorIcon name='question' size={24} color={theme.colors.primary} />
            <View style={styles.faqInfo}>
              <Text variant='titleSmall' style={[styles.contactInfoText, { color: theme.colors.onSurface }]}>
                Check our FAQ first
              </Text>
              <Text variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
                You might find the answer in our frequently asked questions.
              </Text>
            </View>
            <Button variant='text' onPress={() => navigation.navigate('HelpFAQ')} textColor={theme.colors.primary}>
              View FAQ
            </Button>
          </View>
        </Surface>
      </ScrollView>

      <ActionSheet
        visible={actionSheetState.visible}
        onDismiss={hideActionSheet}
        title={actionSheetState.title}
        description={actionSheetState.description}
        options={actionSheetState.options}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  contentContainer: { padding: SPACING.lg },
  header: { borderRadius: BORDER_RADIUS.lg, marginBottom: SPACING.lg },
  headerContent: { padding: SPACING.xl, alignItems: 'center' },
  title: { marginTop: SPACING.md, marginBottom: SPACING.sm, fontWeight: '700' },
  subtitle: { textAlign: 'center', lineHeight: 20 },
  contactMethods: { marginBottom: SPACING.lg },
  contactCard: { borderRadius: BORDER_RADIUS.lg, marginBottom: SPACING.md },
  contactItem: { flexDirection: 'row', padding: SPACING.lg, alignItems: 'center' },
  contactInfo: { marginLeft: SPACING.md, flex: 1 },
  contactInfoText: { fontWeight: '600' },
  formCard: { borderRadius: BORDER_RADIUS.lg, padding: SPACING.lg, marginBottom: SPACING.lg },
  formTitle: { marginBottom: SPACING.lg, fontWeight: '600' },
  input: { marginBottom: SPACING.md },
  textArea: { marginBottom: SPACING.lg },
  submitButton: { borderRadius: BORDER_RADIUS.md },
  faqCard: { borderRadius: BORDER_RADIUS.lg },
  faqContent: { flexDirection: 'row', alignItems: 'center', padding: SPACING.lg },
  faqInfo: { flex: 1, marginLeft: SPACING.md, marginRight: SPACING.md },
});

export default ContactSupportScreen;