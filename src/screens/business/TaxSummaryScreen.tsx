// @ts-nocheck
/**
 * TaxSummaryScreen - Full Page Version
 * Converted from TaxSummaryBottomSheet with enhanced layout and navigation
 */

import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Card, Chip, DataTable, Divider, Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { ActionSheetOption } from '../../types';
import { FINANCIAL_CONFIG } from '../../config/constants';
import { useTheme } from '../../context/ThemeContext';
import { FinancialService } from '../../services/financialService';
import { RootStackNavigationProp } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface TaxSummaryData {
  period: { startDate: string; endDate: string };
  totalTaxLiability: number;
  salesTax: { taxableAmount: number; rate: number; amount: number };
  incomeTax: { taxableAmount: number; rate: number; amount: number };
}

const TaxSummaryScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<RootStackNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [taxData, setTaxData] = useState<TaxSummaryData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // ActionSheet state
  const [showErrorActionSheet, setShowErrorActionSheet] = useState(false);
  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState(false);
  const [showScheduleActionSheet, setShowScheduleActionSheet] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const periods = [
    { id: 'week', label: 'This Week' },
    { id: 'month', label: 'This Month' },
    { id: 'quarter', label: 'This Quarter' },
    { id: 'year', label: 'This Year' },
  ];

  useEffect(() => {
    loadTaxData();
  }, [selectedPeriod]);

  const loadTaxData = useCallback(async () => {
    try {
      setLoading(true);
      const endDate = new Date().toISOString();
      const startDate = getStartDate(selectedPeriod);

      const taxSummary = await FinancialService.getTaxSummary(startDate, endDate);

      const data: TaxSummaryData = {
        period: { startDate, endDate },
        totalTaxLiability: taxSummary.totalTaxCollected,
        salesTax: {
          taxableAmount: taxSummary.totalTaxableAmount,
          rate: taxSummary.taxRate,
          amount: taxSummary.totalTaxCollected,
        },
        incomeTax: {
          taxableAmount: taxSummary.totalTaxableAmount * 0.8,
          rate: 0.25,
          amount: taxSummary.totalTaxCollected * 0.3,
        },
      };

      setTaxData(data);
    } catch (error) {
      // Log error silently or use a proper logging service
      setErrorMessage('Failed to load tax data. Please try again.');
      setShowErrorActionSheet(true);
    } finally {
      setLoading(false);
    }
  }, [selectedPeriod]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTaxData();
    setRefreshing(false);
  };

  const getStartDate = (period: string): string => {
    const now = new Date();
    switch (period) {
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      case 'quarter': {
        const quarter = Math.floor(now.getMonth() / 3);
        return new Date(now.getFullYear(), quarter * 3, 1).toISOString();
      }
      case 'year':
        return new Date(now.getFullYear(), 0, 1).toISOString();
      default:
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    }
  };

  const formatCurrency = (amount: number): string => {
    const safeAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
    if (isNaN(safeAmount)) {
      return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}0.00`;
    }
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${safeAmount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value: number): string => {
    const safeValue = typeof value === 'number' ? value : parseFloat(value) || 0;
    if (isNaN(safeValue)) {
      return '0.0%';
    }
    return `${(safeValue * 100).toFixed(1)}%`;
  };

  const getTaxStatus = (amount: number) => {
    if (amount === 0) {
      return { status: 'No Tax Due', color: '#4CAF50', icon: 'check-circle' };
    } else if (amount < 1000) {
      return { status: 'Low Tax Liability', color: '#FF9800', icon: 'alert-circle' };
    } else {
      return { status: 'High Tax Liability', color: '#F44336', icon: 'alert' };
    }
  };

  const exportTaxReport = () => {
    try {
      // Export logic here
      setSuccessMessage('Tax report exported successfully!');
      setShowSuccessActionSheet(true);
    } catch (error) {
      setErrorMessage('Failed to export tax report. Please try again.');
      setShowErrorActionSheet(true);
    }
  };

  const scheduleTaxReminder = () => {
    setShowScheduleActionSheet(true);
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>
          Loading Tax Summary...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Tax Summary'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'download',
            onPress: exportTaxReport,
          },
        ]}
        
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {periods.map(period => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.period,
                  selectedPeriod === period.id && { backgroundColor: theme.colors.primary },
                ]}
                onPress={() => setSelectedPeriod(period.id)}
              >
                <Text
                  style={[
                    styles.periodText,
                    {
                      color:
                        selectedPeriod === period.id
                          ? theme.colors.onPrimary
                          : theme.colors.onBackground,
                    },
                  ]}
                >
                  {period.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {taxData && (
          <>
            {/* Tax Status Card */}
            <Card style={[styles.statusCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
                <View style={styles.statusHeader}>
                  <PhosphorIcon
                    name={getTaxStatus(taxData.totalTaxLiability).icon}
                    size={32}
                    color={getTaxStatus(taxData.totalTaxLiability).color}
                  />
                  <View style={styles.statusInfo}>
                    <Text
                      variant='titleLarge'
                      style={{ color: theme.colors.onSurface, fontWeight: '700' }}
                    >
                      {formatCurrency(taxData.totalTaxLiability)}
                    </Text>
                    <Text variant='bodyMedium' style={{ color: theme.colors.onVariant }}>
                      Total Tax Liability
                    </Text>
                  </View>
                </View>
                <Chip
                  icon={getTaxStatus(taxData.totalTaxLiability).icon}
                  style={{
                    backgroundColor: `${getTaxStatus(taxData.totalTaxLiability).color}20`,
                    alignSelf: 'flex-start',
                    marginTop: 12,
                  }}
                  textStyle={{ color: getTaxStatus(taxData.totalTaxLiability).color }}
                >
                  {getTaxStatus(taxData.totalTaxLiability).status}
                </Chip>
              </Card.Content>
            </Card>

            {/* Sales Tax Details */}
            <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
                <View style={styles.taxHeader}>
                  <PhosphorIcon name='cash-register' size={24} color='#2196F3' />
                  <Text
                    variant='titleMedium'
                    style={{ color: theme.colors.onSurface, marginLeft: 8 }}
                  >
                    Sales Tax
                  </Text>
                </View>

                <DataTable>
                  <DataTable.Row>
                    <DataTable.Cell>Taxable Sales</DataTable.Cell>
                    <DataTable.Cell numeric>
                      {formatCurrency(taxData.salesTax.taxableAmount)}
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row>
                    <DataTable.Cell>Tax Rate</DataTable.Cell>
                    <DataTable.Cell numeric>
                      {formatPercentage(taxData.salesTax.rate)}
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row>
                    <DataTable.Cell>Tax Amount</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: '#2196F3', fontWeight: '600' }}>
                        {formatCurrency(taxData.salesTax.amount)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                </DataTable>
              </Card.Content>
            </Card>

            {/* Income Tax Details */}
            <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
                <View style={styles.taxHeader}>
                  <PhosphorIcon name='chart-line' size={24} color='#4CAF50' />
                  <Text
                    variant='titleMedium'
                    style={{ color: theme.colors.onSurface, marginLeft: 8 }}
                  >
                    Income Tax
                  </Text>
                </View>

                <DataTable>
                  <DataTable.Row>
                    <DataTable.Cell>Taxable Income</DataTable.Cell>
                    <DataTable.Cell numeric>
                      {formatCurrency(taxData.incomeTax.taxableAmount)}
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row>
                    <DataTable.Cell>Tax Rate</DataTable.Cell>
                    <DataTable.Cell numeric>
                      {formatPercentage(taxData.incomeTax.rate)}
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row>
                    <DataTable.Cell>Tax Amount</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: '#4CAF50', fontWeight: '600' }}>
                        {formatCurrency(taxData.incomeTax.amount)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                </DataTable>
              </Card.Content>
            </Card>

            {/* Tax Summary Table */}
            <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
              <Card.Content>
                <Text
                  variant='titleMedium'
                  style={{ color: theme.colors.onPrimaryContainer, marginBottom: 16 }}
                >
                  Tax Summary
                </Text>

                <View style={styles.summaryRow}>
                  <Text variant='bodyMedium' style={{ color: theme.colors.onPrimaryContainer }}>
                    Sales Tax
                  </Text>
                  <Text
                    variant='bodyMedium'
                    style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}
                  >
                    {formatCurrency(taxData.salesTax.amount)}
                  </Text>
                </View>

                <View style={styles.summaryRow}>
                  <Text variant='bodyMedium' style={{ color: theme.colors.onPrimaryContainer }}>
                    Income Tax
                  </Text>
                  <Text
                    variant='bodyMedium'
                    style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}
                  >
                    {formatCurrency(taxData.incomeTax.amount)}
                  </Text>
                </View>

                <Divider
                  style={[
                    styles.divider,
                    { backgroundColor: `${theme.colors.onPrimaryContainer}30` },
                  ]}
                />

                <View style={styles.summaryRow}>
                  <Text
                    variant='titleMedium'
                    style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}
                  >
                    Total Tax Liability
                  </Text>
                  <Text
                    variant='titleMedium'
                    style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}
                  >
                    {formatCurrency(taxData.totalTaxLiability)}
                  </Text>
                </View>
              </Card.Content>
            </Card>
          </>
        )}

        {/* Bottom padding for safe area */}
        <View style={{ height: Math.max(insets.bottom + 20, 40) }} />
      </ScrollView>

      {/* Footer Actions */}
      <View
        style={[
          styles.footer,
          { paddingBottom: insets.bottom, borderTopColor: `${theme.colors.outline}20` },
        ]}
      >
        <View style={styles.actionContainer}>
          <Button
            variant='outline'
            onPress={scheduleTaxReminder}
            icon='bell'
            style={styles.actionButton}
          >
            Set Reminders
          </Button>
          <Button
            variant='primary'
            onPress={exportTaxReport}
            icon='download'
            style={styles.actionButton}
          >
            Export Report
          </Button>
        </View>
      </View>

      <ActionSheet
        visible={showErrorActionSheet}
        onDismiss={() => setShowErrorActionSheet(false)}
        title='Error'
        description={errorMessage}
        options={[{ text: 'OK', onPress: () => setShowErrorActionSheet(false), isAction: true }]}
      />

      <ActionSheet
        visible={showSuccessActionSheet}
        onDismiss={() => setShowSuccessActionSheet(false)}
        title='Success'
        description={successMessage}
        options={[{ text: 'OK', onPress: () => setShowSuccessActionSheet(false), isAction: true }]}
      />

      <ActionSheet
        visible={showScheduleActionSheet}
        onDismiss={() => setShowScheduleActionSheet(false)}
        title='Schedule Tax Reminder'
        description='Set a reminder for tax filing deadline?'
        options={[
          {
            text: 'Schedule',
            onPress: () => {
              // Schedule logic here
              setSuccessMessage('Tax reminder scheduled successfully!');
              setShowSuccessActionSheet(true);
              setShowScheduleActionSheet(false);
            },
            isAction: true,
          },
          { text: 'Cancel', style: 'cancel', isAction: false },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  periodSelector: {
    marginBottom: 16,
  },
  period: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: 16,
    flex: 1,
  },
  taxCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  taxHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default TaxSummaryScreen;
