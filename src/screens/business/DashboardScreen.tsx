import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
// import { useSafeAreaInsets } from 'react-native-safe-area-context'; // Not used

// InfoCard is not used in this component
import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { ActionSheetOption } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useData } from '@/context/DataContext';
import { useFinancial } from '@/context/FinancialContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { useNotifications } from '@/services/notificationService';
import { SPACING } from '@/theme/theme';
import { StatCard } from '@/types';
import { formatCurrency } from '@/utils/currency';

interface DashboardScreenProps {
  navigation: any;
  navigateToTab: (tabName: string) => void;
}

interface DashboardStats {
  todaysSales: number;
  totalOrders: number;
  totalProducts: number;
  totalCustomers: number;
}

interface DashboardCard {
  key: string;
  title: string;
  value: string;
  icon: string;
  iconColor: string;
  onPress: () => void;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation, navigateToTab }) => {
  const theme = useTheme();
  const { state } = useData();
  const { profitLossData, derivedData } = useFinancial();
  const { unreadCount } = useNotifications();
  const { showSuccess } = useToast();
  const { state: authState } = useAuth();
  // const insets = useSafeAreaInsets(); // Not used

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));
      LoggingService.info('Dashboard refreshed', 'SCREEN');
      showSuccess('Dashboard refreshed successfully');
    } catch (error) {
      LoggingService.warn('Failed to refresh dashboard', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [showSuccess]);

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback(
    (type: string): void => {
      switch (type) {
        case 'sales':
          LoggingService.info('Fast navigation to Sales Reports', 'NAVIGATION');
          try {
            // Navigate to Reports screen
            navigation.navigate('Reports');
          } catch (error) {
            LoggingService.error('Failed to navigate to Reports', 'NAVIGATION', error as Error);
          }
          break;
        case 'orders':
          LoggingService.info('Fast navigation to Orders', 'NAVIGATION');
          try {
            navigateToTab('Orders');
          } catch (error) {
            LoggingService.error('Failed to navigate to Orders', 'NAVIGATION', error as Error);
          }
          break;
        case 'products':
          LoggingService.info('Fast navigation to Products', 'NAVIGATION');
          try {
            // For products, use the dedicated Products screen
            navigation.navigate('InventoryItems');
          } catch (error) {
            LoggingService.error('Failed to navigate to Products', 'NAVIGATION', error as Error);
          }
          break;
        case 'customers':
          LoggingService.info('Fast navigation to Customers', 'NAVIGATION');
          try {
            // Navigate to dedicated Customers screen
            navigation.navigate('Customers');
          } catch (error) {
            LoggingService.error('Failed to navigate to Customers', 'NAVIGATION', error as Error);
          }
          break;
        case 'financial':
          LoggingService.info('Fast navigation to Financial', 'NAVIGATION');
          try {
            // Navigate to dedicated Financial screen
            navigation.navigate('Financial');
          } catch (error) {
            LoggingService.error('Failed to navigate to Financial', 'NAVIGATION', error as Error);
          }
          break;
        case 'reports':
          LoggingService.info('Navigating to Advanced Reports', 'NAVIGATION');
          try {
            navigation.navigate('Reports');
          } catch (error) {
            LoggingService.error('Failed to navigate to Reports', 'NAVIGATION', error as Error);
          }
          break;
      }
    },
    [navigateToTab, showSuccess]
  );

  // Simplified stats calculation
  const dashboardStats = useMemo((): DashboardStats => {
    const orders = state.orders || [];
    const products = state.products || [];
    const customers = state.customers || [];

    const todaysSales = orders
      .filter((order: any) => order.date === today && order.status === 'Completed')
      .reduce((sum: number, order: any) => sum + order.total, 0);

    return {
      todaysSales,
      totalOrders: orders.length,
      totalProducts: products.length,
      totalCustomers: customers.length,
    };
  }, [state.orders, state.products, state.customers, today]);

  const dashboardCards = useMemo(
    (): DashboardCard[] => [
      {
        key: 'sales',
        title: "Today's Sales",
        value: formatCurrency(dashboardStats.todaysSales, { decimals: 0 }),
        icon: 'money',
        iconColor: theme.colors.primary,
        onPress: () => handleStatCardPress('sales'),
      },
      {
        key: 'orders',
        title: 'Total Orders',
        value: dashboardStats.totalOrders.toString(),
        icon: 'clipboard-text',
        iconColor: theme.colors.secondary,
        onPress: () => handleStatCardPress('orders'),
      },
      {
        key: 'products',
        title: 'Total Products',
        value: dashboardStats.totalProducts.toString(),
        icon: 'package',
        iconColor: theme.colors.tertiary,
        onPress: () => handleStatCardPress('products'),
      },
      {
        key: 'customers',
        title: 'Total Customers',
        value: dashboardStats.totalCustomers.toString(),
        icon: 'users',
        iconColor: theme.colors.primary,
        onPress: () => handleStatCardPress('customers'),
      },
      {
        key: 'profit',
        title: 'Net Profit',
        value: formatCurrency((profitLossData as any)?.profit?.net ?? 0, { decimals: 0 }),
        icon: 'chart-line',
        iconColor: (profitLossData as any)?.profit?.net >= 0 ? '#10B981' : '#EF4444',
        onPress: () => handleStatCardPress('financial'),
      },
      {
        key: 'expenses',
        title: 'Total Expenses',
        value: formatCurrency(derivedData?.totalExpenses ?? 0, { decimals: 0 }),
        icon: 'receipt',
        iconColor: '#F59E0B',
        onPress: () => handleStatCardPress('financial'),
      },
      {
        key: 'reports',
        title: 'View Reports',
        value: 'Advanced Analytics',
        icon: 'chart-bar',
        iconColor: '#8B5CF6',
        onPress: () => handleStatCardPress('reports'),
      },
      // Quality Control card removed
    ],
    [dashboardStats, theme.colors, profitLossData, derivedData, handleStatCardPress]
  );

  // Show loading state if data is not loaded yet
  if (!state.isDataLoaded) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: theme.colors.background,
            justifyContent: 'center',
            alignItems: 'center',
          },
        ]}
      >
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={{ marginTop: 16, color: theme.colors.onSurfaceVariant }}>
          Loading dashboard...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Dashboard'
        showBack={false}
        showSearch={false}
        actions={[
          {
            icon: 'magnifying-glass',
            onPress: () => {
              try {
                navigation.navigate('Search');
              } catch (error) {
                LoggingService.error('Failed to navigate to Search', 'NAVIGATION', error as Error);
              }
            }
          }
        ]}
        showNotifications={true}
        notificationCount={unreadCount}
        onNotificationPress={() => {
          try {
            navigation.navigate('Notifications');
          } catch (error) {
            LoggingService.error(
              'Failed to navigate to Notifications',
              'NAVIGATION',
              error as Error
            );
          }
        }}
        
      />

      <FlatList
        data={[{ key: 'dashboard-content' }]}
        renderItem={() => (
          <View style={styles.content}>
            <StatCardGroup
              title='Dashboard Stats'
              cards={dashboardCards as StatCard[]}
              columns={2}
              showTitle={false}
            />
          </View>
        )}
        keyExtractor={item => item.key}
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
});

export default DashboardScreen;
