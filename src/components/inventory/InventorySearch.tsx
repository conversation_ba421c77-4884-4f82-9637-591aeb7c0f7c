/**
 * Inventory Search Component
 * Enhanced search and filtering for inventory items
 */

import React, { useState, useMemo, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Searchbar, Text, Button, Card, Menu, IconButton } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { InventoryItem, InventoryFilters } from '../../types/inventory';
import UnitConverter from '../../utils/UnitConverter';

interface InventorySearchProps {
  items: InventoryItem[];
  onFilteredResults: (filteredItems: InventoryItem[]) => void;
  onFiltersChange?: (filters: InventoryFilters) => void;
  placeholder?: string;
  showAdvancedFilters?: boolean;
}

interface AdvancedFilters extends InventoryFilters {
  priceRange?: {
    min: number;
    max: number;
  };
  sortBy?: 'name' | 'category' | 'price' | 'stock';
  sortOrder?: 'asc' | 'desc';
}

const SORT_OPTIONS: { value: 'name' | 'category' | 'price' | 'stock'; label: string }[] = [
  { value: 'name', label: 'Name' },
  { value: 'category', label: 'Category' },
  { value: 'price', label: 'Price' },
  { value: 'stock', label: 'Stock Level' },
];

const InventorySearch: React.FC<InventorySearchProps> = ({
  items,
  onFilteredResults,
  onFiltersChange,
  placeholder = 'Search inventory items...',
  showAdvancedFilters = true,
}) => {
  const theme = useTheme();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [showSortMenu, setShowSortMenu] = useState<boolean>(false);

  const [filters, setFilters] = useState<AdvancedFilters>({
    search: '',
    isActive: true,
    sortBy: 'name',
    sortOrder: 'asc',
  });

  // Apply filters and search
  const filteredItems = useMemo(() => {
    let filtered = [...items];

    // Text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(
        item =>
          item.name.toLowerCase().includes(query) ||
          item.category.toLowerCase().includes(query) ||
          (item.availableUnits &&
            item.availableUnits.some(unit =>
              UnitConverter.getUnitDisplayName(unit).toLowerCase().includes(query)
            ))
      );
    }

    // Active status filter
    if (filters.isActive !== undefined) {
      filtered = filtered.filter(item => item.isActive === filters.isActive);
    }

    // Price range filter
    if (filters.priceRange) {
      const { min, max } = filters.priceRange;
      filtered = filtered.filter(item => item.sellingPrice >= min && item.sellingPrice <= max);
    }

    // Sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: string | number;
        let bValue: string | number;

        switch (filters.sortBy) {
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case 'category':
            aValue = a.category.toLowerCase();
            bValue = b.category.toLowerCase();
            break;
          case 'price':
            aValue = a.sellingPrice;
            bValue = b.sellingPrice;
            break;
          case 'stock':
            aValue = a.minimumStockLevel;
            bValue = b.minimumStockLevel;
            break;
          default:
            return 0;
        }

        if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [items, searchQuery, filters]);

  // Update results when filtered items change
  useEffect(() => {
    onFilteredResults(filteredItems);
  }, [filteredItems, onFilteredResults]);

  // Update filters callback
  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange({
        search: searchQuery,
        isActive: filters.isActive,
      });
    }
  }, [searchQuery, filters, onFiltersChange]);

  // Handle search query change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({ ...prev, search: query }));
  };

  // Handle sort change
  const handleSortChange = (sortBy: 'name' | 'category' | 'price' | 'stock', sortOrder: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sortBy, sortOrder }));
    setShowSortMenu(false);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('');
    setFilters({
      search: '',
      isActive: true,
      sortBy: 'name',
      sortOrder: 'asc',
    });
  };

  // Get active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (searchQuery.trim()) count++;
    if (filters.isActive === false) count++;
    return count;
  }, [searchQuery, filters]);

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchRow}>
        <Searchbar
          placeholder={placeholder}
          value={searchQuery}
          onChangeText={handleSearchChange}
          style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
          inputStyle={{ color: theme.colors.onSurface }}
          iconColor={theme.colors.onSurfaceVariant}
        />

        {showAdvancedFilters && (
          <IconButton
            icon={showFilters ? 'filter-off' : 'filter'}
            size={24}
            onPress={() => setShowFilters(!showFilters)}
            iconColor={activeFilterCount > 0 ? theme.colors.primary : theme.colors.onSurfaceVariant}
            style={[
              styles.filterButton,
              activeFilterCount > 0 && { backgroundColor: `${theme.colors.primary}20` },
            ]}
          />
        )}
      </View>

      {/* Results Summary */}
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryText, { color: theme.colors.onSurfaceVariant }]}>
          {filteredItems.length} of {items.length} items
        </Text>

        {activeFilterCount > 0 && (
          <Button mode='text' onPress={clearFilters} compact textColor={theme.colors.primary}>
            Clear Filters ({activeFilterCount})
          </Button>
        )}
      </View>

      {/* Advanced Filters */}
      {showAdvancedFilters && showFilters && (
        <Card style={styles.filtersCard} mode='outlined'>
          <Card.Content>
            {/* Sort Options */}
            <View style={styles.filterSection}>
              <Text style={[styles.filterLabel, { color: theme.colors.onSurface }]}>Sort By</Text>
              <View style={styles.sortRow}>
                <Menu
                  visible={showSortMenu}
                  onDismiss={() => setShowSortMenu(false)}
                  anchor={
                    <Button
                      mode='outlined'
                      onPress={() => setShowSortMenu(true)}
                      icon='sort'
                      style={styles.sortButton}
                    >
                      {SORT_OPTIONS.find(opt => opt.value === filters.sortBy)?.label || 'Name'}
                    </Button>
                  }
                >
                  {SORT_OPTIONS.map(option => (
                    <Menu.Item
                      key={option.value}
                      onPress={() => handleSortChange(option.value, filters.sortOrder || 'asc')}
                      title={option.label}
                      leadingIcon={filters.sortBy === option.value ? 'check' : undefined}
                    />
                  ))}
                </Menu>

                <IconButton
                  icon={filters.sortOrder === 'asc' ? 'sort-ascending' : 'sort-descending'}
                  size={20}
                  onPress={() =>
                    handleSortChange(
                      filters.sortBy || 'name',
                      filters.sortOrder === 'asc' ? 'desc' : 'asc'
                    )
                  }
                  iconColor={theme.colors.primary}
                />
              </View>
            </View>

            {/* Active Status Toggle */}
            <View style={styles.filterSection}>
              <View style={styles.toggleRow}>
                <Text style={[styles.filterLabel, { color: theme.colors.onSurface }]}>
                  Show Inactive Items
                </Text>
                <Button
                  mode={filters.isActive === false ? 'contained' : 'outlined'}
                  onPress={() =>
                    setFilters(prev => ({
                      ...prev,
                      isActive: prev.isActive === false ? true : false,
                    }))
                  }
                  compact
                >
                  {filters.isActive === false ? 'Showing' : 'Hidden'}
                </Button>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  searchBar: {
    flex: 1,
    elevation: 0,
  },
  filterButton: {
    margin: 0,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 12,
  },
  filtersCard: {
    marginTop: 8,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  sortRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sortButton: {
    flex: 1,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default InventorySearch;